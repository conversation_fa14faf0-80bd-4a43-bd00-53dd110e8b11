# cPanel Deployment Instructions

## Strapi Deployment

1. **Create Node.js App in cPanel:**
   - Go to "Node.js Apps" in cPanel
   - Create new app with Node.js 18.x or higher
   - Set startup file to `server.js`
   - Set application root to `/strapi`

2. **Upload Files:**
   - Upload the entire `strapi` folder to your cPanel file manager
   - Place it in your desired location (e.g., `/home/<USER>/strapi`)

3. **Configure Environment:**
   - Rename `.env.example` to `.env`
   - Update all the REPLACE_WITH_SECURE_* values with actual secure keys
   - Update database configuration if needed

4. **Install Dependencies:**
   ```bash
   cd /path/to/your/strapi
   npm install --production
   ```

5. **Start the App:**
   - Use cPanel Node.js Apps interface to start the application

## Next.js Deployment

### For Static Export:
1. Upload contents of `nextjs-static` folder to your domain's `public_html`
2. Configure your domain to point to the uploaded files

### For Node.js App:
1. Create another Node.js app in cPanel
2. Upload `nextjs` folder contents
3. Set startup file to `server.js` (you may need to create this)
4. Install dependencies and start

## Post-Deployment

1. **Configure Domains:**
   - Set up subdomain for Strapi (e.g., api.yourdomain.com)
   - Point main domain to Next.js

2. **Update Environment Variables:**
   - Update Next.js `.env` with actual Strapi URL
   - Generate and configure API tokens in Strapi admin

3. **Test the Setup:**
   - Access Strapi admin panel
   - Verify Next.js site loads correctly
   - Test contact form functionality

## Security Checklist

- [ ] Changed all default secrets in Strapi
- [ ] Configured HTTPS for both applications
- [ ] Set up proper CORS in Strapi
- [ ] Generated secure API tokens
- [ ] Configured firewall rules if needed

