{"kind": "singleType", "collectionName": "projects_pages", "info": {"singularName": "projects-page", "pluralName": "projects-pages", "displayName": "Projects Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"metadata": {"type": "component", "repeatable": false, "component": "seo.metadata", "required": true}, "banner": {"type": "component", "repeatable": false, "component": "sections.banner", "required": true}}}