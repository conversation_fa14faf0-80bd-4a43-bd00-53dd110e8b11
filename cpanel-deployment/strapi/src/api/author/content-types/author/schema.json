{"kind": "collectionType", "collectionName": "authors", "info": {"singularName": "author", "pluralName": "authors", "displayName": "Author", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"authorName": {"type": "string", "required": true, "unique": true}, "isOrganization": {"type": "boolean", "default": false, "required": true}, "url": {"type": "string", "required": true}}}