{"kind": "singleType", "collectionName": "homepages", "info": {"singularName": "homepage", "pluralName": "homepages", "displayName": "Home Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"metadata": {"type": "component", "repeatable": false, "component": "seo.metadata", "required": true}, "hero": {"type": "component", "repeatable": false, "component": "sections.hero", "required": true}, "featuredProjects": {"type": "component", "repeatable": false, "component": "sections.portfolio", "required": true}, "testimonials": {"type": "component", "repeatable": false, "component": "sections.testimonials", "required": true}, "about": {"type": "component", "repeatable": false, "component": "sections.about", "required": true}, "skills": {"type": "component", "repeatable": false, "component": "sections.skills", "required": true}, "faq": {"type": "component", "repeatable": false, "component": "sections.faq", "required": true}, "latestPosts": {"type": "component", "repeatable": false, "component": "sections.posts", "required": true}, "useCaseSpecificContent": {"type": "dynamiczone", "components": ["sections.services", "sections.experience"], "required": true, "min": 1, "max": 1}}}