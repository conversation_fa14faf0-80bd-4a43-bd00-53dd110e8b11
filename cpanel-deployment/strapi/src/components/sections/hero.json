{"collectionName": "components_sections_heroes", "info": {"displayName": "Hero", "description": ""}, "options": {}, "attributes": {"greeting": {"type": "string"}, "headline": {"type": "string", "required": true}, "supportiveText": {"type": "text", "required": true}, "primaryButton": {"type": "component", "repeatable": false, "component": "basic.button"}, "secondaryButton": {"type": "component", "repeatable": false, "component": "basic.button"}}}