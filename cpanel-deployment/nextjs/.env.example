# Production Environment Variables for Next.js
# Copy this file to .env.local and update with your production values

# Public URLs (update with your actual domains)
NEXT_PUBLIC_STRAPI="https://api.yourdomain.com"
NEXT_PUBLIC_WEBSITE="https://yourdomain.com"

# Encoded Contact Information
# Use Base64 encoding for your email and phone
# Example: echo -n "<EMAIL>" | base64
NEXT_PUBLIC_EMAIL_ENCODED="your_base64_encoded_email"
NEXT_PUBLIC_TELEPHONE_ENCODED="your_base64_encoded_phone"

# API Tokens (get these from your Strapi admin panel)
STRAPI_READ_ONLY_TOKEN="your_production_read_only_token"
STRAPI_FORM_TOKEN="your_production_form_token"

# Optional: Analytics
# NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"
# NEXT_PUBLIC_GTM_ID="GTM-XXXXXXX"

# Optional: SEO
# NEXT_PUBLIC_SITE_NAME="Your Portfolio"
# NEXT_PUBLIC_SITE_DESCRIPTION="Your portfolio description"
