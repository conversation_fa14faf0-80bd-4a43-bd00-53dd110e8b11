[{"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/blog/[slug]/page.jsx": "1", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/blog/page.jsx": "2", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/contact/page.jsx": "3", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/layout.jsx": "4", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/not-found.jsx": "5", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/page.jsx": "6", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/privacy-policy/page.jsx": "7", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/projects/[slug]/page.jsx": "8", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/projects/page.jsx": "9", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/sitemap.js": "10", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/About.jsx": "11", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/AnimatedGradient.jsx": "12", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Announcement.jsx": "13", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BackTo.jsx": "14", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Banner.jsx": "15", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnLight.jsx": "16", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnPrimary.jsx": "17", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnSecondary.jsx": "18", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnToggle.jsx": "19", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/CallToAction.jsx": "20", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/CarouselArrowButtons.jsx": "21", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/CarouselDotButton.jsx": "22", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Chart.jsx": "23", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ChartCSR.jsx": "24", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ChartSSR.jsx": "25", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ContactLink.jsx": "26", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Experience.jsx": "27", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ExperienceEntry.jsx": "28", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ExperienceList.jsx": "29", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Faq.jsx": "30", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FaqEntry.jsx": "31", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FaqList.jsx": "32", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FeaturedProjects.jsx": "33", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Footer.jsx": "34", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterCopyright.jsx": "35", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterNavigation.jsx": "36", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Form.jsx": "37", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Header.jsx": "38", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Hero.jsx": "39", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/LatestPosts.jsx": "40", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/NoSSRWrapper.jsx": "41", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/PageTransition.jsx": "42", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/PostEntry.jsx": "43", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/PostList.jsx": "44", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ProjectCarousel.jsx": "45", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ProjectEntry.jsx": "46", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ProjectGrid.jsx": "47", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/SectionHeader.jsx": "48", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ServiceEntry.jsx": "49", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ServiceList.jsx": "50", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Services.jsx": "51", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ShapeDivider.jsx": "52", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Skills.jsx": "53", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/SocialShare.jsx": "54", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/TestimonialEntry.jsx": "55", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/TestimonialList.jsx": "56", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Testimonials.jsx": "57", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Wrapper.jsx": "58", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/actions.js": "59", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/api.js": "60", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/gradient.js": "61", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/schemas.js": "62", "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/utils.js": "63"}, {"size": 9306, "mtime": 1757574862994, "results": "64", "hashOfConfig": "65"}, {"size": 5536, "mtime": 1757574862994, "results": "66", "hashOfConfig": "65"}, {"size": 8665, "mtime": 1757574862994, "results": "67", "hashOfConfig": "65"}, {"size": 3001, "mtime": 1757574862994, "results": "68", "hashOfConfig": "65"}, {"size": 4630, "mtime": 1757574862994, "results": "69", "hashOfConfig": "65"}, {"size": 6115, "mtime": 1757574862994, "results": "70", "hashOfConfig": "65"}, {"size": 5129, "mtime": 1757574862994, "results": "71", "hashOfConfig": "65"}, {"size": 10442, "mtime": 1757574862994, "results": "72", "hashOfConfig": "65"}, {"size": 5464, "mtime": 1757574862994, "results": "73", "hashOfConfig": "65"}, {"size": 1690, "mtime": 1757574862994, "results": "74", "hashOfConfig": "65"}, {"size": 1839, "mtime": 1757574862994, "results": "75", "hashOfConfig": "65"}, {"size": 624, "mtime": 1757574862994, "results": "76", "hashOfConfig": "65"}, {"size": 1985, "mtime": 1757574862994, "results": "77", "hashOfConfig": "65"}, {"size": 643, "mtime": 1757574862994, "results": "78", "hashOfConfig": "65"}, {"size": 542, "mtime": 1757574862994, "results": "79", "hashOfConfig": "65"}, {"size": 1754, "mtime": 1757574862994, "results": "80", "hashOfConfig": "65"}, {"size": 1762, "mtime": 1757574862994, "results": "81", "hashOfConfig": "65"}, {"size": 1702, "mtime": 1757574862994, "results": "82", "hashOfConfig": "65"}, {"size": 979, "mtime": 1757574862994, "results": "83", "hashOfConfig": "65"}, {"size": 1312, "mtime": 1757574862994, "results": "84", "hashOfConfig": "65"}, {"size": 2273, "mtime": 1757574862994, "results": "85", "hashOfConfig": "65"}, {"size": 1027, "mtime": 1757574862994, "results": "86", "hashOfConfig": "65"}, {"size": 1102, "mtime": 1757574862994, "results": "87", "hashOfConfig": "65"}, {"size": 2854, "mtime": 1757574862994, "results": "88", "hashOfConfig": "65"}, {"size": 1446, "mtime": 1757574862994, "results": "89", "hashOfConfig": "65"}, {"size": 1658, "mtime": 1757574862994, "results": "90", "hashOfConfig": "65"}, {"size": 623, "mtime": 1757574862994, "results": "91", "hashOfConfig": "65"}, {"size": 2199, "mtime": 1757574862994, "results": "92", "hashOfConfig": "65"}, {"size": 832, "mtime": 1757574862994, "results": "93", "hashOfConfig": "65"}, {"size": 474, "mtime": 1757574862994, "results": "94", "hashOfConfig": "65"}, {"size": 842, "mtime": 1757574862994, "results": "95", "hashOfConfig": "65"}, {"size": 307, "mtime": 1757574862994, "results": "96", "hashOfConfig": "65"}, {"size": 1328, "mtime": 1757574862994, "results": "97", "hashOfConfig": "65"}, {"size": 9950, "mtime": 1757574862994, "results": "98", "hashOfConfig": "65"}, {"size": 796, "mtime": 1757574862994, "results": "99", "hashOfConfig": "65"}, {"size": 2116, "mtime": 1757574862994, "results": "100", "hashOfConfig": "65"}, {"size": 7440, "mtime": 1757574862994, "results": "101", "hashOfConfig": "65"}, {"size": 5355, "mtime": 1757574862994, "results": "102", "hashOfConfig": "65"}, {"size": 2037, "mtime": 1757574862994, "results": "103", "hashOfConfig": "65"}, {"size": 1251, "mtime": 1757574862994, "results": "104", "hashOfConfig": "65"}, {"size": 153, "mtime": 1757574862994, "results": "105", "hashOfConfig": "65"}, {"size": 0, "mtime": 1757574862994, "results": "106", "hashOfConfig": "65"}, {"size": 1672, "mtime": 1757574862994, "results": "107", "hashOfConfig": "65"}, {"size": 433, "mtime": 1757574862994, "results": "108", "hashOfConfig": "65"}, {"size": 949, "mtime": 1757574862994, "results": "109", "hashOfConfig": "65"}, {"size": 1859, "mtime": 1757574862994, "results": "110", "hashOfConfig": "65"}, {"size": 714, "mtime": 1757574862994, "results": "111", "hashOfConfig": "65"}, {"size": 361, "mtime": 1757574862994, "results": "112", "hashOfConfig": "65"}, {"size": 363, "mtime": 1757574862994, "results": "113", "hashOfConfig": "65"}, {"size": 1087, "mtime": 1757574862994, "results": "114", "hashOfConfig": "65"}, {"size": 623, "mtime": 1757574862994, "results": "115", "hashOfConfig": "65"}, {"size": 490, "mtime": 1757574862994, "results": "116", "hashOfConfig": "65"}, {"size": 823, "mtime": 1757574862994, "results": "117", "hashOfConfig": "65"}, {"size": 3521, "mtime": 1757574862994, "results": "118", "hashOfConfig": "65"}, {"size": 606, "mtime": 1757574862994, "results": "119", "hashOfConfig": "65"}, {"size": 2658, "mtime": 1757574862994, "results": "120", "hashOfConfig": "65"}, {"size": 636, "mtime": 1757574862994, "results": "121", "hashOfConfig": "65"}, {"size": 112, "mtime": 1757574862994, "results": "122", "hashOfConfig": "65"}, {"size": 1877, "mtime": 1757574862994, "results": "123", "hashOfConfig": "65"}, {"size": 12874, "mtime": 1757574862994, "results": "124", "hashOfConfig": "65"}, {"size": 35075, "mtime": 1757574862994, "results": "125", "hashOfConfig": "65"}, {"size": 7713, "mtime": 1757574862994, "results": "126", "hashOfConfig": "65"}, {"size": 1113, "mtime": 1757574862994, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "j<PERSON><PERSON><PERSON>", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/blog/[slug]/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/blog/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/contact/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/layout.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/not-found.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/privacy-policy/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/projects/[slug]/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/projects/page.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/sitemap.js", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/About.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/AnimatedGradient.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Announcement.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BackTo.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Banner.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnLight.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnPrimary.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnSecondary.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnToggle.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/CallToAction.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/CarouselArrowButtons.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/CarouselDotButton.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Chart.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ChartCSR.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ChartSSR.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ContactLink.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Experience.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ExperienceEntry.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ExperienceList.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Faq.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FaqEntry.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FaqList.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FeaturedProjects.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Footer.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterCopyright.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterNavigation.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Form.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Header.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Hero.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/LatestPosts.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/NoSSRWrapper.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/PageTransition.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/PostEntry.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/PostList.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ProjectCarousel.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ProjectEntry.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ProjectGrid.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/SectionHeader.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ServiceEntry.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ServiceList.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Services.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ShapeDivider.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Skills.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/SocialShare.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/TestimonialEntry.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/TestimonialList.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Testimonials.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Wrapper.jsx", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/actions.js", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/api.js", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/gradient.js", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/schemas.js", [], [], "/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/lib/utils.js", [], []]