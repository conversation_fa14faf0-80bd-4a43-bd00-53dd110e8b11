exports.id=269,exports.ids=[269],exports.modules={11088:(a,b,c)=>{function d(a){return a&&a.default||a}a.exports=global.DOMPurify=global.DOMPurify||("undefined"!=typeof window?d(c(75392)):function(){let a=d(c(75392)),{JSDOM:b}=d(c(32325)),{window:e}=new b("<!DOCTYPE html>");return a(e)}())},56572:(a,b,c)=>{"use strict";function d(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}c.d(b,{Dz:()=>am,xI:()=>ao});let e=d(),f={exec:()=>null};function g(a,b=""){let c="string"==typeof a?a:a.source,d={replace:(a,b)=>{let e="string"==typeof b?b:b.source;return e=e.replace(h.caret,"$1"),c=c.replace(a,e),d},getRegex:()=>new RegExp(c,b)};return d}let h={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:a=>RegExp(`^( {0,3}${a})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:a=>RegExp(`^ {0,${Math.min(3,a-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:a=>RegExp(`^ {0,${Math.min(3,a-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:a=>RegExp(`^ {0,${Math.min(3,a-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:a=>RegExp(`^ {0,${Math.min(3,a-1)}}#`),htmlBeginRegex:a=>RegExp(`^ {0,${Math.min(3,a-1)}}<(?:[a-z].*>|!--)`,"i")},i=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,j=/(?:[*+-]|\d{1,9}[.)])/,k=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,l=g(k).replace(/bull/g,j).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),m=g(k).replace(/bull/g,j).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),n=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,o=/(?!\s*\])(?:\\.|[^\[\]\\])+/,p=g(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",o).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),q=g(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,j).getRegex(),r="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",s=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,t=g("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",s).replace("tag",r).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),u=g(n).replace("hr",i).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",r).getRegex(),v={blockquote:g(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",u).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:p,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:i,html:t,lheading:l,list:q,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:u,table:f,text:/^[^\n]+/},w=g("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",i).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",r).getRegex(),x={...v,lheading:m,table:w,paragraph:g(n).replace("hr",i).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",w).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",r).getRegex()},y={...v,html:g("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",s).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:f,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:g(n).replace("hr",i).replace("heading"," *#{1,6} *[^\n]").replace("lheading",l).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},z=/^( {2,}|\\)\n(?!\s*$)/,A=/[\p{P}\p{S}]/u,B=/[\s\p{P}\p{S}]/u,C=/[^\s\p{P}\p{S}]/u,D=g(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,B).getRegex(),E=/(?!~)[\p{P}\p{S}]/u,F=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,G=g(F,"u").replace(/punct/g,A).getRegex(),H=g(F,"u").replace(/punct/g,E).getRegex(),I="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",J=g(I,"gu").replace(/notPunctSpace/g,C).replace(/punctSpace/g,B).replace(/punct/g,A).getRegex(),K=g(I,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,E).getRegex(),L=g("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,C).replace(/punctSpace/g,B).replace(/punct/g,A).getRegex(),M=g(/\\(punct)/,"gu").replace(/punct/g,A).getRegex(),N=g(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),O=g(s).replace("(?:--\x3e|$)","--\x3e").getRegex(),P=g("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",O).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Q=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,R=g(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Q).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),S=g(/^!?\[(label)\]\[(ref)\]/).replace("label",Q).replace("ref",o).getRegex(),T=g(/^!?\[(ref)\](?:\[\])?/).replace("ref",o).getRegex(),U=g("reflink|nolink(?!\\()","g").replace("reflink",S).replace("nolink",T).getRegex(),V={_backpedal:f,anyPunctuation:M,autolink:N,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:z,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:f,emStrongLDelim:G,emStrongRDelimAst:J,emStrongRDelimUnd:L,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:R,nolink:T,punctuation:D,reflink:S,reflinkSearch:U,tag:P,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:f},W={...V,link:g(/^!?\[(label)\]\((.*?)\)/).replace("label",Q).getRegex(),reflink:g(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Q).getRegex()},X={...V,emStrongRDelimAst:K,emStrongLDelim:H,url:g(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Y={...X,br:g(z).replace("{2,}","*").getRegex(),text:g(X.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Z={normal:v,gfm:x,pedantic:y},$={normal:V,gfm:X,breaks:Y,pedantic:W},_={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},aa=a=>_[a];function ab(a,b){if(b){if(h.escapeTest.test(a))return a.replace(h.escapeReplace,aa)}else if(h.escapeTestNoEncode.test(a))return a.replace(h.escapeReplaceNoEncode,aa);return a}function ac(a){try{a=encodeURI(a).replace(h.percentDecode,"%")}catch{return null}return a}function ad(a,b){let c=a.replace(h.findPipe,(a,b,c)=>{let d=!1,e=b;for(;--e>=0&&"\\"===c[e];)d=!d;return d?"|":" |"}).split(h.splitPipe),d=0;if(c[0].trim()||c.shift(),c.length>0&&!c.at(-1)?.trim()&&c.pop(),b)if(c.length>b)c.splice(b);else for(;c.length<b;)c.push("");for(;d<c.length;d++)c[d]=c[d].trim().replace(h.slashPipe,"|");return c}function ae(a,b,c){let d=a.length;if(0===d)return"";let e=0;for(;e<d;)if(a.charAt(d-e-1)===b)e++;else break;return a.slice(0,d-e)}function af(a,b,c,d,e){let f=b.href,g=b.title||null,h=a[1].replace(e.other.outputLinkReplace,"$1");if("!"!==a[0].charAt(0)){d.state.inLink=!0;let a={type:"link",raw:c,href:f,title:g,text:h,tokens:d.inlineTokens(h)};return d.state.inLink=!1,a}return{type:"image",raw:c,href:f,title:g,text:h}}class ag{options;rules;lexer;constructor(a){this.options=a||e}space(a){let b=this.rules.block.newline.exec(a);if(b&&b[0].length>0)return{type:"space",raw:b[0]}}code(a){let b=this.rules.block.code.exec(a);if(b){let a=b[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:b[0],codeBlockStyle:"indented",text:this.options.pedantic?a:ae(a,"\n")}}}fences(a){let b=this.rules.block.fences.exec(a);if(b){let a=b[0],c=function(a,b,c){let d=a.match(c.other.indentCodeCompensation);if(null===d)return b;let e=d[1];return b.split("\n").map(a=>{let b=a.match(c.other.beginningSpace);if(null===b)return a;let[d]=b;return d.length>=e.length?a.slice(e.length):a}).join("\n")}(a,b[3]||"",this.rules);return{type:"code",raw:a,lang:b[2]?b[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):b[2],text:c}}}heading(a){let b=this.rules.block.heading.exec(a);if(b){let a=b[2].trim();if(this.rules.other.endingHash.test(a)){let b=ae(a,"#");this.options.pedantic?a=b.trim():(!b||this.rules.other.endingSpaceChar.test(b))&&(a=b.trim())}return{type:"heading",raw:b[0],depth:b[1].length,text:a,tokens:this.lexer.inline(a)}}}hr(a){let b=this.rules.block.hr.exec(a);if(b)return{type:"hr",raw:ae(b[0],"\n")}}blockquote(a){let b=this.rules.block.blockquote.exec(a);if(b){let a=ae(b[0],"\n").split("\n"),c="",d="",e=[];for(;a.length>0;){let b,f=!1,g=[];for(b=0;b<a.length;b++)if(this.rules.other.blockquoteStart.test(a[b]))g.push(a[b]),f=!0;else if(f)break;else g.push(a[b]);a=a.slice(b);let h=g.join("\n"),i=h.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");c=c?`${c}
${h}`:h,d=d?`${d}
${i}`:i;let j=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(i,e,!0),this.lexer.state.top=j,0===a.length)break;let k=e.at(-1);if(k?.type==="code")break;if(k?.type==="blockquote"){let b=k.raw+"\n"+a.join("\n"),f=this.blockquote(b);e[e.length-1]=f,c=c.substring(0,c.length-k.raw.length)+f.raw,d=d.substring(0,d.length-k.text.length)+f.text;break}if(k?.type==="list"){let b=k.raw+"\n"+a.join("\n"),f=this.list(b);e[e.length-1]=f,c=c.substring(0,c.length-k.raw.length)+f.raw,d=d.substring(0,d.length-k.raw.length)+f.raw,a=b.substring(e.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:c,tokens:e,text:d}}}list(a){let b=this.rules.block.list.exec(a);if(b){let c=b[1].trim(),d=c.length>1,e={type:"list",raw:"",ordered:d,start:d?+c.slice(0,-1):"",loose:!1,items:[]};c=d?`\\d{1,9}\\${c.slice(-1)}`:`\\${c}`,this.options.pedantic&&(c=d?c:"[*+-]");let f=this.rules.other.listItemRegex(c),g=!1;for(;a;){let c,d=!1,h="",i="";if(!(b=f.exec(a))||this.rules.block.hr.test(a))break;h=b[0],a=a.substring(h.length);let j=b[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,a=>" ".repeat(3*a.length)),k=a.split("\n",1)[0],l=!j.trim(),m=0;if(this.options.pedantic?(m=2,i=j.trimStart()):l?m=b[1].length+1:(m=(m=b[2].search(this.rules.other.nonSpaceChar))>4?1:m,i=j.slice(m),m+=b[1].length),l&&this.rules.other.blankLine.test(k)&&(h+=k+"\n",a=a.substring(k.length+1),d=!0),!d){let b=this.rules.other.nextBulletRegex(m),c=this.rules.other.hrRegex(m),d=this.rules.other.fencesBeginRegex(m),e=this.rules.other.headingBeginRegex(m),f=this.rules.other.htmlBeginRegex(m);for(;a;){let g,n=a.split("\n",1)[0];if(k=n,g=this.options.pedantic?k=k.replace(this.rules.other.listReplaceNesting,"  "):k.replace(this.rules.other.tabCharGlobal,"    "),d.test(k)||e.test(k)||f.test(k)||b.test(k)||c.test(k))break;if(g.search(this.rules.other.nonSpaceChar)>=m||!k.trim())i+="\n"+g.slice(m);else{if(l||j.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||d.test(j)||e.test(j)||c.test(j))break;i+="\n"+k}l||k.trim()||(l=!0),h+=n+"\n",a=a.substring(n.length+1),j=g.slice(m)}}!e.loose&&(g?e.loose=!0:this.rules.other.doubleBlankLine.test(h)&&(g=!0));let n=null;this.options.gfm&&(n=this.rules.other.listIsTask.exec(i))&&(c="[ ] "!==n[0],i=i.replace(this.rules.other.listReplaceTask,"")),e.items.push({type:"list_item",raw:h,task:!!n,checked:c,loose:!1,text:i,tokens:[]}),e.raw+=h}let h=e.items.at(-1);if(!h)return;h.raw=h.raw.trimEnd(),h.text=h.text.trimEnd(),e.raw=e.raw.trimEnd();for(let a=0;a<e.items.length;a++)if(this.lexer.state.top=!1,e.items[a].tokens=this.lexer.blockTokens(e.items[a].text,[]),!e.loose){let b=e.items[a].tokens.filter(a=>"space"===a.type);e.loose=b.length>0&&b.some(a=>this.rules.other.anyLine.test(a.raw))}if(e.loose)for(let a=0;a<e.items.length;a++)e.items[a].loose=!0;return e}}html(a){let b=this.rules.block.html.exec(a);if(b)return{type:"html",block:!0,raw:b[0],pre:"pre"===b[1]||"script"===b[1]||"style"===b[1],text:b[0]}}def(a){let b=this.rules.block.def.exec(a);if(b){let a=b[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),c=b[2]?b[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",d=b[3]?b[3].substring(1,b[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):b[3];return{type:"def",tag:a,raw:b[0],href:c,title:d}}}table(a){let b=this.rules.block.table.exec(a);if(!b||!this.rules.other.tableDelimiter.test(b[2]))return;let c=ad(b[1]),d=b[2].replace(this.rules.other.tableAlignChars,"").split("|"),e=b[3]?.trim()?b[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],f={type:"table",raw:b[0],header:[],align:[],rows:[]};if(c.length===d.length){for(let a of d)this.rules.other.tableAlignRight.test(a)?f.align.push("right"):this.rules.other.tableAlignCenter.test(a)?f.align.push("center"):this.rules.other.tableAlignLeft.test(a)?f.align.push("left"):f.align.push(null);for(let a=0;a<c.length;a++)f.header.push({text:c[a],tokens:this.lexer.inline(c[a]),header:!0,align:f.align[a]});for(let a of e)f.rows.push(ad(a,f.header.length).map((a,b)=>({text:a,tokens:this.lexer.inline(a),header:!1,align:f.align[b]})));return f}}lheading(a){let b=this.rules.block.lheading.exec(a);if(b)return{type:"heading",raw:b[0],depth:"="===b[2].charAt(0)?1:2,text:b[1],tokens:this.lexer.inline(b[1])}}paragraph(a){let b=this.rules.block.paragraph.exec(a);if(b){let a="\n"===b[1].charAt(b[1].length-1)?b[1].slice(0,-1):b[1];return{type:"paragraph",raw:b[0],text:a,tokens:this.lexer.inline(a)}}}text(a){let b=this.rules.block.text.exec(a);if(b)return{type:"text",raw:b[0],text:b[0],tokens:this.lexer.inline(b[0])}}escape(a){let b=this.rules.inline.escape.exec(a);if(b)return{type:"escape",raw:b[0],text:b[1]}}tag(a){let b=this.rules.inline.tag.exec(a);if(b)return!this.lexer.state.inLink&&this.rules.other.startATag.test(b[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(b[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(b[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(b[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:b[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:b[0]}}link(a){let b=this.rules.inline.link.exec(a);if(b){let a=b[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(a)){if(!this.rules.other.endAngleBracket.test(a))return;let b=ae(a.slice(0,-1),"\\");if((a.length-b.length)%2==0)return}else{let a=function(a,b){if(-1===a.indexOf(")"))return -1;let c=0;for(let d=0;d<a.length;d++)if("\\"===a[d])d++;else if("("===a[d])c++;else if(a[d]===b[1]&&--c<0)return d;return -1}(b[2],"()");if(a>-1){let c=(0===b[0].indexOf("!")?5:4)+b[1].length+a;b[2]=b[2].substring(0,a),b[0]=b[0].substring(0,c).trim(),b[3]=""}}let c=b[2],d="";if(this.options.pedantic){let a=this.rules.other.pedanticHrefTitle.exec(c);a&&(c=a[1],d=a[3])}else d=b[3]?b[3].slice(1,-1):"";return c=c.trim(),this.rules.other.startAngleBracket.test(c)&&(c=this.options.pedantic&&!this.rules.other.endAngleBracket.test(a)?c.slice(1):c.slice(1,-1)),af(b,{href:c?c.replace(this.rules.inline.anyPunctuation,"$1"):c,title:d?d.replace(this.rules.inline.anyPunctuation,"$1"):d},b[0],this.lexer,this.rules)}}reflink(a,b){let c;if((c=this.rules.inline.reflink.exec(a))||(c=this.rules.inline.nolink.exec(a))){let a=b[(c[2]||c[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!a){let a=c[0].charAt(0);return{type:"text",raw:a,text:a}}return af(c,a,c[0],this.lexer,this.rules)}}emStrong(a,b,c=""){let d=this.rules.inline.emStrongLDelim.exec(a);if(!(!d||d[3]&&c.match(this.rules.other.unicodeAlphaNumeric))&&(!(d[1]||d[2])||!c||this.rules.inline.punctuation.exec(c))){let c=[...d[0]].length-1,e,f,g=c,h=0,i="*"===d[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(i.lastIndex=0,b=b.slice(-1*a.length+c);null!=(d=i.exec(b));){if(!(e=d[1]||d[2]||d[3]||d[4]||d[5]||d[6]))continue;if(f=[...e].length,d[3]||d[4]){g+=f;continue}if((d[5]||d[6])&&c%3&&!((c+f)%3)){h+=f;continue}if((g-=f)>0)continue;f=Math.min(f,f+g+h);let b=[...d[0]][0].length,i=a.slice(0,c+d.index+b+f);if(Math.min(c,f)%2){let a=i.slice(1,-1);return{type:"em",raw:i,text:a,tokens:this.lexer.inlineTokens(a)}}let j=i.slice(2,-2);return{type:"strong",raw:i,text:j,tokens:this.lexer.inlineTokens(j)}}}}codespan(a){let b=this.rules.inline.code.exec(a);if(b){let a=b[2].replace(this.rules.other.newLineCharGlobal," "),c=this.rules.other.nonSpaceChar.test(a),d=this.rules.other.startingSpaceChar.test(a)&&this.rules.other.endingSpaceChar.test(a);return c&&d&&(a=a.substring(1,a.length-1)),{type:"codespan",raw:b[0],text:a}}}br(a){let b=this.rules.inline.br.exec(a);if(b)return{type:"br",raw:b[0]}}del(a){let b=this.rules.inline.del.exec(a);if(b)return{type:"del",raw:b[0],text:b[2],tokens:this.lexer.inlineTokens(b[2])}}autolink(a){let b=this.rules.inline.autolink.exec(a);if(b){let a,c;return c="@"===b[2]?"mailto:"+(a=b[1]):a=b[1],{type:"link",raw:b[0],text:a,href:c,tokens:[{type:"text",raw:a,text:a}]}}}url(a){let b;if(b=this.rules.inline.url.exec(a)){let a,c;if("@"===b[2])c="mailto:"+(a=b[0]);else{let d;do d=b[0],b[0]=this.rules.inline._backpedal.exec(b[0])?.[0]??"";while(d!==b[0]);a=b[0],c="www."===b[1]?"http://"+b[0]:b[0]}return{type:"link",raw:b[0],text:a,href:c,tokens:[{type:"text",raw:a,text:a}]}}}inlineText(a){let b=this.rules.inline.text.exec(a);if(b){let a=this.lexer.state.inRawBlock;return{type:"text",raw:b[0],text:b[0],escaped:a}}}}class ah{tokens;options;state;tokenizer;inlineQueue;constructor(a){this.tokens=[],this.tokens.links=Object.create(null),this.options=a||e,this.options.tokenizer=this.options.tokenizer||new ag,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let b={other:h,block:Z.normal,inline:$.normal};this.options.pedantic?(b.block=Z.pedantic,b.inline=$.pedantic):this.options.gfm&&(b.block=Z.gfm,this.options.breaks?b.inline=$.breaks:b.inline=$.gfm),this.tokenizer.rules=b}static get rules(){return{block:Z,inline:$}}static lex(a,b){return new ah(b).lex(a)}static lexInline(a,b){return new ah(b).inlineTokens(a)}lex(a){a=a.replace(h.carriageReturn,"\n"),this.blockTokens(a,this.tokens);for(let a=0;a<this.inlineQueue.length;a++){let b=this.inlineQueue[a];this.inlineTokens(b.src,b.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(a,b=[],c=!1){for(this.options.pedantic&&(a=a.replace(h.tabCharGlobal,"    ").replace(h.spaceLine,""));a;){let d;if(this.options.extensions?.block?.some(c=>!!(d=c.call({lexer:this},a,b))&&(a=a.substring(d.raw.length),b.push(d),!0)))continue;if(d=this.tokenizer.space(a)){a=a.substring(d.raw.length);let c=b.at(-1);1===d.raw.length&&void 0!==c?c.raw+="\n":b.push(d);continue}if(d=this.tokenizer.code(a)){a=a.substring(d.raw.length);let c=b.at(-1);c?.type==="paragraph"||c?.type==="text"?(c.raw+="\n"+d.raw,c.text+="\n"+d.text,this.inlineQueue.at(-1).src=c.text):b.push(d);continue}if((d=this.tokenizer.fences(a))||(d=this.tokenizer.heading(a))||(d=this.tokenizer.hr(a))||(d=this.tokenizer.blockquote(a))||(d=this.tokenizer.list(a))||(d=this.tokenizer.html(a))){a=a.substring(d.raw.length),b.push(d);continue}if(d=this.tokenizer.def(a)){a=a.substring(d.raw.length);let c=b.at(-1);c?.type==="paragraph"||c?.type==="text"?(c.raw+="\n"+d.raw,c.text+="\n"+d.raw,this.inlineQueue.at(-1).src=c.text):this.tokens.links[d.tag]||(this.tokens.links[d.tag]={href:d.href,title:d.title});continue}if((d=this.tokenizer.table(a))||(d=this.tokenizer.lheading(a))){a=a.substring(d.raw.length),b.push(d);continue}let e=a;if(this.options.extensions?.startBlock){let b,c=1/0,d=a.slice(1);this.options.extensions.startBlock.forEach(a=>{"number"==typeof(b=a.call({lexer:this},d))&&b>=0&&(c=Math.min(c,b))}),c<1/0&&c>=0&&(e=a.substring(0,c+1))}if(this.state.top&&(d=this.tokenizer.paragraph(e))){let f=b.at(-1);c&&f?.type==="paragraph"?(f.raw+="\n"+d.raw,f.text+="\n"+d.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=f.text):b.push(d),c=e.length!==a.length,a=a.substring(d.raw.length);continue}if(d=this.tokenizer.text(a)){a=a.substring(d.raw.length);let c=b.at(-1);c?.type==="text"?(c.raw+="\n"+d.raw,c.text+="\n"+d.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=c.text):b.push(d);continue}if(a){let b="Infinite loop on byte: "+a.charCodeAt(0);if(this.options.silent){console.error(b);break}throw Error(b)}}return this.state.top=!0,b}inline(a,b=[]){return this.inlineQueue.push({src:a,tokens:b}),b}inlineTokens(a,b=[]){let c=a,d=null;if(this.tokens.links){let a=Object.keys(this.tokens.links);if(a.length>0)for(;null!=(d=this.tokenizer.rules.inline.reflinkSearch.exec(c));)a.includes(d[0].slice(d[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,d.index)+"["+"a".repeat(d[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(d=this.tokenizer.rules.inline.blockSkip.exec(c));)c=c.slice(0,d.index)+"["+"a".repeat(d[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(d=this.tokenizer.rules.inline.anyPunctuation.exec(c));)c=c.slice(0,d.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let e=!1,f="";for(;a;){let d;if(e||(f=""),e=!1,this.options.extensions?.inline?.some(c=>!!(d=c.call({lexer:this},a,b))&&(a=a.substring(d.raw.length),b.push(d),!0)))continue;if((d=this.tokenizer.escape(a))||(d=this.tokenizer.tag(a))||(d=this.tokenizer.link(a))){a=a.substring(d.raw.length),b.push(d);continue}if(d=this.tokenizer.reflink(a,this.tokens.links)){a=a.substring(d.raw.length);let c=b.at(-1);"text"===d.type&&c?.type==="text"?(c.raw+=d.raw,c.text+=d.text):b.push(d);continue}if((d=this.tokenizer.emStrong(a,c,f))||(d=this.tokenizer.codespan(a))||(d=this.tokenizer.br(a))||(d=this.tokenizer.del(a))||(d=this.tokenizer.autolink(a))||!this.state.inLink&&(d=this.tokenizer.url(a))){a=a.substring(d.raw.length),b.push(d);continue}let g=a;if(this.options.extensions?.startInline){let b,c=1/0,d=a.slice(1);this.options.extensions.startInline.forEach(a=>{"number"==typeof(b=a.call({lexer:this},d))&&b>=0&&(c=Math.min(c,b))}),c<1/0&&c>=0&&(g=a.substring(0,c+1))}if(d=this.tokenizer.inlineText(g)){a=a.substring(d.raw.length),"_"!==d.raw.slice(-1)&&(f=d.raw.slice(-1)),e=!0;let c=b.at(-1);c?.type==="text"?(c.raw+=d.raw,c.text+=d.text):b.push(d);continue}if(a){let b="Infinite loop on byte: "+a.charCodeAt(0);if(this.options.silent){console.error(b);break}throw Error(b)}}return b}}class ai{options;parser;constructor(a){this.options=a||e}space(a){return""}code({text:a,lang:b,escaped:c}){let d=(b||"").match(h.notSpaceStart)?.[0],e=a.replace(h.endingNewline,"")+"\n";return d?'<pre><code class="language-'+ab(d)+'">'+(c?e:ab(e,!0))+"</code></pre>\n":"<pre><code>"+(c?e:ab(e,!0))+"</code></pre>\n"}blockquote({tokens:a}){let b=this.parser.parse(a);return`<blockquote>
${b}</blockquote>
`}html({text:a}){return a}heading({tokens:a,depth:b}){return`<h${b}>${this.parser.parseInline(a)}</h${b}>
`}hr(a){return"<hr>\n"}list(a){let b=a.ordered,c=a.start,d="";for(let b=0;b<a.items.length;b++){let c=a.items[b];d+=this.listitem(c)}let e=b?"ol":"ul";return"<"+e+(b&&1!==c?' start="'+c+'"':"")+">\n"+d+"</"+e+">\n"}listitem(a){let b="";if(a.task){let c=this.checkbox({checked:!!a.checked});a.loose?a.tokens[0]?.type==="paragraph"?(a.tokens[0].text=c+" "+a.tokens[0].text,a.tokens[0].tokens&&a.tokens[0].tokens.length>0&&"text"===a.tokens[0].tokens[0].type&&(a.tokens[0].tokens[0].text=c+" "+ab(a.tokens[0].tokens[0].text),a.tokens[0].tokens[0].escaped=!0)):a.tokens.unshift({type:"text",raw:c+" ",text:c+" ",escaped:!0}):b+=c+" "}return b+=this.parser.parse(a.tokens,!!a.loose),`<li>${b}</li>
`}checkbox({checked:a}){return"<input "+(a?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:a}){return`<p>${this.parser.parseInline(a)}</p>
`}table(a){let b="",c="";for(let b=0;b<a.header.length;b++)c+=this.tablecell(a.header[b]);b+=this.tablerow({text:c});let d="";for(let b=0;b<a.rows.length;b++){let e=a.rows[b];c="";for(let a=0;a<e.length;a++)c+=this.tablecell(e[a]);d+=this.tablerow({text:c})}return d&&(d=`<tbody>${d}</tbody>`),"<table>\n<thead>\n"+b+"</thead>\n"+d+"</table>\n"}tablerow({text:a}){return`<tr>
${a}</tr>
`}tablecell(a){let b=this.parser.parseInline(a.tokens),c=a.header?"th":"td";return(a.align?`<${c} align="${a.align}">`:`<${c}>`)+b+`</${c}>
`}strong({tokens:a}){return`<strong>${this.parser.parseInline(a)}</strong>`}em({tokens:a}){return`<em>${this.parser.parseInline(a)}</em>`}codespan({text:a}){return`<code>${ab(a,!0)}</code>`}br(a){return"<br>"}del({tokens:a}){return`<del>${this.parser.parseInline(a)}</del>`}link({href:a,title:b,tokens:c}){let d=this.parser.parseInline(c),e=ac(a);if(null===e)return d;let f='<a href="'+(a=e)+'"';return b&&(f+=' title="'+ab(b)+'"'),f+=">"+d+"</a>"}image({href:a,title:b,text:c}){let d=ac(a);if(null===d)return ab(c);a=d;let e=`<img src="${a}" alt="${c}"`;return b&&(e+=` title="${ab(b)}"`),e+=">"}text(a){return"tokens"in a&&a.tokens?this.parser.parseInline(a.tokens):"escaped"in a&&a.escaped?a.text:ab(a.text)}}class aj{strong({text:a}){return a}em({text:a}){return a}codespan({text:a}){return a}del({text:a}){return a}html({text:a}){return a}text({text:a}){return a}link({text:a}){return""+a}image({text:a}){return""+a}br(){return""}}class ak{options;renderer;textRenderer;constructor(a){this.options=a||e,this.options.renderer=this.options.renderer||new ai,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new aj}static parse(a,b){return new ak(b).parse(a)}static parseInline(a,b){return new ak(b).parseInline(a)}parse(a,b=!0){let c="";for(let d=0;d<a.length;d++){let e=a[d];if(this.options.extensions?.renderers?.[e.type]){let a=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==a||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){c+=a||"";continue}}switch(e.type){case"space":c+=this.renderer.space(e);continue;case"hr":c+=this.renderer.hr(e);continue;case"heading":c+=this.renderer.heading(e);continue;case"code":c+=this.renderer.code(e);continue;case"table":c+=this.renderer.table(e);continue;case"blockquote":c+=this.renderer.blockquote(e);continue;case"list":c+=this.renderer.list(e);continue;case"html":c+=this.renderer.html(e);continue;case"paragraph":c+=this.renderer.paragraph(e);continue;case"text":{let f=e,g=this.renderer.text(f);for(;d+1<a.length&&"text"===a[d+1].type;)f=a[++d],g+="\n"+this.renderer.text(f);b?c+=this.renderer.paragraph({type:"paragraph",raw:g,text:g,tokens:[{type:"text",raw:g,text:g,escaped:!0}]}):c+=g;continue}default:{let a='Token with "'+e.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw Error(a)}}}return c}parseInline(a,b=this.renderer){let c="";for(let d=0;d<a.length;d++){let e=a[d];if(this.options.extensions?.renderers?.[e.type]){let a=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==a||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(e.type)){c+=a||"";continue}}switch(e.type){case"escape":case"text":c+=b.text(e);break;case"html":c+=b.html(e);break;case"link":c+=b.link(e);break;case"image":c+=b.image(e);break;case"strong":c+=b.strong(e);break;case"em":c+=b.em(e);break;case"codespan":c+=b.codespan(e);break;case"br":c+=b.br(e);break;case"del":c+=b.del(e);break;default:{let a='Token with "'+e.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw Error(a)}}}return c}}class al{options;block;constructor(a){this.options=a||e}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(a){return a}postprocess(a){return a}processAllTokens(a){return a}provideLexer(){return this.block?ah.lex:ah.lexInline}provideParser(){return this.block?ak.parse:ak.parseInline}}class am{defaults=d();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=ak;Renderer=ai;TextRenderer=aj;Lexer=ah;Tokenizer=ag;Hooks=al;constructor(...a){this.use(...a)}walkTokens(a,b){let c=[];for(let d of a)switch(c=c.concat(b.call(this,d)),d.type){case"table":for(let a of d.header)c=c.concat(this.walkTokens(a.tokens,b));for(let a of d.rows)for(let d of a)c=c.concat(this.walkTokens(d.tokens,b));break;case"list":c=c.concat(this.walkTokens(d.items,b));break;default:{let a=d;this.defaults.extensions?.childTokens?.[a.type]?this.defaults.extensions.childTokens[a.type].forEach(d=>{let e=a[d].flat(1/0);c=c.concat(this.walkTokens(e,b))}):a.tokens&&(c=c.concat(this.walkTokens(a.tokens,b)))}}return c}use(...a){let b=this.defaults.extensions||{renderers:{},childTokens:{}};return a.forEach(a=>{let c={...a};if(c.async=this.defaults.async||c.async||!1,a.extensions&&(a.extensions.forEach(a=>{if(!a.name)throw Error("extension name required");if("renderer"in a){let c=b.renderers[a.name];c?b.renderers[a.name]=function(...b){let d=a.renderer.apply(this,b);return!1===d&&(d=c.apply(this,b)),d}:b.renderers[a.name]=a.renderer}if("tokenizer"in a){if(!a.level||"block"!==a.level&&"inline"!==a.level)throw Error("extension level must be 'block' or 'inline'");let c=b[a.level];c?c.unshift(a.tokenizer):b[a.level]=[a.tokenizer],a.start&&("block"===a.level?b.startBlock?b.startBlock.push(a.start):b.startBlock=[a.start]:"inline"===a.level&&(b.startInline?b.startInline.push(a.start):b.startInline=[a.start]))}"childTokens"in a&&a.childTokens&&(b.childTokens[a.name]=a.childTokens)}),c.extensions=b),a.renderer){let b=this.defaults.renderer||new ai(this.defaults);for(let c in a.renderer){if(!(c in b))throw Error(`renderer '${c}' does not exist`);if(["options","parser"].includes(c))continue;let d=a.renderer[c],e=b[c];b[c]=(...a)=>{let c=d.apply(b,a);return!1===c&&(c=e.apply(b,a)),c||""}}c.renderer=b}if(a.tokenizer){let b=this.defaults.tokenizer||new ag(this.defaults);for(let c in a.tokenizer){if(!(c in b))throw Error(`tokenizer '${c}' does not exist`);if(["options","rules","lexer"].includes(c))continue;let d=a.tokenizer[c],e=b[c];b[c]=(...a)=>{let c=d.apply(b,a);return!1===c&&(c=e.apply(b,a)),c}}c.tokenizer=b}if(a.hooks){let b=this.defaults.hooks||new al;for(let c in a.hooks){if(!(c in b))throw Error(`hook '${c}' does not exist`);if(["options","block"].includes(c))continue;let d=a.hooks[c],e=b[c];al.passThroughHooks.has(c)?b[c]=a=>{if(this.defaults.async)return Promise.resolve(d.call(b,a)).then(a=>e.call(b,a));let c=d.call(b,a);return e.call(b,c)}:b[c]=(...a)=>{let c=d.apply(b,a);return!1===c&&(c=e.apply(b,a)),c}}c.hooks=b}if(a.walkTokens){let b=this.defaults.walkTokens,d=a.walkTokens;c.walkTokens=function(a){let c=[];return c.push(d.call(this,a)),b&&(c=c.concat(b.call(this,a))),c}}this.defaults={...this.defaults,...c}}),this}setOptions(a){return this.defaults={...this.defaults,...a},this}lexer(a,b){return ah.lex(a,b??this.defaults)}parser(a,b){return ak.parse(a,b??this.defaults)}parseMarkdown(a){return(b,c)=>{let d={...c},e={...this.defaults,...d},f=this.onError(!!e.silent,!!e.async);if(!0===this.defaults.async&&!1===d.async)return f(Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==b)return f(Error("marked(): input parameter is undefined or null"));if("string"!=typeof b)return f(Error("marked(): input parameter is of type "+Object.prototype.toString.call(b)+", string expected"));e.hooks&&(e.hooks.options=e,e.hooks.block=a);let g=e.hooks?e.hooks.provideLexer():a?ah.lex:ah.lexInline,h=e.hooks?e.hooks.provideParser():a?ak.parse:ak.parseInline;if(e.async)return Promise.resolve(e.hooks?e.hooks.preprocess(b):b).then(a=>g(a,e)).then(a=>e.hooks?e.hooks.processAllTokens(a):a).then(a=>e.walkTokens?Promise.all(this.walkTokens(a,e.walkTokens)).then(()=>a):a).then(a=>h(a,e)).then(a=>e.hooks?e.hooks.postprocess(a):a).catch(f);try{e.hooks&&(b=e.hooks.preprocess(b));let a=g(b,e);e.hooks&&(a=e.hooks.processAllTokens(a)),e.walkTokens&&this.walkTokens(a,e.walkTokens);let c=h(a,e);return e.hooks&&(c=e.hooks.postprocess(c)),c}catch(a){return f(a)}}}onError(a,b){return c=>{if(c.message+="\nPlease report this to https://github.com/markedjs/marked.",a){let a="<p>An error occurred:</p><pre>"+ab(c.message+"",!0)+"</pre>";return b?Promise.resolve(a):a}if(b)return Promise.reject(c);throw c}}}let an=new am;function ao(a,b){return an.parse(a,b)}ao.options=ao.setOptions=function(a){return an.setOptions(a),ao.defaults=an.defaults,e=ao.defaults,ao},ao.getDefaults=d,ao.defaults=e,ao.use=function(...a){return an.use(...a),ao.defaults=an.defaults,e=ao.defaults,ao},ao.walkTokens=function(a,b){return an.walkTokens(a,b)},ao.parseInline=an.parseInline,ao.Parser=ak,ao.parser=ak.parse,ao.Renderer=ai,ao.TextRenderer=aj,ao.Lexer=ah,ao.lexer=ah.lex,ao.Tokenizer=ag,ao.Hooks=al,ao.parse=ao,ao.options,ao.setOptions,ao.use,ao.walkTokens,ao.parseInline,ak.parse,ah.lex},75392:a=>{"use strict";let{entries:b,setPrototypeOf:c,isFrozen:d,getPrototypeOf:e,getOwnPropertyDescriptor:f}=Object,{freeze:g,seal:h,create:i}=Object,{apply:j,construct:k}="undefined"!=typeof Reflect&&Reflect;g||(g=function(a){return a}),h||(h=function(a){return a}),j||(j=function(a,b,c){return a.apply(b,c)}),k||(k=function(a,b){return new a(...b)});let l=z(Array.prototype.forEach),m=z(Array.prototype.lastIndexOf),n=z(Array.prototype.pop),o=z(Array.prototype.push),p=z(Array.prototype.splice),q=z(String.prototype.toLowerCase),r=z(String.prototype.toString),s=z(String.prototype.match),t=z(String.prototype.replace),u=z(String.prototype.indexOf),v=z(String.prototype.trim),w=z(Object.prototype.hasOwnProperty),x=z(RegExp.prototype.test),y=(X=TypeError,function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return k(X,b)});function z(a){return function(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return j(a,b,d)}}function A(a,b){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:q;c&&c(a,null);let f=b.length;for(;f--;){let c=b[f];if("string"==typeof c){let a=e(c);a!==c&&(d(b)||(b[f]=a),c=a)}a[c]=!0}return a}function B(a){let c=i(null);for(let[d,e]of b(a))w(a,d)&&(Array.isArray(e)?c[d]=function(a){for(let b=0;b<a.length;b++)w(a,b)||(a[b]=null);return a}(e):e&&"object"==typeof e&&e.constructor===Object?c[d]=B(e):c[d]=e);return c}function C(a,b){for(;null!==a;){let c=f(a,b);if(c){if(c.get)return z(c.get);if("function"==typeof c.value)return z(c.value)}a=e(a)}return function(){return null}}let D=g(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),E=g(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),F=g(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),G=g(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),H=g(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),I=g(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),J=g(["#text"]),K=g(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),L=g(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),M=g(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),N=g(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),O=h(/\{\{[\w\W]*|[\w\W]*\}\}/gm),P=h(/<%[\w\W]*|[\w\W]*%>/gm),Q=h(/\$\{[\w\W]*/gm),R=h(/^data-[\-\w.\u00B7-\uFFFF]+$/),S=h(/^aria-[\-\w]+$/),T=h(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),U=h(/^(?:\w+script|data):/i),V=h(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),W=h(/^html$/i);var X,Y=Object.freeze({__proto__:null,ARIA_ATTR:S,ATTR_WHITESPACE:V,CUSTOM_ELEMENT:h(/^[a-z][.\w]*(-[.\w]+)+$/i),DATA_ATTR:R,DOCTYPE_NAME:W,ERB_EXPR:P,IS_ALLOWED_URI:T,IS_SCRIPT_OR_DATA:U,MUSTACHE_EXPR:O,TMPLIT_EXPR:Q});let Z={element:1,text:3,progressingInstruction:7,comment:8,document:9},$=function(a,b){if("object"!=typeof a||"function"!=typeof a.createPolicy)return null;let c=null,d="data-tt-policy-suffix";b&&b.hasAttribute(d)&&(c=b.getAttribute(d));let e="dompurify"+(c?"#"+c:"");try{return a.createPolicy(e,{createHTML:a=>a,createScriptURL:a=>a})}catch(a){return console.warn("TrustedTypes policy "+e+" could not be created."),null}},_=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};a.exports=function a(){let c,d=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window,e=b=>a(b);if(e.version="3.2.4",e.removed=[],!d||!d.document||d.document.nodeType!==Z.document||!d.Element)return e.isSupported=!1,e;let{document:f}=d,h=f,j=h.currentScript,{DocumentFragment:k,HTMLTemplateElement:z,Node:O,Element:P,NodeFilter:Q,NamedNodeMap:R=d.NamedNodeMap||d.MozNamedAttrMap,HTMLFormElement:S,DOMParser:U,trustedTypes:V}=d,X=P.prototype,aa=C(X,"cloneNode"),ab=C(X,"remove"),ac=C(X,"nextSibling"),ad=C(X,"childNodes"),ae=C(X,"parentNode");if("function"==typeof z){let a=f.createElement("template");a.content&&a.content.ownerDocument&&(f=a.content.ownerDocument)}let af="",{implementation:ag,createNodeIterator:ah,createDocumentFragment:ai,getElementsByTagName:aj}=f,{importNode:ak}=h,al=_();e.isSupported="function"==typeof b&&"function"==typeof ae&&ag&&void 0!==ag.createHTMLDocument;let{MUSTACHE_EXPR:am,ERB_EXPR:an,TMPLIT_EXPR:ao,DATA_ATTR:ap,ARIA_ATTR:aq,IS_SCRIPT_OR_DATA:ar,ATTR_WHITESPACE:as,CUSTOM_ELEMENT:at}=Y,{IS_ALLOWED_URI:au}=Y,av=null,aw=A({},[...D,...E,...F,...H,...J]),ax=null,ay=A({},[...K,...L,...M,...N]),az=Object.seal(i(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),aA=null,aB=null,aC=!0,aD=!0,aE=!1,aF=!0,aG=!1,aH=!0,aI=!1,aJ=!1,aK=!1,aL=!1,aM=!1,aN=!1,aO=!0,aP=!1,aQ=!0,aR=!1,aS={},aT=null,aU=A({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),aV=null,aW=A({},["audio","video","img","source","image","track"]),aX=null,aY=A({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),aZ="http://www.w3.org/1998/Math/MathML",a$="http://www.w3.org/2000/svg",a_="http://www.w3.org/1999/xhtml",a0=a_,a1=!1,a2=null,a3=A({},[aZ,a$,a_],r),a4=A({},["mi","mo","mn","ms","mtext"]),a5=A({},["annotation-xml"]),a6=A({},["title","style","font","a","script"]),a7=null,a8=["application/xhtml+xml","text/html"],a9=null,ba=null,bb=f.createElement("form"),bc=function(a){return a instanceof RegExp||a instanceof Function},bd=function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ba||ba!==a){if(a&&"object"==typeof a||(a={}),a=B(a),a9="application/xhtml+xml"===(a7=-1===a8.indexOf(a.PARSER_MEDIA_TYPE)?"text/html":a.PARSER_MEDIA_TYPE)?r:q,av=w(a,"ALLOWED_TAGS")?A({},a.ALLOWED_TAGS,a9):aw,ax=w(a,"ALLOWED_ATTR")?A({},a.ALLOWED_ATTR,a9):ay,a2=w(a,"ALLOWED_NAMESPACES")?A({},a.ALLOWED_NAMESPACES,r):a3,aX=w(a,"ADD_URI_SAFE_ATTR")?A(B(aY),a.ADD_URI_SAFE_ATTR,a9):aY,aV=w(a,"ADD_DATA_URI_TAGS")?A(B(aW),a.ADD_DATA_URI_TAGS,a9):aW,aT=w(a,"FORBID_CONTENTS")?A({},a.FORBID_CONTENTS,a9):aU,aA=w(a,"FORBID_TAGS")?A({},a.FORBID_TAGS,a9):{},aB=w(a,"FORBID_ATTR")?A({},a.FORBID_ATTR,a9):{},aS=!!w(a,"USE_PROFILES")&&a.USE_PROFILES,aC=!1!==a.ALLOW_ARIA_ATTR,aD=!1!==a.ALLOW_DATA_ATTR,aE=a.ALLOW_UNKNOWN_PROTOCOLS||!1,aF=!1!==a.ALLOW_SELF_CLOSE_IN_ATTR,aG=a.SAFE_FOR_TEMPLATES||!1,aH=!1!==a.SAFE_FOR_XML,aI=a.WHOLE_DOCUMENT||!1,aL=a.RETURN_DOM||!1,aM=a.RETURN_DOM_FRAGMENT||!1,aN=a.RETURN_TRUSTED_TYPE||!1,aK=a.FORCE_BODY||!1,aO=!1!==a.SANITIZE_DOM,aP=a.SANITIZE_NAMED_PROPS||!1,aQ=!1!==a.KEEP_CONTENT,aR=a.IN_PLACE||!1,au=a.ALLOWED_URI_REGEXP||T,a0=a.NAMESPACE||a_,a4=a.MATHML_TEXT_INTEGRATION_POINTS||a4,a5=a.HTML_INTEGRATION_POINTS||a5,az=a.CUSTOM_ELEMENT_HANDLING||{},a.CUSTOM_ELEMENT_HANDLING&&bc(a.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(az.tagNameCheck=a.CUSTOM_ELEMENT_HANDLING.tagNameCheck),a.CUSTOM_ELEMENT_HANDLING&&bc(a.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(az.attributeNameCheck=a.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),a.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof a.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(az.allowCustomizedBuiltInElements=a.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),aG&&(aD=!1),aM&&(aL=!0),aS&&(av=A({},J),ax=[],!0===aS.html&&(A(av,D),A(ax,K)),!0===aS.svg&&(A(av,E),A(ax,L),A(ax,N)),!0===aS.svgFilters&&(A(av,F),A(ax,L),A(ax,N)),!0===aS.mathMl&&(A(av,H),A(ax,M),A(ax,N))),a.ADD_TAGS&&(av===aw&&(av=B(av)),A(av,a.ADD_TAGS,a9)),a.ADD_ATTR&&(ax===ay&&(ax=B(ax)),A(ax,a.ADD_ATTR,a9)),a.ADD_URI_SAFE_ATTR&&A(aX,a.ADD_URI_SAFE_ATTR,a9),a.FORBID_CONTENTS&&(aT===aU&&(aT=B(aT)),A(aT,a.FORBID_CONTENTS,a9)),aQ&&(av["#text"]=!0),aI&&A(av,["html","head","body"]),av.table&&(A(av,["tbody"]),delete aA.tbody),a.TRUSTED_TYPES_POLICY){if("function"!=typeof a.TRUSTED_TYPES_POLICY.createHTML)throw y('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof a.TRUSTED_TYPES_POLICY.createScriptURL)throw y('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');af=(c=a.TRUSTED_TYPES_POLICY).createHTML("")}else void 0===c&&(c=$(V,j)),null!==c&&"string"==typeof af&&(af=c.createHTML(""));g&&g(a),ba=a}},be=A({},[...E,...F,...G]),bf=A({},[...H,...I]),bg=function(a){let b=ae(a);b&&b.tagName||(b={namespaceURI:a0,tagName:"template"});let c=q(a.tagName),d=q(b.tagName);return!!a2[a.namespaceURI]&&(a.namespaceURI===a$?b.namespaceURI===a_?"svg"===c:b.namespaceURI===aZ?"svg"===c&&("annotation-xml"===d||a4[d]):!!be[c]:a.namespaceURI===aZ?b.namespaceURI===a_?"math"===c:b.namespaceURI===a$?"math"===c&&a5[d]:!!bf[c]:a.namespaceURI===a_?(b.namespaceURI!==a$||!!a5[d])&&(b.namespaceURI!==aZ||!!a4[d])&&!bf[c]&&(a6[c]||!be[c]):"application/xhtml+xml"===a7&&!!a2[a.namespaceURI])},bh=function(a){o(e.removed,{element:a});try{ae(a).removeChild(a)}catch(b){ab(a)}},bi=function(a,b){try{o(e.removed,{attribute:b.getAttributeNode(a),from:b})}catch(a){o(e.removed,{attribute:null,from:b})}if(b.removeAttribute(a),"is"===a)if(aL||aM)try{bh(b)}catch(a){}else try{b.setAttribute(a,"")}catch(a){}},bj=function(a){let b=null,d=null;if(aK)a="<remove></remove>"+a;else{let b=s(a,/^[\r\n\t ]+/);d=b&&b[0]}"application/xhtml+xml"===a7&&a0===a_&&(a='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+a+"</body></html>");let e=c?c.createHTML(a):a;if(a0===a_)try{b=new U().parseFromString(e,a7)}catch(a){}if(!b||!b.documentElement){b=ag.createDocument(a0,"template",null);try{b.documentElement.innerHTML=a1?af:e}catch(a){}}let g=b.body||b.documentElement;return(a&&d&&g.insertBefore(f.createTextNode(d),g.childNodes[0]||null),a0===a_)?aj.call(b,aI?"html":"body")[0]:aI?b.documentElement:g},bk=function(a){return ah.call(a.ownerDocument||a,a,Q.SHOW_ELEMENT|Q.SHOW_COMMENT|Q.SHOW_TEXT|Q.SHOW_PROCESSING_INSTRUCTION|Q.SHOW_CDATA_SECTION,null)},bl=function(a){return a instanceof S&&("string"!=typeof a.nodeName||"string"!=typeof a.textContent||"function"!=typeof a.removeChild||!(a.attributes instanceof R)||"function"!=typeof a.removeAttribute||"function"!=typeof a.setAttribute||"string"!=typeof a.namespaceURI||"function"!=typeof a.insertBefore||"function"!=typeof a.hasChildNodes)},bm=function(a){return"function"==typeof O&&a instanceof O};function bn(a,b,c){l(a,a=>{a.call(e,b,c,ba)})}let bo=function(a){let b=null;if(bn(al.beforeSanitizeElements,a,null),bl(a))return bh(a),!0;let c=a9(a.nodeName);if(bn(al.uponSanitizeElement,a,{tagName:c,allowedTags:av}),a.hasChildNodes()&&!bm(a.firstElementChild)&&x(/<[/\w]/g,a.innerHTML)&&x(/<[/\w]/g,a.textContent)||a.nodeType===Z.progressingInstruction||aH&&a.nodeType===Z.comment&&x(/<[/\w]/g,a.data))return bh(a),!0;if(!av[c]||aA[c]){if(!aA[c]&&bq(c)&&(az.tagNameCheck instanceof RegExp&&x(az.tagNameCheck,c)||az.tagNameCheck instanceof Function&&az.tagNameCheck(c)))return!1;if(aQ&&!aT[c]){let b=ae(a)||a.parentNode,c=ad(a)||a.childNodes;if(c&&b){let d=c.length;for(let e=d-1;e>=0;--e){let d=aa(c[e],!0);d.__removalCount=(a.__removalCount||0)+1,b.insertBefore(d,ac(a))}}}return bh(a),!0}return a instanceof P&&!bg(a)||("noscript"===c||"noembed"===c||"noframes"===c)&&x(/<\/no(script|embed|frames)/i,a.innerHTML)?(bh(a),!0):(aG&&a.nodeType===Z.text&&(b=a.textContent,l([am,an,ao],a=>{b=t(b,a," ")}),a.textContent!==b&&(o(e.removed,{element:a.cloneNode()}),a.textContent=b)),bn(al.afterSanitizeElements,a,null),!1)},bp=function(a,b,c){if(aO&&("id"===b||"name"===b)&&(c in f||c in bb))return!1;if(aD&&!aB[b]&&x(ap,b));else if(aC&&x(aq,b));else if(!ax[b]||aB[b]){if(!(bq(a)&&(az.tagNameCheck instanceof RegExp&&x(az.tagNameCheck,a)||az.tagNameCheck instanceof Function&&az.tagNameCheck(a))&&(az.attributeNameCheck instanceof RegExp&&x(az.attributeNameCheck,b)||az.attributeNameCheck instanceof Function&&az.attributeNameCheck(b))||"is"===b&&az.allowCustomizedBuiltInElements&&(az.tagNameCheck instanceof RegExp&&x(az.tagNameCheck,c)||az.tagNameCheck instanceof Function&&az.tagNameCheck(c))))return!1}else if(aX[b]);else if(x(au,t(c,as,"")));else if(("src"===b||"xlink:href"===b||"href"===b)&&"script"!==a&&0===u(c,"data:")&&aV[a]);else if(aE&&!x(ar,t(c,as,"")));else if(c)return!1;return!0},bq=function(a){return"annotation-xml"!==a&&s(a,at)},br=function(a){bn(al.beforeSanitizeAttributes,a,null);let{attributes:b}=a;if(!b||bl(a))return;let d={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ax,forceKeepAttr:void 0},f=b.length;for(;f--;){let{name:g,namespaceURI:h,value:i}=b[f],j=a9(g),k="value"===g?i:v(i);if(d.attrName=j,d.attrValue=k,d.keepAttr=!0,d.forceKeepAttr=void 0,bn(al.uponSanitizeAttribute,a,d),k=d.attrValue,aP&&("id"===j||"name"===j)&&(bi(g,a),k="user-content-"+k),aH&&x(/((--!?|])>)|<\/(style|title)/i,k)){bi(g,a);continue}if(d.forceKeepAttr||(bi(g,a),!d.keepAttr))continue;if(!aF&&x(/\/>/i,k)){bi(g,a);continue}aG&&l([am,an,ao],a=>{k=t(k,a," ")});let m=a9(a.nodeName);if(bp(m,j,k)){if(c&&"object"==typeof V&&"function"==typeof V.getAttributeType)if(h);else switch(V.getAttributeType(m,j)){case"TrustedHTML":k=c.createHTML(k);break;case"TrustedScriptURL":k=c.createScriptURL(k)}try{h?a.setAttributeNS(h,g,k):a.setAttribute(g,k),bl(a)?bh(a):n(e.removed)}catch(a){}}}bn(al.afterSanitizeAttributes,a,null)},bs=function a(b){let c=null,d=bk(b);for(bn(al.beforeSanitizeShadowDOM,b,null);c=d.nextNode();)bn(al.uponSanitizeShadowNode,c,null),bo(c),br(c),c.content instanceof k&&a(c.content);bn(al.afterSanitizeShadowDOM,b,null)};return e.sanitize=function(a){let b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=null,f=null,g=null,i=null;if((a1=!a)&&(a="\x3c!--\x3e"),"string"!=typeof a&&!bm(a))if("function"==typeof a.toString){if("string"!=typeof(a=a.toString()))throw y("dirty is not a string, aborting")}else throw y("toString is not a function");if(!e.isSupported)return a;if(aJ||bd(b),e.removed=[],"string"==typeof a&&(aR=!1),aR){if(a.nodeName){let b=a9(a.nodeName);if(!av[b]||aA[b])throw y("root node is forbidden and cannot be sanitized in-place")}}else if(a instanceof O)(f=(d=bj("\x3c!----\x3e")).ownerDocument.importNode(a,!0)).nodeType===Z.element&&"BODY"===f.nodeName||"HTML"===f.nodeName?d=f:d.appendChild(f);else{if(!aL&&!aG&&!aI&&-1===a.indexOf("<"))return c&&aN?c.createHTML(a):a;if(!(d=bj(a)))return aL?null:aN?af:""}d&&aK&&bh(d.firstChild);let j=bk(aR?a:d);for(;g=j.nextNode();)bo(g),br(g),g.content instanceof k&&bs(g.content);if(aR)return a;if(aL){if(aM)for(i=ai.call(d.ownerDocument);d.firstChild;)i.appendChild(d.firstChild);else i=d;return(ax.shadowroot||ax.shadowrootmode)&&(i=ak.call(h,i,!0)),i}let m=aI?d.outerHTML:d.innerHTML;return aI&&av["!doctype"]&&d.ownerDocument&&d.ownerDocument.doctype&&d.ownerDocument.doctype.name&&x(W,d.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+d.ownerDocument.doctype.name+">\n"+m),aG&&l([am,an,ao],a=>{m=t(m,a," ")}),c&&aN?c.createHTML(m):m},e.setConfig=function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};bd(a),aJ=!0},e.clearConfig=function(){ba=null,aJ=!1},e.isValidAttribute=function(a,b,c){return ba||bd({}),bp(a9(a),a9(b),c)},e.addHook=function(a,b){"function"==typeof b&&o(al[a],b)},e.removeHook=function(a,b){if(void 0!==b){let c=m(al[a],b);return -1===c?void 0:p(al[a],c,1)[0]}return n(al[a])},e.removeHooks=function(a){al[a]=[]},e.removeAllHooks=function(){al=_()},e}()}};