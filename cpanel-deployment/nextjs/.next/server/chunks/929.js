exports.id=929,exports.ids=[929],exports.modules={756:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,54160,23)),Promise.resolve().then(c.t.bind(c,31603,23)),Promise.resolve().then(c.t.bind(c,68495,23)),Promise.resolve().then(c.t.bind(c,75170,23)),Promise.resolve().then(c.t.bind(c,77526,23)),Promise.resolve().then(c.t.bind(c,78922,23)),Promise.resolve().then(c.t.bind(c,29234,23)),Promise.resolve().then(c.t.bind(c,12263,23)),Promise.resolve().then(c.bind(c,82146))},12951:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(21124),e=c(3991),f=c.n(e),g=c(18062),h=c(27832);function i(){let a=(0,g.o)();return(0,d.jsxs)("div",{className:"mt-[6px] md:mt-0 col-span-1",children:[(0,d.jsx)("h3",{className:"text-white font-medium text-xl tracking-tight text-center md:text-start",children:"Navigation"}),(0,d.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,d.jsx)("li",{className:"text-center md:text-start",children:(0,d.jsx)(f(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/",onClick:b=>{b.preventDefault(),a.push("/",{onTransitionReady:h.W})},children:"Home"})}),(0,d.jsx)("li",{className:"text-center md:text-start",children:(0,d.jsx)(f(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/projects/",onClick:b=>{b.preventDefault(),a.push("/projects/",{onTransitionReady:h.W})},children:"Projects"})}),(0,d.jsx)("li",{className:"text-center md:text-start",children:(0,d.jsx)(f(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/blog/",onClick:b=>{b.preventDefault(),a.push("/blog/",{onTransitionReady:h.W})},children:"Blog"})}),(0,d.jsx)("li",{className:"text-center md:text-start",children:(0,d.jsx)(f(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/contact/",onClick:b=>{b.preventDefault(),a.push("/contact/",{onTransitionReady:h.W})},children:"Contact"})})]})]})}},12989:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterNavigation.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterNavigation.jsx","default")},13541:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Header.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Header.jsx","default")},13903:(a,b,c)=>{"use strict";c.d(b,{default:()=>m});var d=c(21124),e=c(24515),f=c(80386),g=c(35399),h=c(3991),i=c.n(h),j=c(18062),k=c(27832),l=c(38301);function m({data:a,siteRepresentation:b}){let[c,h]=(0,l.useState)(!1),m=(0,j.o)(),n=(0,l.useCallback)(()=>{h(!c)},[c]);if(!a||!b)return(0,d.jsx)("div",{className:"backdrop-blur-xl sticky top-0 z-[1000] border-b border-black/15",children:(0,d.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,d.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "Header" component.'})})});let{additionalNavigationItems:o,cta:p}=a,{logo:q,logomark:r}=b,s=new URL(q.url,"https://api.yourdomain.com").href,t=new URL(r.url,"https://api.yourdomain.com").href;return(0,d.jsx)("header",{className:"backdrop-blur-xl sticky top-0 z-[1000] border-b border-black/15",children:(0,d.jsxs)("nav",{className:"flex flex-wrap gap-4 md:gap-6 items-center justify-between p-4",children:[(0,d.jsxs)(i(),{href:"/",className:"block text-primary-700",onClick:a=>{a.preventDefault(),m.push("/",{onTransitionReady:k.W})},children:[(0,d.jsx)("span",{className:"sr-only",children:"Home"}),(0,d.jsx)(e.default,{draggable:"false",priority:!0,src:s,alt:q.alternativeText??"",className:"hidden md:block",width:Math.round((q?.width??36)/2),height:Math.round((q?.height??36)/2),sizes:`${Math.round((q?.width??36)/2)}px`}),(0,d.jsx)(e.default,{draggable:"false",priority:!0,src:t,alt:r.alternativeText??"",className:"md:hidden",width:Math.round((r?.width??36)/2),height:Math.round((r?.height??36)/2),sizes:`${Math.round((r?.width??36)/2)}px`})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 md:order-2",children:[(0,d.jsx)(f.default,{className:"!h-9",target:p.openLinkInNewTab?"_blank":void 0,rel:p.sameHostLink?void 0:"noopener noreferrer",label:p.label,url:p.url,showIcon:p.showIcon,iconType:p.iconType}),(0,d.jsxs)("button",{className:" block justify-items-center w-9 h-9 rounded-full transition border border-primary-100 text-primary-700 bg-primary-50 hover:bg-primary-100 active:bg-primary-200 md:hidden ","aria-label":"Toggle navigation","aria-expanded":c,"aria-controls":"header-navigation",onClick:n,children:[(0,d.jsx)("span",{className:"sr-only",children:"Toggle menu"}),(0,d.jsx)(g.A,{className:"size-5"})]})]}),(0,d.jsxs)("ul",{id:"header-navigation",className:`header-navigation flex flex-col basis-full grow text-base md:flex-row md:basis-auto ${c?"show":""}`,children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/projects/",className:"block py-[10px] leading-none md:px-2 text-gray-900 transition hover:text-gray-900/75",onClick:a=>{a.preventDefault(),m.push("/projects/",{onTransitionReady:k.W})},children:"Projects"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/blog/",className:"block py-[10px] leading-none md:px-2 text-gray-900 transition hover:text-gray-900/75",onClick:a=>{a.preventDefault(),m.push("/blog/",{onTransitionReady:k.W})},children:"Blog"})}),o.length>0&&o.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(i(),{target:a.openLinkInNewTab?"_blank":void 0,rel:a.sameHostLink?void 0:"noopener noreferrer",href:a.url,className:"block py-[10px] leading-none md:px-2 text-gray-900 transition hover:text-gray-900/75",children:a.label})},a.id))]})]})})}},26411:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Announcement.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/Announcement.jsx","default")},27832:(a,b,c)=>{"use strict";c.d(b,{W:()=>e,Y:()=>d});let d=(a,b="en-US")=>new Intl.DateTimeFormat(b,{dateStyle:"short"}).format(new Date(a)),e=()=>{document.documentElement.animate([{opacity:1,scale:1,transform:"translateY(0)"},{opacity:.5,scale:.9,transform:"translateY(-100px)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-old(root)"}),document.documentElement.animate([{transform:"translateY(100%)"},{transform:"translateY(0)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-new(root)"})}},32271:(a,b,c)=>{Promise.resolve().then(c.bind(c,26411)),Promise.resolve().then(c.bind(c,41810)),Promise.resolve().then(c.bind(c,56972)),Promise.resolve().then(c.bind(c,42600)),Promise.resolve().then(c.bind(c,12989)),Promise.resolve().then(c.bind(c,13541)),Promise.resolve().then(c.bind(c,65986)),Promise.resolve().then(c.bind(c,33820)),Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.t.bind(c,76424,23))},34346:(a,b,c)=>{Promise.resolve().then(c.bind(c,80636))},41588:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(21124),e=c(72893),f=c(24086),g=c(3991),h=c.n(g),i=c(18062),j=c(27832);function k({label:a="Default label",url:b="#",target:c,rel:g,className:k="",showIcon:l=!1,iconType:m="arrowRight",...n}){let o=(0,i.o)();return(0,d.jsxs)(h(),{target:c,rel:g,href:b,onClick:a=>{a.preventDefault(),o.push(b,{onTransitionReady:j.W})},className:`
        group
        inline-flex
        justify-center
        items-center
        transition
        px-4
        h-11
        font-medium
        leading-none
        rounded-full
        text-primary-700
        border border-white
        hover:border-neutral-100
        active:border-neutral-200
        bg-white
        hover:bg-neutral-100
        active:bg-neutral-200
        ${k}
      `,...n,children:[a,l?"arrowUpRight"===m?(0,d.jsx)(f.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):(0,d.jsx)(e.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):null]})}},41810:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnLight.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnLight.jsx","default")},42600:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterCopyright.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/FooterCopyright.jsx","default")},47381:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,generateMetadata:()=>g});var d=c(75338),e=c(80636),f=c(37522);async function g(a,b){let c;try{c=await (0,f.p3)()}catch(a){return console.error(a.message),{}}let d=await b,{metadata:e}=c,{title:g,description:h,image:i}=e,j=i?new URL(i.url,"https://api.yourdomain.com").href:d.openGraph.images[0];return{title:g||`Page Not Found | ${d.openGraph.siteName}`,description:h||d.description,openGraph:{...d.openGraph,images:[j],type:"website"}}}async function h(){let[a,b]=await Promise.allSettled([(0,f.p3)(),(0,f.Xp)()]);if("rejected"===a.status)return(0,d.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,d.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "404 - Page not found" page.'})});let{banner:c}=a.value,{headline:g,supportiveText:h}=c,i=null;if("fulfilled"===b.status){let{siteRepresentation:a,miscellaneous:c}=b.value,{siteImage:d,logo:e,knowsAbout:f,isOrganization:g,siteName:h,siteDescription:j,jobTitle:k,schedulingLink:l,socialChannels:m,addressLocality:n,areaServed:o}=a,p=new URL(d.url,"https://api.yourdomain.com").href,q=new URL(e.url,"https://api.yourdomain.com").href,r=f.flatMap(a=>a.children.map(a=>a.name)),{htmlLanguageTag:s}=c;i={"@context":"https://schema.org","@graph":[{"@type":"WebSite","@id":new URL("/#website","https://yourdomain.com").href,url:new URL("/","https://yourdomain.com").href,name:h,description:j,inLanguage:s,publisher:{"@id":g?new URL("/#organization","https://yourdomain.com").href:new URL("/#person","https://yourdomain.com").href}},{"@type":g?"Organization":"Person","@id":g?new URL("/#organization","https://yourdomain.com").href:new URL("/#person","https://yourdomain.com").href,name:h,description:j,url:new URL("/","https://yourdomain.com").href,contactPoint:{"@type":"ContactPoint",url:new URL("/contact/","https://yourdomain.com").href},...g&&{logo:q},image:p,...!g&&{jobTitle:k},...l||m.length>0?{sameAs:[...l?[l]:[],...m.map(a=>a.url)]}:{},knowsAbout:r,address:{"@type":"PostalAddress",addressLocality:n},...g&&o&&{areaServed:o}}]}}return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(i)}}),(0,d.jsx)("section",{className:"triangle-path bg-neutral-100 py-16 relative after:content-[''] after:absolute after:top-0 after:right-0 after:w-1/4 after:h-full after:bg-neutral-200/50",children:(0,d.jsxs)("div",{className:"relative mx-auto max-w-5xl px-4 text-center z-10",children:[(0,d.jsx)("h1",{className:"text-gray-900 font-bold text-3xl sm:text-4xl tracking-tight text-center mb-4",children:g}),(0,d.jsx)("p",{className:"text-gray-700 text-lg mb-7",children:h}),(0,d.jsx)(e.default,{className:"w-full sm:w-auto",label:"Return to Home",url:"/"})]})})]})}},48020:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,81170,23)),Promise.resolve().then(c.t.bind(c,23597,23)),Promise.resolve().then(c.t.bind(c,36893,23)),Promise.resolve().then(c.t.bind(c,89748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,69576,23)),Promise.resolve().then(c.t.bind(c,73041,23)),Promise.resolve().then(c.t.bind(c,51384,23))},51746:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(21124),e=c(3991),f=c.n(e),g=c(18062),h=c(27832);function i({copyright:a}){let b=(0,g.o)();return(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between",children:[(0,d.jsx)(f(),{className:"text-white/75 text-base transition hover:underline md:order-2 text-center mb-4 md:mb-0",href:"/privacy-policy/",onClick:a=>{a.preventDefault(),b.push("/privacy-policy/",{onTransitionReady:h.W})},children:"Privacy policy"}),(0,d.jsx)("p",{className:"text-white/75 text-base md:order-1 text-center",children:a})]})}},56972:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ContactLink.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/ContactLink.jsx","default")},65986:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/NoSSRWrapper.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/NoSSRWrapper.jsx","default")},70512:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(13973).default)(async()=>{},{loadableGenerated:{modules:["components/NoSSRWrapper.jsx -> ./Wrapper"]},ssr:!1})},70541:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z,generateMetadata:()=>y,generateViewport:()=>x});var d=c(75338);c(82704);var e=c(26411),f=c(13541),g=c(29507),h=c(82296),i=c(31946),j=c(41427),k=c(65169),l=c.n(k),m=c(65986),n=c(56972),o=c(59865),p=c(12989),q=c(42600);let r={LinkedIn:(0,d.jsx)("svg",{className:"size-8",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"})}),GitHub:(0,d.jsx)("svg",{className:"size-8",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M448 96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96zM265.8 407.7c0-1.8 0-6 .1-11.6c.1-11.4 .1-28.8 .1-43.7c0-15.6-5.2-25.5-11.3-30.7c37-4.1 76-9.2 76-73.1c0-18.2-6.5-27.3-17.1-39c1.7-4.3 7.4-22-1.7-45c-13.9-4.3-45.7 17.9-45.7 17.9c-13.2-3.7-27.5-5.6-41.6-5.6s-28.4 1.9-41.6 5.6c0 0-31.8-22.2-45.7-17.9c-9.1 22.9-3.5 40.6-1.7 45c-10.6 11.7-15.6 20.8-15.6 39c0 63.6 37.3 69 74.3 73.1c-4.8 4.3-9.1 11.7-10.6 22.3c-9.5 4.3-33.8 11.7-48.3-13.9c-9.1-15.8-25.5-17.1-25.5-17.1c-16.2-.2-1.1 10.2-1.1 10.2c10.8 5 18.4 24.2 18.4 24.2c9.7 29.7 56.1 19.7 56.1 19.7c0 9 .1 21.7 .1 30.6c0 4.8 .1 8.6 .1 10c0 4.3-3 9.5-11.5 8C106 393.6 59.8 330.8 59.8 257.4c0-91.8 70.2-161.5 162-161.5s166.2 69.7 166.2 161.5c.1 73.4-44.7 136.3-110.7 158.3c-8.4 1.5-11.5-3.7-11.5-8zm-90.5-54.8c-.2-1.5 1.1-2.8 3-3.2c1.9-.2 3.7 .6 3.9 1.9c.3 1.3-1 2.6-3 3c-1.9 .4-3.7-.4-3.9-1.7zm-9.1 3.2c-2.2 .2-3.7-.9-3.7-2.4c0-1.3 1.5-2.4 3.5-2.4c1.9-.2 3.7 .9 3.7 2.4c0 1.3-1.5 2.4-3.5 2.4zm-14.3-2.2c-1.9-.4-3.2-1.9-2.8-3.2s2.4-1.9 4.1-1.5c2 .6 3.3 2.1 2.8 3.4c-.4 1.3-2.4 1.9-4.1 1.3zm-12.5-7.3c-1.5-1.3-1.9-3.2-.9-4.1c.9-1.1 2.8-.9 4.3 .6c1.3 1.3 1.8 3.3 .9 4.1c-.9 1.1-2.8 .9-4.3-.6zm-8.5-10c-1.1-1.5-1.1-3.2 0-3.9c1.1-.9 2.8-.2 3.7 1.3c1.1 1.5 1.1 3.3 0 4.1c-.9 .6-2.6 0-3.7-1.5zm-6.3-8.8c-1.1-1.3-1.3-2.8-.4-3.5c.9-.9 2.4-.4 3.5 .6c1.1 1.3 1.3 2.8 .4 3.5c-.9 .9-2.4 .4-3.5-.6zm-6-6.4c-1.3-.6-1.9-1.7-1.5-2.6c.4-.6 1.5-.9 2.8-.4c1.3 .7 1.9 1.8 1.5 2.6c-.4 .9-1.7 1.1-2.8 .4z"})}),X:(0,d.jsx)("svg",{className:"size-8",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm297.1 84L257.3 234.6 379.4 396H283.8L209 298.1 123.3 396H75.8l111-126.9L69.7 116h98l67.7 89.5L313.6 116h47.5zM323.3 367.6L153.4 142.9H125.1L296.9 367.6h26.3z"})})};async function s({data:a,siteRepresentation:b}){if(!a||!b)return(0,d.jsx)("div",{className:"bg-neutral-950",children:(0,d.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,d.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "Footer" component.'})})});let{statement:c,copyright:e}=a,{isOrganization:f,siteName:k,schedulingLink:s,socialChannels:t,businessHours:u,addressLocality:v,areaServed:w}=b;return(0,d.jsxs)("footer",{children:[(0,d.jsx)(l(),{className:"text-white/75 text-base transition hover:underline bg-neutral-800 block text-center px-4 py-2",href:"#",children:"Back to top"}),(0,d.jsxs)("h2",{className:"sr-only",children:[k," footer"]}),(0,d.jsx)("div",{className:"bg-neutral-950",children:(0,d.jsxs)("div",{className:"mx-auto max-w-5xl px-4 py-24",children:[(0,d.jsxs)("div",{className:"gap-8 grid grid-cols-1 md:grid-cols-5 mb-8",children:[(0,d.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,d.jsx)("h3",{className:"text-white font-medium text-xl tracking-tight text-center md:text-start",children:"Statement"}),(0,d.jsx)("p",{className:"mt-4 mb-6 text-white/75 text-base text-center md:text-start",children:c}),(0,d.jsxs)("div",{className:"flex justify-center md:justify-start gap-4",children:[(0,d.jsx)(l(),{href:"https://www.w3.org/WAI/WCAG2AA-Conformance",rel:"noopener noreferrer",target:"_blank",title:"Explanation of WCAG 2 Level AA conformance",children:(0,d.jsx)(o.default,{className:"grayscale hover:grayscale-0 transition",src:"/wcag2AA-blue-v.svg",height:28,width:80,alt:"Level AA conformance, W3C WAI Web Content Accessibility Guidelines 2.0"})}),(0,d.jsx)(l(),{href:"/privacy-policy/",target:"_blank",title:"Read our privacy policy",children:(0,d.jsx)(o.default,{className:"grayscale hover:grayscale-0 transition",src:"/gdpr-badge.svg",height:28,width:80,alt:"GDPR compliance badge"})})]}),t.length>0&&(0,d.jsxs)("div",{className:"mt-8",children:[(0,d.jsxs)("h3",{className:"text-white font-medium text-xl tracking-tight text-center md:text-start",children:["Follow ",f?"us":"me"]}),t.length>0&&(0,d.jsx)("ul",{className:"mt-5 flex justify-center gap-3 md:justify-start",children:t.map(a=>(0,d.jsx)("li",{children:(0,d.jsxs)(l(),{href:a.url,rel:"noopener noreferrer",target:"_blank",className:"text-white/75 transition hover:text-white block",children:[(0,d.jsx)("span",{className:"sr-only",children:a.label}),r[a.channel]||(0,d.jsx)("span",{className:"text-red-500",children:"Icon not found"})]})},a.id))})]})]}),(0,d.jsx)(p.default,{}),(0,d.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,d.jsx)("h3",{className:"text-white font-medium text-xl tracking-tight text-center md:text-start",children:"Location & contact"}),(0,d.jsx)("h4",{className:"sr-only",children:"Location information"}),(0,d.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,d.jsx)("li",{children:(0,d.jsxs)("p",{className:"flex items-center justify-center gap-1.5 md:justify-start group text-base text-white/75",children:[(0,d.jsx)(j.A,{className:"h-[1.2em] w-[1.2em] shrink-0"}),(0,d.jsxs)("span",{className:"truncate",children:["Based in ",v]})]})}),f&&w&&(0,d.jsx)("li",{children:(0,d.jsxs)("p",{className:"flex items-center justify-center gap-1.5 md:justify-start group text-base text-white/75",children:[(0,d.jsx)(i.A,{className:"h-[1.2em] w-[1.2em] shrink-0"}),(0,d.jsxs)("span",{className:"truncate",children:["Serving ",w]})]})})]}),(0,d.jsx)("div",{className:"h-px w-1/4 bg-white/15 mx-auto md:mx-0 my-6"}),(0,d.jsx)("h4",{className:"sr-only",children:"Contact methods"}),(0,d.jsxs)("ul",{className:"space-y-4",children:[(0,d.jsx)("li",{children:(0,d.jsx)(m.default,{children:(0,d.jsx)(n.default,{type:"email",className:"flex md:inline-flex items-center justify-center gap-1.5 group text-base text-white/75 hover:underline",showIcon:!0})})}),(0,d.jsx)("li",{children:(0,d.jsx)(m.default,{children:(0,d.jsx)(n.default,{type:"telephone",className:"flex md:inline-flex items-center justify-center gap-1.5 group text-base text-white/75 hover:underline",showIcon:!0})})}),s&&(0,d.jsx)("li",{children:(0,d.jsxs)(l(),{className:"flex md:inline-flex items-center justify-center gap-1.5 group text-base text-white/75",href:s,rel:"noopener noreferrer",target:"_blank",children:[(0,d.jsx)(g.A,{className:"h-[1.2em] w-[1.2em] shrink-0"}),(0,d.jsx)("span",{className:"group-hover:underline",children:"Schedule a call"})]})})]}),f&&u&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"h-px w-1/4 bg-white/15 mx-auto md:mx-0 my-6"}),(0,d.jsx)("h4",{className:"sr-only",children:"Business hours"}),(0,d.jsxs)("p",{className:"flex items-center justify-center gap-1.5 md:justify-start group text-base text-white/75",children:[(0,d.jsx)(h.A,{className:"h-[1.2em] w-[1.2em] shrink-0"}),(0,d.jsx)("span",{className:"truncate",children:u})]})]})]})]}),(0,d.jsx)("div",{className:"h-px bg-white/15 my-10"}),(0,d.jsx)(q.default,{copyright:e})]})})]})}var t=c(41810);async function u({data:a}){if(!a)return(0,d.jsx)("div",{className:"bg-primary-900 bg-dot-white/20",children:(0,d.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,d.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "Call-to-action" component.'})})});let{headline:b,supportiveText:c,button:e}=a;return(0,d.jsx)("section",{className:"bg-primary-900 bg-dot-white/20 relative",children:(0,d.jsxs)("div",{className:"mx-auto max-w-5xl px-4 py-16 text-center",children:[(0,d.jsx)("h2",{className:"text-white font-bold text-3xl sm:text-4xl tracking-tight mb-4",children:b}),(0,d.jsx)("p",{className:"text-white/75 text-lg mb-6",children:c}),(0,d.jsx)("div",{className:"flex items-center justify-center gap-x-4",children:(0,d.jsx)(t.default,{target:e.openLinkInNewTab?"_blank":void 0,rel:e.sameHostLink?void 0:"noopener noreferrer",label:e.label,url:e.url,showIcon:e.showIcon,iconType:e.iconType})})]})})}var v=c(33820),w=c(37522);async function x(){let a;try{a=await (0,w.Xp)()}catch(a){return console.error(a.message),{}}return{themeColor:a.miscellaneous.themeColor}}async function y(){let a;try{a=await (0,w.Xp)()}catch(a){return console.error(a.message),{description:"Description",openGraph:{siteName:"Site Name",images:["https://placehold.co/1200x630.jpg?text=Fallback+Image"]}}}let{siteRepresentation:b,icons:c,miscellaneous:d}=a,{siteName:e,siteDescription:f,siteImage:g}=b,{iconICO:h,iconSVG:i,iconPNG:j}=c,{localeString:k}=d,l=new URL(g.url,"https://api.yourdomain.com").href,m=new URL(h.url,"https://api.yourdomain.com").href,n=new URL(j.url,"https://api.yourdomain.com").href,o=new URL(i.url,"https://api.yourdomain.com").href;return{description:f,openGraph:{locale:k.replace("-","_"),siteName:e,images:[l]},icons:{icon:[{url:m,sizes:"32x32"},{url:o,type:"image/svg+xml"}],apple:[{url:n}]}}}async function z({children:a}){let b=null;try{b=await (0,w.Xp)()}catch(a){console.error(a.message),b={announcement:null,header:null,cta:null,footer:null,siteRepresentation:null,miscellaneous:{htmlLanguageTag:"en"}}}let{announcement:c,header:g,cta:h,footer:i,siteRepresentation:j,miscellaneous:k}=b;return(0,d.jsx)(v.ViewTransitions,{children:(0,d.jsx)("html",{lang:k.htmlLanguageTag,children:(0,d.jsxs)("body",{className:"antialiased text-gray-500 text-base",children:[(0,d.jsx)(e.default,{data:c}),(0,d.jsx)(f.default,{data:g,siteRepresentation:j}),(0,d.jsx)("main",{className:"relative",children:a}),(0,d.jsx)(u,{data:h}),(0,d.jsx)(s,{data:i,siteRepresentation:j})]})})})}},74098:(a,b,c)=>{Promise.resolve().then(c.bind(c,80386))},76830:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(21124),e=c(3420),f=c(53476);let g=({type:a,className:b,showIcon:c=!0,...g})=>{let h,i,j=atob("your_base64_encoded_email").trim(),k=atob("your_base64_encoded_phone").trim(),l=k?k.replace(/[^\d+]/g,""):null;if("email"===a)h=j?`mailto:${j}`:"#",i=j||"Email not available";else{if("telephone"!==a)return console.error(`Invalid type "${a}" passed to ContactLink.`),null;h=l?`tel:${l}`:"#",i=k||"Phone not available"}let m=c&&"email"===a?e.A:c&&"telephone"===a?f.A:null;return(0,d.jsxs)("a",{className:b,href:h,...g,children:[m&&(0,d.jsx)(m,{className:"h-[1.2em] w-[1.2em] shrink-0"})," ",(0,d.jsx)("span",{children:i})]})}},80386:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(21124),e=c(72893),f=c(24086),g=c(3991),h=c.n(g),i=c(18062),j=c(27832);function k({label:a="Default label",url:b="#",target:c,rel:g,className:k="",showIcon:l=!1,iconType:m="arrowRight",...n}){let o=(0,i.o)();return(0,d.jsxs)(h(),{target:c,rel:g,href:b,onClick:a=>{a.preventDefault(),o.push(b,{onTransitionReady:j.W})},className:`
        group
        inline-flex
        justify-center
        items-center
        transition
        px-4
        h-11
        font-medium
        leading-none
        rounded-full
        text-white
        border border-primary-700
        hover:border-primary-600
        active:border-primary-500
        bg-primary-700
        hover:bg-primary-600
        active:bg-primary-500
        ${k}
      `,...n,children:[a,l?"arrowUpRight"===m?(0,d.jsx)(f.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):(0,d.jsx)(e.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):null]})}},80636:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnPrimary.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/BtnPrimary.jsx","default")},82704:()=>{},92103:(a,b,c)=>{Promise.resolve().then(c.bind(c,92113)),Promise.resolve().then(c.bind(c,41588)),Promise.resolve().then(c.bind(c,76830)),Promise.resolve().then(c.bind(c,51746)),Promise.resolve().then(c.bind(c,12951)),Promise.resolve().then(c.bind(c,13903)),Promise.resolve().then(c.bind(c,70512)),Promise.resolve().then(c.bind(c,18062)),Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.t.bind(c,40106,23))},92113:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(21124),e=c(3350),f=c.n(e),g=c(51498),h=c(26452),i=c(38301);function j({data:a}){let[b,c]=(0,i.useState)(null);if(!a)return(0,d.jsx)("div",{className:"bg-neutral-950 relative z-[10000]",children:(0,d.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,d.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "Announcement" component.'})})});if(!b)return null;let{content:e}=a;return e?(0,d.jsx)("aside",{className:"bg-neutral-950 relative z-[10000]",children:(0,d.jsxs)("div",{className:"flex items-center justify-center gap-3 mx-auto max-w-screen-xl text-white pl-[56px] pr-4 py-2",children:[(0,d.jsx)("div",{className:"text-center prose prose-sm leading-snug prose-invert prose-modifier !max-w-none",dangerouslySetInnerHTML:{__html:f().sanitize(g.xI.parse(e))}}),(0,d.jsx)("button",{"aria-label":"Dismiss announcement",className:" p-1 rounded-full bg-white/20 transition hover:bg-white/25 active:bg-white/30 ",onClick:()=>{c(!1),localStorage.setItem("announcementDismissed","true")},children:(0,d.jsx)(h.A,{className:"size-5"})})]})}):null}}};