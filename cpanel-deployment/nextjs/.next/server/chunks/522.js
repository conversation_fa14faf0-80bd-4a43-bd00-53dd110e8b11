"use strict";exports.id=522,exports.ids=[522],exports.modules={37522:(a,b,c)=>{c.d(b,{bB:()=>I,C8:()=>M,ky:()=>Q,WK:()=>E,s4:()=>F,W$:()=>R,Ts:()=>N,pt:()=>C,xF:()=>J,Xp:()=>B,p3:()=>H,V5:()=>K,__:()=>L,MU:()=>G,CH:()=>O,zC:()=>P,Y6:()=>D});var d=c(45711);let e=d.Ik({alternativeText:d.Yj().nullable(),width:d.ai().nullable(),height:d.ai().nullable(),url:d.Yj()}),f=d.Ik({headline:d.Yj(),supportiveText:d.Yj()}),g=d.Ik({title:d.Yj().nullable(),description:d.Yj().nullable(),image:e.nullable()}),h=d.Ik({id:d.ai(),channel:d.Yj().refine(a=>"GitHub"===a||"LinkedIn"==a||"X"===a,{message:"Value must be 'GitHub', 'LinkedIn' or 'X'"}),url:d.Yj(),label:d.Yj()}),i=d.Ik({id:d.ai(),label:d.Yj(),url:d.Yj(),openLinkInNewTab:d.zM(),sameHostLink:d.zM(),showIcon:d.zM(),iconType:d.k5(["arrowRight","arrowUpRight"])}),j=d.Ik({headline:d.Yj(),supportiveText:d.Yj()}),k=d.Ik({id:d.ai(),authorName:d.Yj(),isOrganization:d.zM(),url:d.Yj()}).nullable(),l=d.Ik({id:d.ai(),title:d.Yj(),slug:d.Yj(),excerpt:d.Yj(),content:d.Yj(),createdAt:d.Yj().datetime(),updatedAt:d.Yj().datetime(),featuredImage:e,author:k}),m=d.Ik({id:d.ai(),title:d.Yj(),slug:d.Yj(),excerpt:d.Yj(),demoUrl:d.Yj().nullable(),repoUrl:d.Yj().nullable(),content:d.Yj(),duration:d.Yj(),featuredImage:e,scopes:d.YO(d.Ik({id:d.ai(),title:d.Yj()})),tools:d.YO(d.Ik({id:d.ai(),title:d.Yj()})),designFile:d.Ik({url:d.Yj()}).nullable(),author:k}),n=d.Ik({data:d.YO(l)}),o=d.Ik({data:d.YO(m)}),p=d.Ik({data:d.Ik({announcement:d.Ik({content:d.Yj().nullable()}),header:d.Ik({additionalNavigationItems:d.YO(i),cta:i}),cta:j.extend({button:i}),footer:d.Ik({statement:d.Yj(),copyright:d.Yj()}),siteRepresentation:d.Ik({isOrganization:d.zM(),siteName:d.Yj(),siteDescription:d.Yj(),siteImage:e,jobTitle:d.Yj().nullable(),schedulingLink:d.Yj().nullable(),logo:e,logomark:e,socialChannels:d.YO(h),addressLocality:d.Yj(),areaServed:d.Yj().nullable(),businessHours:d.Yj().nullable(),knowsAbout:d.YO(d.Ik({name:d.Yj(),children:d.YO(d.Ik({name:d.Yj(),value:d.ai()}))}))}),miscellaneous:d.Ik({localeString:d.Yj(),htmlLanguageTag:d.Yj(),themeColor:d.Yj()}),icons:d.Ik({iconICO:e,iconSVG:e,iconPNG:e})})}),q=d.Ik({data:d.Ik({metadata:g,hero:j.extend({greeting:d.Yj().nullable(),primaryButton:i.nullable(),secondaryButton:i.nullable()}),about:j.extend({content:d.Yj(),image:e}),featuredProjects:j,skills:j,testimonials:j.extend({testimonialList:d.YO(d.Ik({id:d.ai(),statement:d.Yj(),author:d.Yj(),role:d.Yj(),company:d.Yj(),companyWebsite:d.Yj()})).nonempty()}),faq:j.extend({faqList:d.YO(d.Ik({id:d.ai(),question:d.Yj(),answer:d.Yj()})).nonempty()}),latestPosts:j,useCaseSpecificContent:d.YO(d.gM("__component",[j.extend({__component:d.eu("sections.services"),serviceList:d.YO(d.Ik({id:d.ai(),description:d.Yj(),title:d.Yj()})).nonempty()}),j.extend({__component:d.eu("sections.experience"),experienceList:d.YO(d.Ik({id:d.ai(),role:d.Yj(),company:d.Yj(),companyUrl:d.Yj().nullable(),duration:d.Yj(),location:d.Yj(),description:d.Yj(),content:d.Yj(),companyLogo:e})).nonempty()})]))})}),r=d.Ik({data:d.Ik({metadata:g,banner:f})}),s=d.Ik({data:d.Ik({metadata:g,banner:f})}),t=d.Ik({data:d.Ik({metadata:g,banner:f,contactFormHeading:d.Yj(),otherContactOptionsHeading:d.Yj()})}),u=d.Ik({data:d.Ik({metadata:g,banner:f,content:d.Yj()})}),v=d.Ik({data:d.Ik({metadata:g,banner:f})});d.Ik({name:d.Yj().trim(),email:d.Yj().trim().nonempty({message:"Email is required."}).email({message:"Invalid email address."}),message:d.Yj().trim().nonempty({message:"Message is required."}).min(10,{message:"Message must be at least 10 characters long."}),consent:d.zM().refine(a=>!0===a,{message:"Consent is required."})});let w=d.Ik({data:d.YO(d.Ik({slug:d.Yj()}))}),x=d.Ik({data:d.YO(d.Ik({title:d.Yj(),excerpt:d.Yj(),featuredImage:e}))}),y=c(34563);async function z(a){let b=process.env.STRAPI_READ_ONLY_TOKEN,c=new URL(a,"https://api.yourdomain.com").href,d={headers:{"Content-Type":"application/json",Authorization:`Bearer ${b}`},cache:"force-cache"};try{let a=await fetch(c,d);if(!a.ok)throw Error(`Failed to fetch data: ${a.status} ${a.statusText}`);return await a.json()}catch(b){throw console.error(`Error fetching data from ${a}: ${b.message}`),Error(`Unable to fetch data from ${a}.`)}}async function A(a,b,c){let d=b.safeParse(a);if(!d.success)throw console.error(`Validation failed for ${c}:`,d.error),Error(`Invalid data received from ${c}`);return d.data}let B=async()=>{let a=y.stringify({populate:{siteRepresentation:{populate:"*"},icons:{populate:"*"},announcement:!0,header:{populate:"*"},cta:{populate:"*"},footer:{populate:"*"},miscellaneous:{populate:"*"}}},{encodeValuesOnly:!0}),b=`/api/global?${a}`,c=await z(b),d=await A(c,p,b);return{announcement:d.data.announcement,header:d.data.header,cta:d.data.cta,footer:d.data.footer,siteRepresentation:d.data.siteRepresentation,miscellaneous:d.data.miscellaneous,icons:d.data.icons}},C=async()=>{let a=y.stringify({populate:{metadata:{populate:"*"},hero:{populate:"*"},about:{populate:"*"},featuredProjects:!0,skills:!0,testimonials:{populate:"*"},faq:{populate:"*"},latestPosts:!0,useCaseSpecificContent:{on:{"sections.experience":{populate:{experienceList:{populate:"*"}}},"sections.services":{populate:"*"}}}}},{encodeValuesOnly:!0}),b=`/api/homepage?${a}`,c=await z(b),d=await A(c,q,b);return{metadata:d.data.metadata,hero:d.data.hero,about:d.data.about,featuredProjects:d.data.featuredProjects,skills:d.data.skills,testimonials:d.data.testimonials,faq:d.data.faq,latestPosts:d.data.latestPosts,useCaseSpecificContent:d.data.useCaseSpecificContent}},D=async()=>{let a=y.stringify({populate:{metadata:{populate:"*"},banner:!0}},{encodeValuesOnly:!0}),b=`/api/projects-page?${a}`,c=await z(b),d=await A(c,r,b);return{metadata:d.data.metadata,banner:d.data.banner}},E=async()=>{let a=y.stringify({populate:{metadata:{populate:"*"},banner:!0}},{encodeValuesOnly:!0}),b=`/api/blog-page?${a}`,c=await z(b),d=await A(c,s,b);return{metadata:d.data.metadata,banner:d.data.banner}},F=async()=>{let a=y.stringify({populate:{metadata:{populate:"*"},banner:!0},fields:["contactFormHeading","otherContactOptionsHeading"]},{encodeValuesOnly:!0}),b=`/api/contact-page?${a}`,c=await z(b),d=await A(c,t,b);return{contactFormHeading:d.data.contactFormHeading,otherContactOptionsHeading:d.data.otherContactOptionsHeading,metadata:d.data.metadata,banner:d.data.banner}},G=async()=>{let a=y.stringify({populate:{metadata:{populate:"*"},banner:!0},fields:["content"]},{encodeValuesOnly:!0}),b=`/api/privacy-policy?${a}`,c=await z(b),d=await A(c,u,b);return{metadata:d.data.metadata,banner:d.data.banner,content:d.data.content}},H=async()=>{let a=y.stringify({populate:{metadata:{populate:"*"},banner:!0}},{encodeValuesOnly:!0}),b=`/api/not-found?${a}`,c=await z(b),d=await A(c,v,b);return{metadata:d.data.metadata,banner:d.data.banner}},I=async()=>{let a=y.stringify({populate:"*",sort:["createdAt:desc"],pagination:{pageSize:100,page:1}},{encodeValuesOnly:!0}),b=`/api/posts?${a}`,c=await z(b);return(await A(c,n,b)).data},J=async()=>{let a=y.stringify({populate:"*",sort:["createdAt:desc"],pagination:{start:0,limit:3}},{encodeValuesOnly:!0}),b=`/api/posts?${a}`,c=await z(b);return(await A(c,n,b)).data},K=async a=>{let b=y.stringify({populate:"*",filters:{slug:{$eq:a}}},{encodeValuesOnly:!0}),c=`/api/posts?${b}`,d=await z(c),e=await A(d,n,c);if(!e.data||0===e.data.length)return null;let f=e.data[0];return{id:f.id,title:f.title,slug:f.slug,excerpt:f.excerpt,content:f.content,createdAt:f.createdAt,updatedAt:f.updatedAt,featuredImage:f.featuredImage,author:f.author}},L=async()=>{let a=y.stringify({populate:"*"},{encodeValuesOnly:!0}),b=`/api/posts?${a}`,c=await z(b);return(await A(c,n,b)).data.map(a=>({slug:a.slug,updatedAt:a.updatedAt}))},M=async()=>{let a=y.stringify({populate:"*",sort:["order:asc"],pagination:{pageSize:100,page:1}},{encodeValuesOnly:!0}),b=`/api/projects?${a}`,c=await z(b);return(await A(c,o,b)).data},N=async()=>{let a=y.stringify({populate:"*",filters:{isFeatured:{$eq:!0}},sort:["order:asc"]},{encodeValuesOnly:!0}),b=`/api/projects?${a}`,c=await z(b);return(await A(c,o,b)).data},O=async a=>{let b=y.stringify({populate:"*",filters:{slug:{$eq:a}}},{encodeValuesOnly:!0}),c=`/api/projects?${b}`,d=await z(c),e=await A(d,o,c);return e.data&&0!==e.data.length?{author:e.data[0].author,title:e.data[0].title,excerpt:e.data[0].excerpt,duration:e.data[0].duration,demoUrl:e.data[0].demoUrl,repoUrl:e.data[0].repoUrl,content:e.data[0].content,featuredImage:e.data[0].featuredImage,scopes:e.data[0].scopes,tools:e.data[0].tools,designFile:e.data[0].designFile}:null},P=async()=>{let a=y.stringify({populate:"*"},{encodeValuesOnly:!0}),b=`/api/projects?${a}`,c=await z(b);return(await A(c,o,b)).data.map(a=>({slug:a.slug,updatedAt:a.updatedAt}))},Q=async a=>{let b=y.stringify({fields:["slug"]},{encodeValuesOnly:!0}),c=`/api/${a}?${b}`,d=await z(c);return(await A(d,w,c)).data.map(a=>({slug:a.slug}))},R=async(a,b)=>{let c=y.stringify({fields:["title","excerpt"],populate:{featuredImage:!0},filters:{slug:{$eq:b}}},{encodeValuesOnly:!0}),d=`/api/${a}?${c}`,e=await z(d),f=await A(e,x,d);return{title:f.data[0].title,description:f.data[0].excerpt,image:f.data[0].featuredImage}}}};