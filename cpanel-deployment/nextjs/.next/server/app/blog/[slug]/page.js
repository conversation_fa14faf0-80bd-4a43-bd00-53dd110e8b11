(()=>{var a={};a.id=953,a.ids=[953],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9878:(a,b,c)=>{Promise.resolve().then(c.bind(c,76440)),Promise.resolve().then(c.t.bind(c,3991,23)),Promise.resolve().then(c.t.bind(c,40106,23))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32325:a=>{"use strict";a.exports=require("jsdom")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46830:(a,b,c)=>{Promise.resolve().then(c.bind(c,91218)),Promise.resolve().then(c.t.bind(c,65169,23)),Promise.resolve().then(c.t.bind(c,76424,23))},59127:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u,generateMetadata:()=>t,generateStaticParams:()=>s});var d=c(75338),e=c(11088),f=c.n(e),g=c(56572),h=c(28605),i=c(59865),j=c(83492),k=c(91218),l=c(82161),m=c(37522);let n=(a,b="en-US")=>new Intl.DateTimeFormat(b,{dateStyle:"short"}).format(new Date(a));var o=c(56130),p=c.n(o);c(15537),c(2786),c(67357),c(7840),c(85105),c(197);let q={ADD_TAGS:["iframe"],ADD_ATTR:["src","style","title","allow","sandbox","width","height","frameborder","allowfullscreen"]},r=new g.Dz((0,h.x)({langPrefix:"language-",highlight(a,b){let c=p().languages[b]?b:"plaintext";return p().highlight(a,p().languages[c],c)}}));async function s(){try{return await (0,m.ky)("posts")}catch(a){return console.error(a.message),[]}}async function t({params:a},b){let c,d=(await a).slug;try{c=await (0,m.W$)("posts",d)}catch(a){return console.error(a.message),{}}let e=await b,{title:f,description:g,image:h}=c,i=new URL(`/blog/${d}/`,"https://yourdomain.com").href,j=new URL(h.url,"https://api.yourdomain.com").href;return{title:`${f} | ${e.openGraph.siteName}`,description:g,openGraph:{...e.openGraph,images:[j],url:i,type:"article"},alternates:{canonical:i}}}async function u(a){let b=(await a.params).slug,[c,e]=await Promise.allSettled([(0,m.V5)(b),(0,m.Xp)()]);if("rejected"===c.status)return(0,d.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,d.jsx)("div",{className:"text-red-600 text-center",children:"Error: We encountered an issue while loading the blog post."})});c.value||(0,l.notFound)();let{title:g,excerpt:h,content:o,createdAt:p,updatedAt:s,featuredImage:t,author:u}=c.value,v=new URL(t.url,"https://api.yourdomain.com").href,w="en-US",x=null;if("fulfilled"===e.status){let{siteRepresentation:a,miscellaneous:c}=e.value,{siteImage:d,logo:f,knowsAbout:i,isOrganization:j,siteName:k,siteDescription:l,jobTitle:m,schedulingLink:n,socialChannels:o,addressLocality:q,areaServed:r}=a,t=new URL(d.url,"https://api.yourdomain.com").href,y=new URL(f.url,"https://api.yourdomain.com").href,z=i.flatMap(a=>a.children.map(a=>a.name)),{htmlLanguageTag:A}=c;w=c.localeString,x={"@context":"https://schema.org","@graph":[{"@type":"BlogPosting","@id":new URL(`/blog/${b}/#blogposting`,"https://yourdomain.com").href,headline:g,description:h,datePublished:p,dateModified:s,image:v,inLanguage:A,...u?{author:{"@type":u.isOrganization?"Organization":"Person",name:u.authorName,url:u.url}}:{},publisher:{"@id":j?new URL("/#organization","https://yourdomain.com").href:new URL("/#person","https://yourdomain.com").href},isPartOf:{"@id":new URL(`/blog/${b}/`,"https://yourdomain.com").href},mainEntityOfPage:{"@id":new URL(`/blog/${b}/`,"https://yourdomain.com").href}},{"@type":"WebPage","@id":new URL(`/blog/${b}/`,"https://yourdomain.com").href,name:`${g} | ${k}`,description:h,url:new URL(`/blog/${b}/`,"https://yourdomain.com").href,inLanguage:A,isPartOf:{"@id":new URL("/#website","https://yourdomain.com").href}},{"@type":"WebSite","@id":new URL("/#website","https://yourdomain.com").href,url:new URL("/","https://yourdomain.com").href,name:k,description:l,inLanguage:A,publisher:{"@id":j?new URL("/#organization","https://yourdomain.com").href:new URL("/#person","https://yourdomain.com").href}},{"@type":j?"Organization":"Person","@id":j?new URL("/#organization","https://yourdomain.com").href:new URL("/#person","https://yourdomain.com").href,name:k,description:l,url:new URL("/","https://yourdomain.com").href,contactPoint:{"@type":"ContactPoint",url:new URL("/contact/","https://yourdomain.com").href},...j&&{logo:y},image:t,...!j&&{jobTitle:m},...n||o.length>0?{sameAs:[...n?[n]:[],...o.map(a=>a.url)]}:{},knowsAbout:z,address:{"@type":"PostalAddress",addressLocality:q},...j&&r&&{areaServed:r}}]}}let y=n(p,w),z=n(s,w);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(x)}}),(0,d.jsx)(j.A,{label:"Back to Blog",url:"/blog/"}),(0,d.jsx)("div",{className:"mx-auto max-w-5xl px-4",children:(0,d.jsxs)("article",{children:[(0,d.jsxs)("header",{children:[(0,d.jsx)("h1",{className:"text-gray-900 font-bold text-3xl md:text-4xl tracking-tight mb-3",children:g}),(0,d.jsx)("p",{className:"text-gray-700 font-light leading-7 sm:text-xl mb-4",children:h}),(0,d.jsxs)("div",{className:"text-sm mb-12",children:[u&&(0,d.jsxs)("div",{className:"text-gray-900",children:["By ",u.authorName]}),(0,d.jsxs)("div",{children:["Published ",(0,d.jsx)("time",{dateTime:p,children:y}),y!==z&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"px-1",children:"\xb7"}),"Updated ",(0,d.jsx)("time",{dateTime:s,children:z})]})]})]}),(0,d.jsx)(i.default,{className:"mb-12 rounded-2xl overflow-hidden w-full border border-neutral-100",priority:!0,src:v,alt:t.alternativeText??"",width:1468,height:769,sizes:"(max-width: 1024px) calc(100vw - 34px), 990px"})]}),(0,d.jsxs)("div",{className:"mx-auto prose prose-gray prose-modifier",children:[(0,d.jsx)("div",{className:"[&>*:first-child]:mt-0",dangerouslySetInnerHTML:{__html:f().sanitize(r.parse(o),q)}}),(0,d.jsx)("hr",{}),(0,d.jsx)(k.default,{})]})]})}),(0,d.jsx)(j.A,{label:"Back to Blog",url:"/blog/"})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76440:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(21124),e=c(42378),f=c(26285);let g={Envelope:(0,d.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM218 271.7L64.2 172.4C66 156.4 79.5 144 96 144l256 0c16.5 0 30 12.4 31.8 28.4L230 271.7c-1.8 1.2-3.9 1.8-6 1.8s-4.2-.6-6-1.8zm29.4 26.9L384 210.4 384 336c0 17.7-14.3 32-32 32L96 368c-17.7 0-32-14.3-32-32l0-125.6 136.6 88.2c7 4.5 15.1 6.9 23.4 6.9s16.4-2.4 23.4-6.9z"})}),Facebook:(0,d.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64h98.2V334.2H109.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H255V480H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z"})}),LinkedIn:(0,d.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"})}),X:(0,d.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm297.1 84L257.3 234.6 379.4 396H283.8L209 298.1 123.3 396H75.8l111-126.9L69.7 116h98l67.7 89.5L313.6 116h47.5zM323.3 367.6L153.4 142.9H125.1L296.9 367.6h26.3z"})})};function h(){let a=new URL((0,e.usePathname)(),"https://yourdomain.com").href;return(0,d.jsxs)("dl",{className:"flex flex-col gap-2 not-prose",children:[(0,d.jsx)("dt",{className:"text-gray-900 font-medium",children:"Share this page"}),(0,d.jsxs)("dd",{className:"flex flex-wrap gap-3",children:[(0,d.jsx)(f.r6,{url:a,"aria-label":"Share on X",children:g.X||(0,d.jsx)("span",{className:"text-red-500",children:"Icon not found"})}),(0,d.jsx)(f.wk,{url:a,"aria-label":"Share on LinkedIn",children:g.LinkedIn||(0,d.jsx)("span",{className:"text-red-500",children:"Icon not found"})}),(0,d.jsx)(f.uI,{url:a,"aria-label":"Share on Facebook",children:g.Facebook||(0,d.jsx)("span",{className:"text-red-500",children:"Icon not found"})}),(0,d.jsx)(f.Ot,{url:a,"aria-label":"Share via Email",children:g.Envelope||(0,d.jsx)("span",{className:"text-red-500",children:"Icon not found"})})]})]})}},83492:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(75338),e=c(65169),f=c.n(e),g=c(51802);function h({label:a="Default label",url:b="#",...c}){return(0,d.jsx)("div",{className:"px-4 py-10",children:(0,d.jsxs)(f(),{href:b,...c,className:" group flex font-medium leading-none text-primary-700 ",children:[(0,d.jsx)(g.A,{className:"relative h-[1em] w-[1em] me-1 group-hover:-translate-x-0.5 transition"}),(0,d.jsx)("span",{className:"relative",children:a})]})})}},83989:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,59127)),"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/blog/[slug]/page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,70541)),"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/layout.jsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,47381)),"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/not-found.jsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/app/blog/[slug]/page.jsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/blog/[slug]/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91218:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/SocialShare.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Personal/portfolio/nextjs-strapi-portfolio-starter/next/components/SocialShare.jsx","default")},91645:a=>{"use strict";a.exports=require("net")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[331,353,269,713,522,929],()=>b(b.s=83989));module.exports=c})();