(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[529],{65:(t,e,n)=>{"use strict";n.d(e,{w:()=>a});var r=n(882),i=n(4123),o=/cubic-bezier\(([0-9,\.e ]+)\)/;function a(t){var e=t&&o.exec(t);if(e){var n=e[1].split(","),a=+(0,i.trim)(n[0]),s=+(0,i.trim)(n[1]),u=+(0,i.trim)(n[2]),l=+(0,i.trim)(n[3]);if(isNaN(a+s+u+l))return;var c=[];return function(t){return t<=0?0:t>=1?1:(0,r._E)(0,a,u,1,t,c)&&(0,r.Yb)(0,s,l,1,c[0])}}}},169:(t,e,n)=>{"use strict";n.d(e,{Lu:()=>d,VB:()=>u,c8:()=>p,xb:()=>g});var r=n(4123),i=n(2392),o=n(6187),a=n(8991),s={};function u(t,e,n,i,o){var a={};return function(t,e,n,i,o){n=n||s;var a,u=e.ecModel,l=u&&u.option.textStyle,c=function(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||s).rich;if(n){e=e||{};for(var i=(0,r.keys)(n),o=0;o<i.length;o++)e[i[o]]=1}t=t.parentModel}return e}(e);if(c){for(var h in a={},c)if(c.hasOwnProperty(h)){var p=e.getModel(["rich",h]);f(a[h]={},p,l,n,i,o,!1,!0)}}a&&(t.rich=a);var d=e.get("overflow");d&&(t.overflow=d);var g=e.get("minMargin");null!=g&&(t.margin=g),f(t,e,l,n,i,o,!0,!1)}(a,t,n,i,o),e&&(0,r.extend)(a,e),a}var l=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],c=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],h=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function f(t,e,n,i,o,a,u,f){n=!o&&n||s;var p=i&&i.inheritColor,d=e.getShallow("color"),g=e.getShallow("textBorderColor"),y=(0,r.retrieve2)(e.getShallow("opacity"),n.opacity);("inherit"===d||"auto"===d)&&(d=p||null),("inherit"===g||"auto"===g)&&(g=p||null),a||(d=d||n.color,g=g||n.textBorderColor),null!=d&&(t.fill=d),null!=g&&(t.stroke=g);var v=(0,r.retrieve2)(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=v&&(t.lineWidth=v);var m=(0,r.retrieve2)(e.getShallow("textBorderType"),n.textBorderType);null!=m&&(t.lineDash=m);var _=(0,r.retrieve2)(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=_&&(t.lineDashOffset=_),o||null!=y||f||(y=i&&i.defaultOpacity),null!=y&&(t.opacity=y),o||a||null!=t.fill||!i.inheritColor||(t.fill=i.inheritColor);for(var x=0;x<l.length;x++){var b=l[x],w=(0,r.retrieve2)(e.getShallow(b),n[b]);null!=w&&(t[b]=w)}for(var x=0;x<c.length;x++){var b=c[x],w=e.getShallow(b);null!=w&&(t[b]=w)}if(null==t.verticalAlign){var S=e.getShallow("baseline");null!=S&&(t.verticalAlign=S)}if(!u||!i.disableBox){for(var x=0;x<h.length;x++){var b=h[x],w=e.getShallow(b);null!=w&&(t[b]=w)}var M=e.getShallow("borderType");null!=M&&(t.borderDash=M),("auto"===t.backgroundColor||"inherit"===t.backgroundColor)&&p&&(t.backgroundColor=p),("auto"===t.borderColor||"inherit"===t.borderColor)&&p&&(t.borderColor=p)}}function p(t,e){var n=e&&e.getModel("textStyle");return(0,r.trim)([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}var d=(0,o.$r)();function g(t,e,n,s,u){var l=d(t);if(l.valueAnimation&&l.prevValue!==l.value){var c=l.defaultInterpolatedText,h=(0,r.retrieve2)(l.interpolatedValue,l.prevValue),f=l.value;t.percent=0,(null==l.prevValue?a.LW:a.oi)(t,{percent:1},s,e,null,function(a){var s=(0,o.Il)(n,l.precision,h,f,a);l.interpolatedValue=1===a?null:s,function(t,e){for(var n=0;n<i.BV.length;n++){var r=i.BV[n],o=e[r],a=t.ensureState(r);a.style=a.style||{},a.style.text=o}var s=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(s,!0)}(t,function(t,e,n){var o,a=t.labelFetcher,s=t.labelDataIndex,u=t.labelDimIndex,l=e.normal;a&&(o=a.getFormattedLabel(s,"normal",null,u,l&&l.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==o&&(o=(0,r.isFunction)(t.defaultText)?t.defaultText(s,t,n):t.defaultText);for(var c={normal:o},h=0;h<i.BV.length;h++){var f=i.BV[h],p=e[f];c[f]=(0,r.retrieve2)(a?a.getFormattedLabel(s,f,null,u,p&&p.get("formatter")):null,o)}return c}({labelDataIndex:e,labelFetcher:u,defaultText:c?c(s):s+""},l.statesModels,s))})}}},179:(t,e,n)=>{"use strict";function r(t,e,n,r,i,o){if(o>e&&o>r||o<e&&o<r||r===e)return 0;var a=(o-e)/(r-e),s=r<e?1:-1;(1===a||0===a)&&(s=r<e?.5:-.5);var u=a*(n-t)+t;return u===i?1/0:u>i?s:0}n.d(e,{A:()=>r})},202:(t,e,n)=>{"use strict";n.d(e,{FQ:()=>eh,Ng:()=>eF,p5:()=>e7,El:()=>ec,zm:()=>eV,Zf:()=>ez,AS:()=>eH,Bo:()=>eQ,FP:()=>ej,aQ:()=>eU,ZB:()=>e6,Ts:()=>eB,OH:()=>e$,pX:()=>eK,Oh:()=>eJ,Ej:()=>e5,E:()=>er.E,mz:()=>e4,cf:()=>eX,tb:()=>eY,lP:()=>eG,qg:()=>eq,bf:()=>eW,iY:()=>e8,xV:()=>eZ,AF:()=>e0,vV:()=>e3,rE:()=>el});var r,i,o,a,s,u,l,c,h,f,p,d,g,y,v,m,_,x,b,w,S,M,A=n(643),T=n(7172),k=n(4123),C=n(7170),I=n(5951),D=n(8284),O=n(6187),P=n(1613),L=n(706),R="";"undefined"!=typeof navigator&&(R=navigator.platform||"");var E="rgba(0, 0, 0, 0.2)";let N={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:E,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:E,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:E,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:E,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:E,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:E,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:R.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var B=n(8142),F=(0,k.createHashMap)(),z=n(7156),V="\0_ec_inner",H=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,A.__extends)(e,t),e.prototype.init=function(t,e,n,r,i,o){r=r||{},this.option=null,this._theme=new P.A(r),this._locale=new P.A(i),this._optionManager=o},e.prototype.setOption=function(t,e,n){var r=W(e);this._optionManager.setOption(t,n,r),this._resetOption(null,r)},e.prototype.resetOption=function(t,e){return this._resetOption(t,W(e))},e.prototype._resetOption=function(t,e){var n=!1,r=this._optionManager;if(!t||"recreate"===t){var i=r.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(i,e)):o(this,i),n=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=r.getTimelineOption(this);a&&(n=!0,this._mergeOption(a,e))}if(!t||"recreate"===t||"media"===t){var s=r.getMediaOption(this);s.length&&(0,k.each)(s,function(t){n=!0,this._mergeOption(t,e)},this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,o=this._componentsCount,a=[],s=(0,k.createHashMap)(),u=e&&e.replaceMergeMainTypeMap;(0,B.ps)(this),(0,k.each)(t,function(t,e){null!=t&&(L.A.hasClass(e)?e&&(a.push(e),s.set(e,!0)):n[e]=null==n[e]?(0,k.clone)(t):(0,k.merge)(n[e],t,!0))}),u&&u.each(function(t,e){L.A.hasClass(e)&&!s.get(e)&&(a.push(e),s.set(e,!0))}),L.A.topologicalTravel(a,L.A.getAllClassMainTypes(),function(e){var a,s=function(t,e,n){var r=F.get(e);if(!r)return n;var i=r(t);return i?n.concat(i):n}(this,e,O.qB(t[e])),l=i.get(e),c=l?u&&u.get(e)?"replaceMerge":"normalMerge":"replaceAll",h=O.O2(l,s,c);O.GX(h,e,L.A),n[e]=null,i.set(e,null),o.set(e,0);var f=[],p=[],d=0;(0,k.each)(h,function(t,n){var r=t.existing,i=t.newOption;if(i){var o="series"===e,s=L.A.getClass(e,t.keyInfo.subType,!o);if(!s)return;if("tooltip"===e){if(a)return;a=!0}if(r&&r.constructor===s)r.name=t.keyInfo.name,r.mergeOption(i,this),r.optionUpdated(i,!1);else{var u=(0,k.extend)({componentIndex:n},t.keyInfo);r=new s(i,this,this,u),(0,k.extend)(r,u),t.brandNew&&(r.__requireNewView=!0),r.init(i,this,this),r.optionUpdated(null,!0)}}else r&&(r.mergeOption({},this),r.optionUpdated({},!1));r?(f.push(r.option),p.push(r),d++):(f.push(void 0),p.push(void 0))},this),n[e]=f,i.set(e,p),o.set(e,d),"series"===e&&r(this)},this),this._seriesIndices||r(this)},e.prototype.getOption=function(){var t=(0,k.clone)(this.option);return(0,k.each)(t,function(e,n){if(L.A.hasClass(n)){for(var r=O.qB(e),i=r.length,o=!1,a=i-1;a>=0;a--)r[a]&&!O.oh(r[a])?o=!0:(r[a]=null,!o&&i--);r.length=i,t[n]=r}}),delete t[V],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var r=n[e||0];if(r)return r;if(null==e){for(var i=0;i<n.length;i++)if(n[i])return n[i]}}},e.prototype.queryComponents=function(t){var e,n=t.mainType;if(!n)return[];var r=t.index,i=t.id,o=t.name,a=this._componentsMap.get(n);return a&&a.length?(null!=r?(e=[],(0,k.each)(O.qB(r),function(t){a[t]&&e.push(a[t])})):e=null!=i?j("id",i,a):null!=o?j("name",o,a):(0,k.filter)(a,function(t){return!!t}),U(e,t)):[]},e.prototype.findComponents=function(t){var e,n,r,i,o,a=t.query,s=t.mainType,u=(e=a,n=s+"Index",r=s+"Id",i=s+"Name",e&&(null!=e[n]||null!=e[r]||null!=e[i])?{mainType:s,index:e[n],id:e[r],name:e[i]}:null);return o=U(u?this.queryComponents(u):(0,k.filter)(this._componentsMap.get(s),function(t){return!!t}),t),t.filter?(0,k.filter)(o,t.filter):o},e.prototype.eachComponent=function(t,e,n){var r=this._componentsMap;if((0,k.isFunction)(t))r.each(function(n,r){for(var i=0;n&&i<n.length;i++){var o=n[i];o&&t.call(e,r,o,o.componentIndex)}});else for(var i=(0,k.isString)(t)?r.get(t):(0,k.isObject)(t)?this.findComponents(t):null,o=0;i&&o<i.length;o++){var a=i[o];a&&e.call(n,a,a.componentIndex)}},e.prototype.getSeriesByName=function(t){var e=O.vS(t,null);return(0,k.filter)(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return(0,k.filter)(this._componentsMap.get("series"),function(e){return!!e&&e.subType===t})},e.prototype.getSeries=function(){return(0,k.filter)(this._componentsMap.get("series"),function(t){return!!t})},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){i(this),(0,k.each)(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];t.call(e,r,n)},this)},e.prototype.eachRawSeries=function(t,e){(0,k.each)(this._componentsMap.get("series"),function(n){n&&t.call(e,n,n.componentIndex)})},e.prototype.eachSeriesByType=function(t,e,n){i(this),(0,k.each)(this._seriesIndices,function(r){var i=this._componentsMap.get("series")[r];i.subType===t&&e.call(n,i,r)},this)},e.prototype.eachRawSeriesByType=function(t,e,n){return(0,k.each)(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return i(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){i(this);var n=[];(0,k.each)(this._seriesIndices,function(r){var i=this._componentsMap.get("series")[r];t.call(e,i,r)&&n.push(r)},this),this._seriesIndices=n,this._seriesIndicesMap=(0,k.createHashMap)(n)},e.prototype.restoreData=function(t){r(this);var e=this._componentsMap,n=[];e.each(function(t,e){L.A.hasClass(e)&&n.push(e)}),L.A.topologicalTravel(n,L.A.getAllClassMainTypes(),function(n){(0,k.each)(e.get(n),function(e){e&&("series"!==n||!function(t,e){if(e){var n=e.seriesIndex,r=e.seriesId,i=e.seriesName;return null!=n&&t.componentIndex!==n||null!=r&&t.id!==r||null!=i&&t.name!==i}}(e,t))&&e.restoreData()})})},e.internalField=void(r=function(t){var e=t._seriesIndices=[];(0,k.each)(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=(0,k.createHashMap)(e)},i=function(t){},o=function(t,e){t.option={},t.option[V]=1,t._componentsMap=(0,k.createHashMap)({series:[]}),t._componentsCount=(0,k.createHashMap)();var n,r,i,o=e.aria;(0,k.isObject)(o)&&null==o.enabled&&(o.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,(0,k.each)(r,function(t,e){("colorLayer"!==e||!i)&&(L.A.hasClass(e)||("object"==typeof t?n[e]=n[e]?(0,k.merge)(n[e],t,!1):(0,k.clone)(t):null==n[e]&&(n[e]=t)))}),(0,k.merge)(e,N,!1),t._mergeOption(e,null)}),e}(P.A);function j(t,e,n){if((0,k.isArray)(e)){var r=(0,k.createHashMap)();return(0,k.each)(e,function(t){null!=t&&null!=O.vS(t,null)&&r.set(t,!0)}),(0,k.filter)(n,function(e){return e&&r.get(e[t])})}var i=O.vS(e,null);return(0,k.filter)(n,function(e){return e&&null!=i&&e[t]===i})}function U(t,e){return e.hasOwnProperty("subType")?(0,k.filter)(t,function(t){return t&&t.subType===e.subType}):t}function W(t){var e=(0,k.createHashMap)();return t&&(0,k.each)(O.qB(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}(0,k.mixin)(H,z.X);var G=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"];let q=function(t){k.each(G,function(e){this[e]=k.bind(t[e],t)},this)};var X=n(7416),Y=/^(min|max)?(.+)$/,Z=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&((0,k.each)((0,O.qB)(t.series),function(t){t&&t.data&&(0,k.isTypedArray)(t.data)&&(0,k.setAsPrimitive)(t.data)}),(0,k.each)((0,O.qB)(t.dataset),function(t){t&&t.source&&(0,k.isTypedArray)(t.source)&&(0,k.setAsPrimitive)(t.source)})),t=(0,k.clone)(t);var r=this._optionBackup,i=function(t,e,n){var r,i,o=[],a=t.baseOption,s=t.timeline,u=t.options,l=t.media,c=!!t.media,h=!!(u||s||a&&a.timeline);function f(t){(0,k.each)(e,function(e){e(t,n)})}return a?(i=a).timeline||(i.timeline=s):((h||c)&&(t.options=t.media=null),i=t),c&&(0,k.isArray)(l)&&(0,k.each)(l,function(t){t&&t.option&&(t.query?o.push(t):r||(r=t))}),f(i),(0,k.each)(u,function(t){return f(t)}),(0,k.each)(o,function(t){return f(t.option)}),{baseOption:i,timelineOptions:u||[],mediaDefault:r,mediaList:o}}(t,e,!r);this._newBaseOption=i.baseOption,r?(i.timelineOptions.length&&(r.timelineOptions=i.timelineOptions),i.mediaList.length&&(r.mediaList=i.mediaList),i.mediaDefault&&(r.mediaDefault=i.mediaDefault)):this._optionBackup=i},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],(0,k.clone)(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var r=t.getComponent("timeline");r&&(e=(0,k.clone)(n[r.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e,n,r=this._api.getWidth(),i=this._api.getHeight(),o=this._mediaList,a=this._mediaDefault,s=[],u=[];if(!o.length&&!a)return u;for(var l=0,c=o.length;l<c;l++)(function(t,e,n){var r={width:e,height:n,aspectratio:e/n},i=!0;return(0,k.each)(t,function(t,e){var n=e.match(Y);if(n&&n[1]&&n[2]){var o,a,s,u=n[1];o=r[n[2].toLowerCase()],a=t,("min"===(s=u)?o>=a:"max"===s?o<=a:o===a)||(i=!1)}}),i})(o[l].query,r,i)&&s.push(l);return!s.length&&a&&(s=[-1]),s.length&&(e=s,n=this._currentMediaIndices,e.join(",")!==n.join(","))&&(u=(0,k.map)(s,function(t){return(0,k.clone)(-1===t?a.option:o[t].option)})),this._currentMediaIndices=s,u},t}(),$=k.each,K=k.isObject,Q=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function J(t){var e=t&&t.itemStyle;if(e)for(var n=0,r=Q.length;n<r;n++){var i=Q[n],o=e.normal,a=e.emphasis;o&&o[i]&&(t[i]=t[i]||{},t[i].normal?k.merge(t[i].normal,o[i]):t[i].normal=o[i],o[i]=null),a&&a[i]&&(t[i]=t[i]||{},t[i].emphasis?k.merge(t[i].emphasis,a[i]):t[i].emphasis=a[i],a[i]=null)}}function tt(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var r=t[e].normal,i=t[e].emphasis;r&&(n?(t[e].normal=t[e].emphasis=null,k.defaults(t[e],r)):t[e]=r),i&&(t.emphasis=t.emphasis||{},t.emphasis[e]=i,i.focus&&(t.emphasis.focus=i.focus),i.blurScope&&(t.emphasis.blurScope=i.blurScope))}}function te(t){tt(t,"itemStyle"),tt(t,"lineStyle"),tt(t,"areaStyle"),tt(t,"label"),tt(t,"labelLine"),tt(t,"upperLabel"),tt(t,"edgeLabel")}function tn(t,e){var n=K(t)&&t[e],r=K(n)&&n.textStyle;if(r)for(var i=0,o=O.JS.length;i<o;i++){var a=O.JS[i];r.hasOwnProperty(a)&&(n[a]=r[a])}}function tr(t){t&&(te(t),tn(t,"label"),t.emphasis&&tn(t.emphasis,"label"))}function ti(t){return k.isArray(t)?t:t?[t]:[]}function to(t){return(k.isArray(t)?t[0]:t)||{}}function ta(t){t&&(0,k.each)(ts,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var ts=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],tu=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],tl=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function tc(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<tl.length;n++){var r=tl[n][1],i=tl[n][0];null!=e[r]&&(e[i]=e[r])}}function th(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function tf(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function tp(t,e){var n;$(ti(t.series),function(t){K(t)&&function(t){if(K(t)){J(t),te(t),tn(t,"label"),tn(t,"upperLabel"),tn(t,"edgeLabel"),t.emphasis&&(tn(t.emphasis,"label"),tn(t.emphasis,"upperLabel"),tn(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(J(e),tr(e));var n=t.markLine;n&&(J(n),tr(n));var r=t.markArea;r&&tr(r);var i=t.data;if("graph"===t.type){i=i||t.nodes;var o=t.links||t.edges;if(o&&!k.isTypedArray(o))for(var a=0;a<o.length;a++)tr(o[a]);k.each(t.categories,function(t){te(t)})}if(i&&!k.isTypedArray(i))for(var a=0;a<i.length;a++)tr(i[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)tr(s[a]);if((n=t.markLine)&&n.data)for(var u=n.data,a=0;a<u.length;a++)k.isArray(u[a])?(tr(u[a][0]),tr(u[a][1])):tr(u[a]);"gauge"===t.type?(tn(t,"axisLabel"),tn(t,"title"),tn(t,"detail")):"treemap"===t.type?(tt(t.breadcrumb,"itemStyle"),k.each(t.levels,function(t){te(t)})):"tree"===t.type&&te(t.leaves)}}(t)}),n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"],e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),$(n,function(e){$(ti(t[e]),function(t){t&&(tn(t,"axisLabel"),tn(t.axisPointer,"label"))})}),$(ti(t.parallel),function(t){var e=t&&t.parallelAxisDefault;tn(e,"axisLabel"),tn(e&&e.axisPointer,"label")}),$(ti(t.calendar),function(t){tt(t,"itemStyle"),tn(t,"dayLabel"),tn(t,"monthLabel"),tn(t,"yearLabel")}),$(ti(t.radar),function(t){tn(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),$(ti(t.geo),function(t){K(t)&&(tr(t),$(ti(t.regions),function(t){tr(t)}))}),$(ti(t.timeline),function(t){tr(t),tt(t,"label"),tt(t,"itemStyle"),tt(t,"controlStyle",!0);var e=t.data;k.isArray(e)&&k.each(e,function(t){k.isObject(t)&&(tt(t,"label"),tt(t,"itemStyle"))})}),$(ti(t.toolbox),function(t){tt(t,"iconStyle"),$(t.feature,function(t){tt(t,"iconStyle")})}),tn(to(t.axisPointer),"label"),tn(to(t.tooltip).axisPointer,"label"),t.series=(0,O.qB)(t.series),(0,k.each)(t.series,function(t){if((0,k.isObject)(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){null!=t.clockWise&&(t.clockwise=t.clockWise),th(t.label);var n=t.data;if(n&&!(0,k.isTypedArray)(n))for(var r=0;r<n.length;r++)th(n[r]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)}else if("gauge"===e){var i=function(t,e){for(var n=e.split(","),r=t,i=0;i<n.length&&null!=(r=r&&r[n[i]]);i++);return r}(t,"pointer.color");null!=i&&function(t,e,n,r){for(var i,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[i=o[s]]&&(a[i]={}),a=a[i];null==a[o[s]]&&(a[o[s]]=n)}(t,"itemStyle.color",i)}else if("bar"===e){tc(t),tc(t.backgroundStyle),tc(t.emphasis);var n=t.data;if(n&&!(0,k.isTypedArray)(n))for(var r=0;r<n.length;r++)"object"==typeof n[r]&&(tc(n[r]),tc(n[r]&&n[r].emphasis))}else if("sunburst"===e){var o=t.highlightPolicy;o&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=o)),tf(t),function t(e,n){if(e)for(var r=0;r<e.length;r++)n(e[r]),e[r]&&t(e[r].children,n)}(t.data,tf)}else"graph"===e||"sankey"===e?t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency")):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&(0,k.defaults)(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),ta(t)}}),t.dataRange&&(t.visualMap=t.dataRange),(0,k.each)(tu,function(e){var n=t[e];n&&((0,k.isArray)(n)||(n=[n]),(0,k.each)(n,function(t){ta(t)}))})}var td=n(3607);function tg(t){(0,k.each)(t,function(e,n){var r=[],i=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,u=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,function(o,l,c){var h,f,p=a.get(e.stackedDimension,c);if(isNaN(p))return i;s?f=a.getRawIndex(c):h=a.get(e.stackedByDimension,c);for(var d=NaN,g=n-1;g>=0;g--){var y=t[g];if(s||(f=y.data.rawIndexOf(y.stackedByDimension,h)),f>=0){var v=y.data.getByRawIndex(y.stackResultDimension,f);if("all"===u||"positive"===u&&v>0||"negative"===u&&v<0||"samesign"===u&&p>=0&&v>0||"samesign"===u&&p<=0&&v<0){p=(0,td.Tr)(p,v),d=v;break}}}return r[0]=p,r[1]=d,r})})}var ty=n(2403),tv=n(6036),tm=n(4436),t_=n(7821),tx=n(3493),tb=n(8991),tw=n(4271),tS=n(2663),tM=n(2392),tA=n(1270),tT=n(3809),tk=n(1531),tC=n(1764),tI=(0,O.$r)(),tD={itemStyle:(0,tT.A)(tk.L,!0),lineStyle:(0,tT.A)(tC.m,!0)},tO={lineStyle:"stroke",itemStyle:"fill"};function tP(t,e){var n=t.visualStyleMapper||tD[e];return n||(console.warn("Unknown style type '"+e+"'."),tD.itemStyle)}function tL(t,e){var n=t.visualDrawType||tO[e];return n||(console.warn("Unknown style type '"+e+"'."),"fill")}var tR=new P.A,tE=n(6631),tN=n(3875),tB=n(9950),tF=Math.PI,tz=n(9942),tV=n(1929),tH=function(){function t(t,e,n,r){this._stageTaskMap=(0,k.createHashMap)(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),r=this._visualHandlers=r.slice(),this._allHandlers=n.concat(r)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),r=n.context,i=!e&&n.progressiveEnabled&&(!r||r.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=r&&r.modDataCount,a=null!=o?Math.ceil(o/i):null;return{step:i,modBy:a,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),r=t.getData().count(),i=n.progressiveEnabled&&e.incrementalPrepareRender&&r>=n.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=n.context={progressiveRender:i,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=(0,k.createHashMap)();t.eachSeries(function(t){var r=t.getProgressive(),i=t.uid;n.set(i,{id:i,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:r&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(r||700),count:0}),e._pipe(t,t.dataTask)})},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;(0,k.each)(this._allHandlers,function(r){var i=t.get(r.uid)||t.set(r.uid,{});(0,k.assert)(!(r.reset&&r.overallReset),""),r.reset&&this._createSeriesStageTask(r,i,e,n),r.overallReset&&this._createOverallStageTask(r,i,e,n)},this)},t.prototype.prepareView=function(t,e,n,r){var i=t.renderTask,o=i.context;o.model=e,o.ecModel=n,o.api=r,i.__block=!t.incrementalPrepareRender,this._pipe(e,i)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,r){r=r||{};var i=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}(0,k.each)(t,function(t,s){if(!r.visualType||r.visualType===t.visualType){var u=o._stageTaskMap.get(t.uid),l=u.seriesTaskMap,c=u.overallTask;if(c){var h,f=c.agentStubMap;f.each(function(t){a(r,t)&&(t.dirty(),h=!0)}),h&&c.dirty(),o.updatePayload(c,n);var p=o.getPerformArgs(c,r.block);f.each(function(t){t.perform(p)}),c.perform(p)&&(i=!0)}else l&&l.each(function(s,u){a(r,s)&&s.dirty();var l=o.getPerformArgs(s,r.block);l.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(l)&&(i=!0)})}}),this.unfinished=i||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,r){var i=this,o=e.seriesTaskMap,a=e.seriesTaskMap=(0,k.createHashMap)(),s=t.seriesType,u=t.getTargetSeries;function l(e){var s=e.uid,u=a.set(s,o&&o.get(s)||(0,tz.U)({plan:tq,reset:tX,count:t$}));u.context={model:e,ecModel:n,api:r,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:i},i._pipe(e,u)}t.createOnAllSeries?n.eachRawSeries(l):s?n.eachRawSeriesByType(s,l):u&&u(n,r).each(l)},t.prototype._createOverallStageTask=function(t,e,n,r){var i=this,o=e.overallTask=e.overallTask||(0,tz.U)({reset:tj});o.context={ecModel:n,api:r,overallReset:t.overallReset,scheduler:i};var a=o.agentStubMap,s=o.agentStubMap=(0,k.createHashMap)(),u=t.seriesType,l=t.getTargetSeries,c=!0,h=!1;function f(t){var e=t.uid,n=s.set(e,a&&a.get(e)||(h=!0,(0,tz.U)({reset:tU,onDirty:tG})));n.context={model:t,overallProgress:c},n.agent=o,n.__block=c,i._pipe(t,n)}(0,k.assert)(!t.createOnAllSeries,""),u?n.eachRawSeriesByType(u,f):l?l(n,r).each(f):(c=!1,(0,k.each)(n.getSeries(),f)),h&&o.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,r=this._pipelineMap.get(n);r.head||(r.head=e),r.tail&&r.tail.pipe(e),r.tail=e,e.__idxInPipeline=r.count++,e.__pipeline=r},t.wrapStageHandler=function(t,e){return(0,k.isFunction)(t)&&(t={overallReset:t,seriesType:function(t){a=null;try{t(tK,tQ)}catch(t){}return a}(t)}),t.uid=(0,tV.$Q)("stageHandler"),e&&(t.visualType=e),t},t}();function tj(t){t.overallReset(t.ecModel,t.api,t.payload)}function tU(t){return t.overallProgress&&tW}function tW(){this.agent.dirty(),this.getDownstream().dirty()}function tG(){this.agent&&this.agent.dirty()}function tq(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function tX(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=(0,O.qB)(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?(0,k.map)(e,function(t,e){return tZ(e)}):tY}var tY=tZ(0);function tZ(t){return function(e,n){var r=n.data,i=n.resetDefines[t];if(i&&i.dataEach)for(var o=e.start;o<e.end;o++)i.dataEach(r,o);else i&&i.progress&&i.progress(e,r)}}function t$(t){return t.data.count()}var tK={},tQ={};function tJ(t,e){for(var n in e.prototype)t[n]=k.noop}tJ(tK,H),tJ(tQ,q),tK.eachSeriesByType=tK.eachRawSeriesByType=function(t){a=t},tK.eachComponent=function(t){"series"===t.mainType&&t.subType&&(a=t.subType)};var t0=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],t1="#B9B8CE",t2="#100C2A",t5=function(){return{axisLine:{lineStyle:{color:t1}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},t3=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],t4={darkMode:!0,color:t3,backgroundColor:t2,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:t1},pageTextStyle:{color:t1}},textStyle:{color:t1},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:t1}},dataZoom:{borderColor:"#71708A",textStyle:{color:t1},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:t1}},timeline:{lineStyle:{color:t1},label:{color:t1},controlStyle:{color:t1,borderColor:t1}},calendar:{itemStyle:{color:t2},dayLabel:{color:t1},monthLabel:{color:t1},yearLabel:{color:t1}},timeAxis:t5(),logAxis:t5(),valueAxis:t5(),categoryAxis:t5(),line:{symbol:"circle"},graph:{color:t3},gauge:{title:{color:t1},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:t1},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};t4.categoryAxis.splitLine.show=!1;var t6=n(2660),t8=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},r={};if(k.isString(t)){var i=(0,t6.CC)(t);e.mainType=i.main||null,e.subType=i.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};k.each(t,function(t,i){for(var s=!1,u=0;u<o.length;u++){var l=o[u],c=i.lastIndexOf(l);if(c>0&&c===i.length-l.length){var h=i.slice(0,c);"data"!==h&&(e.mainType=h,e[l.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(i)&&(n[i]=t,s=!0),s||(r[i]=t)})}return{cptQuery:e,dataQuery:n,otherQuery:r}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,i=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,u=e.dataQuery;return l(s,o,"mainType")&&l(s,o,"subType")&&l(s,o,"index","componentIndex")&&l(s,o,"name")&&l(s,o,"id")&&l(u,i,"name")&&l(u,i,"dataIndex")&&l(u,i,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,r,i));function l(t,e,n,r){return null==t[n]||e[r||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),t7=["symbol","symbolSize","symbolRotate","symbolOffset"],t9=t7.concat(["symbolKeepAspect"]),et=n(3312);function ee(t,e,n,r,i){var o=t+e;n.isSilent(o)||r.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e=t.seriesIndex,r=t.option.selectedMap,a=i.selected,s=0;s<a.length;s++)if(a[s].seriesIndex===e){var u=t.getData(),l=(0,O.le)(u,i.fromActionPayload);n.trigger(o,{type:o,seriesId:t.id,name:(0,k.isArray)(l)?u.getName(l[0]):u.getName(l),selected:(0,k.isString)(r)?r:(0,k.extend)({},r)})}})}var en=n(1798),er=n(2045);function ei(t,e,n){for(var r;t&&(!e(t)||(r=t,!n));)t=t.__hostTarget||t.parent;return r}var eo=n(5942),ea=new D.A,es=n(2316),eu=n(6033),el="5.6.0",ec={zrender:"5.6.1"},eh={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},ef="__flagInMainProcess",ep="__pendingUpdate",ed="__needsUpdateStatus",eg=/^[a-zA-Z0-9_]+$/,ey="__connectUpdateStatus";function ev(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return this.isDisposed()?void this.id:e_(this,t,e)}}function em(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return e_(this,t,e)}}function e_(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),D.A.prototype[e].apply(t,n)}var ex=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,A.__extends)(e,t),e}(D.A),eb=ex.prototype;eb.on=em("on"),eb.off=em("off");var ew=function(t){function e(e,n,r){var i=t.call(this,new t8)||this;i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],r=r||{},(0,k.isString)(n)&&(n=eD[n]),i._dom=e,r.ssr&&T.registerSSRDataGetter(function(t){var e=(0,tS.z)(t),n=e.dataIndex;if(null!=n){var r=(0,k.createHashMap)();return r.set("series_index",e.seriesIndex),r.set("data_index",n),e.ssrType&&r.set("ssr_type",e.ssrType),r}});var o=i._zr=T.init(e,{renderer:r.renderer||"canvas",devicePixelRatio:r.devicePixelRatio,width:r.width,height:r.height,ssr:r.ssr,useDirtyRect:(0,k.retrieve2)(r.useDirtyRect,!1),useCoarsePointer:(0,k.retrieve2)(r.useCoarsePointer,"auto"),pointerSize:r.pointerSize});i._ssr=r.ssr,i._throttledZrFlush=(0,tA.nF)((0,k.bind)(o.flush,o),17),(n=(0,k.clone)(n))&&tp(n,!0),i._theme=n,i._locale=(0,er.A$)(r.locale||er.Lv),i._coordSysMgr=new X.A;var a=i._api=b(i);function s(t,e){return t.__prio-e.__prio}return(0,I.A)(eI,s),(0,I.A)(ek,s),i._scheduler=new tH(i,a,ek,eI),i._messageCenter=new ex,i._initEvents(),i.resize=(0,k.bind)(i.resize,i),o.animation.on("frame",i._onframe,i),y(o,i),v(o,i),(0,k.setAsPrimitive)(i),i}return(0,A.__extends)(e,t),e.prototype._onframe=function(){if(!this._disposed){M(this);var t=this._scheduler;if(this[ep]){var e=this[ep].silent;this[ef]=!0;try{s(this),c.update.call(this,null,this[ep].updateParams)}catch(t){throw this[ef]=!1,this[ep]=null,t}this._zr.flush(),this[ef]=!1,this[ep]=null,d.call(this,e),g.call(this,e)}else if(t.unfinished){var n=1,r=this._model,i=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(r),t.performDataProcessorTasks(r),f(this,r),t.performVisualTasks(r),x(this,this._model,i,"remain",{}),n-=new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,e,n){if(!this[ef]){if(this._disposed)return void this.id;if((0,k.isObject)(e)&&(n=e.lazyUpdate,r=e.silent,i=e.replaceMerge,o=e.transition,e=e.notMerge),this[ef]=!0,!this._model||e){var r,i,o,a=new Z(this._api),u=this._theme,l=this._model=new H;l.scheduler=this._scheduler,l.ssr=this._ssr,l.init(null,null,null,u,this._locale,a)}this._model.setOption(t,{replaceMerge:i},eC);var h={seriesTransition:o,optionChanged:!0};if(n)this[ep]={silent:r,updateParams:h},this[ef]=!1,this.getZr().wakeUp();else{try{s(this),c.update.call(this,null,h)}catch(t){throw this[ep]=null,this[ef]=!1,t}this._ssr||this._zr.flush(),this[ep]=null,this[ef]=!1,d.call(this,r),g.call(this,r)}}},e.prototype.setTheme=function(){(0,et.aT)("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||C.A.hasGlobalWindow&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){return t=t||{},this._zr.painter.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){return t=t||{},this._zr.painter.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){if(C.A.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return(0,k.each)(e,function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(this._disposed)return void this.id;var e=(t=t||{}).excludeComponents,n=this._model,r=[],i=this;(0,k.each)(e,function(t){n.eachComponent({mainType:t},function(t){var e=i._componentsMap[t.__viewId];e.group.ignore||(r.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return(0,k.each)(r,function(t){t.group.ignore=!1}),o},e.prototype.getConnectedDataURL=function(t){if(this._disposed)return void this.id;var e="svg"===t.type,n=this.group,r=Math.min,i=Math.max,o=1/0;if(!eL[n])return this.getDataURL(t);var a=o,s=o,u=-o,l=-o,c=[],h=t&&t.pixelRatio||this.getDevicePixelRatio();(0,k.each)(eP,function(o,h){if(o.group===n){var f=e?o.getZr().painter.getSvgDom().innerHTML:o.renderToCanvas((0,k.clone)(t)),p=o.getDom().getBoundingClientRect();a=r(p.left,a),s=r(p.top,s),u=i(p.right,u),l=i(p.bottom,l),c.push({dom:f,left:p.left,top:p.top})}}),a*=h,s*=h,u*=h,l*=h;var f=u-a,p=l-s,d=es.yh.createCanvas(),g=T.init(d,{renderer:e?"svg":"canvas"});if(g.resize({width:f,height:p}),!e)return t.connectedBackgroundColor&&g.add(new t_.A({shape:{x:0,y:0,width:f,height:p},style:{fill:t.connectedBackgroundColor}})),(0,k.each)(c,function(t){var e=new tx.Ay({style:{x:t.left*h-a,y:t.top*h-s,image:t.dom}});g.add(e)}),g.refreshImmediately(),d.toDataURL("image/"+(t&&t.type||"png"));var y="";return(0,k.each)(c,function(t){var e=t.left-a,n=t.top-s;y+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&g.painter.setBackgroundColor(t.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()},e.prototype.convertToPixel=function(t,e){return h(this,"convertToPixel",t,e)},e.prototype.convertFromPixel=function(t,e){return h(this,"convertFromPixel",t,e)},e.prototype.containPixel=function(t,e){if(this._disposed)return void this.id;var n,r=this._model,i=O._e(r,t);return(0,k.each)(i,function(t,r){r.indexOf("Models")>=0&&(0,k.each)(t,function(t){var i=t.coordinateSystem;if(i&&i.containPoint)n=n||!!i.containPoint(e);else if("seriesModels"===r){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}},this)},this),!!n},e.prototype.getVisual=function(t,e){var n=this._model,r=O._e(n,t,{defaultMainType:"series"}),i=r.seriesModel.getData(),o=r.hasOwnProperty("dataIndexInside")?r.dataIndexInside:r.hasOwnProperty("dataIndex")?i.indexOfRawIndex(r.dataIndex):null;return null!=o?function(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}(i,o,e):function(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}(i,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t,e,n,r=this;(0,k.each)(eM,function(t){var e=function(e){var n,i=r.getModel(),o=e.target;if("globalout"===t?n={}:o&&ei(o,function(t){var e=(0,tS.z)(t);if(e&&null!=e.dataIndex){var r=e.dataModel||i.getSeriesByIndex(e.seriesIndex);return n=r&&r.getDataParams(e.dataIndex,e.dataType,o)||{},!0}if(e.eventData)return n=(0,k.extend)({},e.eventData),!0},!0),n){var a=n.componentType,s=n.componentIndex;("markLine"===a||"markPoint"===a||"markArea"===a)&&(a="series",s=n.seriesIndex);var u=a&&null!=s&&i.getComponent(a,s),l=u&&r["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];n.event=e,n.type=t,r._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:u,view:l},r.trigger(t,n)}};e.zrEventfulCallAtLast=!0,r._zr.on(t,e,r)}),(0,k.each)(eT,function(t,e){r._messageCenter.on(e,function(t){this.trigger(e,t)},r)}),(0,k.each)(["selectchanged"],function(t){r._messageCenter.on(t,function(e){this.trigger(t,e)},r)}),t=this._messageCenter,e=this,n=this._api,t.on("selectchanged",function(t){var r=n.getModel();t.isFromClick?(ee("map","selectchanged",e,r,t),ee("pie","selectchanged",e,r,t)):"select"===t.fromAction?(ee("map","selected",e,r,t),ee("pie","selected",e,r,t)):"unselect"===t.fromAction&&(ee("map","unselected",e,r,t),ee("pie","unselected",e,r,t))})},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){if(this._disposed)return void this.id;this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed)return void this.id;this._disposed=!0,this.getDom()&&O.Bq(this.getDom(),eN,"");var t=this._api,e=this._model;(0,k.each)(this._componentsViews,function(n){n.dispose(e,t)}),(0,k.each)(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),this._dom=this._model=this._chartsMap=this._componentsMap=this._chartsViews=this._componentsViews=this._scheduler=this._api=this._zr=this._throttledZrFlush=this._theme=this._coordSysMgr=this._messageCenter=null,delete eP[this.id]},e.prototype.resize=function(t){if(!this[ef]){if(this._disposed)return void this.id;this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),r=t&&t.silent;this[ep]&&(null==r&&(r=this[ep].silent),n=!0,this[ep]=null),this[ef]=!0;try{n&&s(this),c.update.call(this,{type:"resize",animation:(0,k.extend)({duration:0},t&&t.animation)})}catch(t){throw this[ef]=!1,t}this[ef]=!1,d.call(this,r),g.call(this,r)}}},e.prototype.showLoading=function(t,e){if(this._disposed)return void this.id;if((0,k.isObject)(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),eO[t]){var n=eO[t](this._api,e),r=this._zr;this._loadingFX=n,r.add(n)}},e.prototype.hideLoading=function(){if(this._disposed)return void this.id;this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},e.prototype.makeActionFromEvent=function(t){var e=(0,k.extend)({},t);return e.type=eT[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed)return void this.id;if(((0,k.isObject)(e)||(e={silent:!!e}),eA[t.type])&&this._model){if(this[ef])return void this._pendingActions.push(t);var n=e.silent;p.call(this,t,n);var r=e.flush;r?this._zr.flush():!1!==r&&C.A.browser.weChat&&this._throttledZrFlush(),d.call(this,n),g.call(this,n)}},e.prototype.updateLabelLayout=function(){ea.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed)return void this.id;var e=t.seriesIndex;this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()},e.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function e(t){for(var e=[],n=t.currentStates,r=0;r<n.length;r++){var i=n[r];"emphasis"!==i&&"blur"!==i&&"select"!==i&&e.push(i)}t.selected&&t.states.select&&e.push("select"),t.hoverState===tM.e3&&t.states.emphasis?e.push("emphasis"):t.hoverState===tM.zX&&t.states.blur&&e.push("blur"),t.useStates(e)}function n(t,e){if(!t.preventAutoZ){var n=t.get("z")||0,r=t.get("zlevel")||0;e.eachRendered(function(t){return function t(e,n,r,i){var o=e.getTextContent(),a=e.getTextGuideLine();if(e.isGroup)for(var s=e.childrenRef(),u=0;u<s.length;u++)i=Math.max(t(s[u],n,r,i),i);else e.z=n,e.zlevel=r,i=Math.max(e.z2,i);if(o&&(o.z=n,o.zlevel=r,isFinite(i)&&(o.z2=i+2)),a){var l=e.textGuideLineConfig;a.z=n,a.zlevel=r,isFinite(i)&&(a.z2=i+(l&&l.showAbove?1:-1))}return i}(t,n,r,-1/0),!0})}}function r(t,e){e.eachRendered(function(t){if(!tb.LR(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}})}function i(t,n){var r=t.getModel("stateAnimation"),i=t.isAnimationEnabled(),o=r.get("duration"),a=o>0?{duration:o,delay:r.get("delay"),easing:r.get("easing")}:null;n.eachRendered(function(t){if(t.states&&t.states.emphasis&&!tb.LR(t)){if(t instanceof tw.Ay&&(0,tM.fz)(t),t.__dirty){var n=t.prevStates;n&&t.useStates(n)}if(i){t.stateTransition=a;var r=t.getTextContent(),o=t.getTextGuideLine();r&&(r.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&e(t)}})}s=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),u(t,!0),u(t,!1),e.plan()},u=function(t,e){for(var n=t._model,r=t._scheduler,i=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,u=0;u<i.length;u++)i[u].__alive=!1;function l(t){var u=t.__requireNewView;t.__requireNewView=!1;var l="_ec_"+t.id+"_"+t.type,c=!u&&o[l];if(!c){var h=(0,t6.CC)(t.type);(c=new(e?tv.A.getClass(h.main,h.sub):tm.A.getClass(h.sub))).init(n,s),o[l]=c,i.push(c),a.add(c.group)}t.__viewId=c.__id=l,c.__alive=!0,c.__model=t,c.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},e||r.prepareView(c,t,n,s)}e?n.eachComponent(function(t,e){"series"!==t&&l(e)}):n.eachSeries(l);for(var u=0;u<i.length;){var c=i[u];c.__alive?u++:(e||c.renderTask.dispose(),a.remove(c.group),c.dispose(n,s),i.splice(u,1),o[c.__id]===c&&delete o[c.__id],c.__id=c.group.__ecComponentInfo=null)}},l=function(t,e,n,r,i){var o,a=t._model;if(a.setUpdatePayload(n),!r)return void(0,k.each)([].concat(t._componentsViews).concat(t._chartsViews),c);var s={};s[r+"Id"]=n[r+"Id"],s[r+"Index"]=n[r+"Index"],s[r+"Name"]=n[r+"Name"];var u={mainType:r,query:s};i&&(u.subType=i);var l=n.excludeSeriesId;function c(r){r&&r.__alive&&r[e]&&r[e](r.__model,a,t._api,n)}null!=l&&(o=(0,k.createHashMap)(),(0,k.each)(O.qB(l),function(t){var e=O.vS(t,null);null!=e&&o.set(e,!0)})),a&&a.eachComponent(u,function(e){if(!(o&&null!=o.get(e.id)))if((0,tM.T$)(n))if(e instanceof ty.A)n.type!==tM.h5||n.notBlur||e.get(["emphasis","disabled"])||(0,tM.lV)(e,n,t._api);else{var r=(0,tM.Tl)(e.mainType,e.componentIndex,n.name,t._api),i=r.focusSelf,a=r.dispatchers;n.type===tM.h5&&i&&!n.notBlur&&(0,tM.Du)(e.mainType,e.componentIndex,t._api),a&&(0,k.each)(a,function(t){n.type===tM.h5?(0,tM.HY)(t):(0,tM.SD)(t)})}else(0,tM.Lx)(n)&&e instanceof ty.A&&((0,tM.t6)(e,n,t._api),(0,tM.jA)(e),S(t))},t),a&&a.eachComponent(u,function(e){o&&null!=o.get(e.id)||c(t["series"===r?"_chartsMap":"_componentsMap"][e.__viewId])},t)},c={prepareAndUpdate:function(t){s(this),c.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(e,n){var r=this._model,i=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(r){r.setUpdatePayload(e),s.restoreData(r,e),s.performSeriesTasks(r),a.create(r,i),s.performDataProcessorTasks(r,e),f(this,r),a.update(r,i),t(r),s.performVisualTasks(r,e),m(this,r,i,e,n);var u=r.get("backgroundColor")||"transparent",l=r.get("darkMode");o.setBackgroundColor(u),null!=l&&"auto"!==l&&o.setDarkMode(l),ea.trigger("afterupdate",r,i)}},updateTransform:function(e){var n=this,r=this._model,i=this._api;if(r){r.setUpdatePayload(e);var o=[];r.eachComponent(function(t,a){if("series"!==t){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var u=s.updateTransform(a,r,i,e);u&&u.update&&o.push(s)}else o.push(s)}});var a=(0,k.createHashMap)();r.eachSeries(function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var s=o.updateTransform(t,r,i,e);s&&s.update&&a.set(t.uid,1)}else a.set(t.uid,1)}),t(r),this._scheduler.performVisualTasks(r,e,{setDirty:!0,dirtyMap:a}),x(this,r,i,e,{},a),ea.trigger("afterupdate",r,i)}},updateView:function(e){var n=this._model;n&&(n.setUpdatePayload(e),tm.A.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),m(this,n,this._api,e,{}),ea.trigger("afterupdate",n,this._api))},updateVisual:function(e){var n=this,r=this._model;r&&(r.setUpdatePayload(e),r.eachSeries(function(t){t.getData().clearAllVisual()}),tm.A.markUpdateMethod(e,"updateVisual"),t(r),this._scheduler.performVisualTasks(r,e,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,i){if("series"!==t){var o=n.getViewOfComponentModel(i);o&&o.__alive&&o.updateVisual(i,r,n._api,e)}}),r.eachSeries(function(t){n._chartsMap[t.__viewId].updateVisual(t,r,n._api,e)}),ea.trigger("afterupdate",r,this._api))},updateLayout:function(t){c.update.call(this,t)}},h=function(t,e,n,r){if(t._disposed)return void t.id;for(var i,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=O._e(o,n),u=0;u<a.length;u++){var l=a[u];if(l[e]&&null!=(i=l[e](o,s,r)))return i}},f=function(t,e){var n=t._chartsMap,r=t._scheduler;e.eachSeries(function(t){r.updateStreamModes(t,n[t.__viewId])})},p=function(t,e){var n,r=this,i=this.getModel(),o=t.type,a=t.escapeConnect,u=eA[o],h=u.actionInfo,f=(h.update||"update").split(":"),p=f.pop(),d=null!=f[0]&&(0,t6.CC)(f[0]);this[ef]=!0;var g=[t],y=!1;t.batch&&(y=!0,g=(0,k.map)(t.batch,function(e){return(e=(0,k.defaults)((0,k.extend)({},e),t)).batch=null,e}));var v=[],m=(0,tM.Lx)(t),_=(0,tM.T$)(t);if(_&&(0,tM.qR)(this._api),(0,k.each)(g,function(e){if((n=(n=u.action(e,r._model,r._api))||(0,k.extend)({},e)).type=h.event||n.type,v.push(n),_){var i=O.HB(t),o=i.queryOptionMap;l(r,p,e,i.mainTypeSpecified?o.keys()[0]:"series"),S(r)}else m?(l(r,p,e,"series"),S(r)):d&&l(r,p,e,d.main,d.sub)}),"none"!==p&&!_&&!m&&!d)try{this[ep]?(s(this),c.update.call(this,t),this[ep]=null):c[p].call(this,t)}catch(t){throw this[ef]=!1,t}if(n=y?{type:h.event||o,escapeConnect:a,batch:v}:v[0],this[ef]=!1,!e){var x=this._messageCenter;if(x.trigger(n.type,n),m){var b={type:"selectchanged",escapeConnect:a,selected:(0,tM.mc)(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};x.trigger(b.type,b)}}},d=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();p.call(this,n,t)}},g=function(t){t||this.trigger("updated")},y=function(t,e){t.on("rendered",function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[ep]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})},v=function(t,e){t.on("mouseover",function(t){var n=ei(t.target,tM.u6);n&&((0,tM._m)(n,t,e._api),S(e))}).on("mouseout",function(t){var n=ei(t.target,tM.u6);n&&((0,tM.Iz)(n,t,e._api),S(e))}).on("click",function(t){var n=ei(t.target,function(t){return null!=(0,tS.z)(t).dataIndex},!0);if(n){var r=n.selected?"unselect":"select",i=(0,tS.z)(n);e._api.dispatchAction({type:r,dataType:i.dataType,dataIndexInside:i.dataIndex,seriesIndex:i.seriesIndex,isFromClick:!0})}})},m=function(t,e,n,r,i){!function(t){var e=[],n=[],r=!1;if(t.eachComponent(function(t,i){var o=i.get("zlevel")||0,a=i.get("z")||0,s=i.getZLevelKey();r=r||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:i.componentIndex,type:t,key:s})}),r){var i,o,a=e.concat(n);(0,I.A)(a,function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),(0,k.each)(a,function(e){var n=t.getComponent(e.type,e.idx),r=e.zlevel,a=e.key;null!=i&&(r=Math.max(i,r)),a?(r===i&&a!==o&&r++,o=a):o&&(r===i&&r++,o=""),i=r,n.setZLevel(r)})}}(e),_(t,e,n,r,i),(0,k.each)(t._chartsViews,function(t){t.__alive=!1}),x(t,e,n,r,i),(0,k.each)(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},_=function(t,e,o,a,s,u){(0,k.each)(u||t._componentsViews,function(t){var s=t.__model;r(s,t),t.render(s,e,o,a),n(s,t),i(s,t)})},x=function(t,e,o,a,s,u){var l,c,h,f,p=t._scheduler;s=(0,k.extend)(s||{},{updatedSeries:e.getSeries()}),ea.trigger("series:beforeupdate",e,o,s);var d=!1;e.eachSeries(function(e){var n,i,o,s=t._chartsMap[e.__viewId];s.__alive=!0;var l=s.renderTask;p.updatePayload(l,a),r(e,s),u&&u.get(e.uid)&&l.dirty(),l.perform(p.getPerformArgs(l))&&(d=!0),s.group.silent=!!e.get("silent"),n=e,i=s,o=n.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=o)}),(0,tM.jA)(e)}),p.unfinished=d||p.unfinished,ea.trigger("series:layoutlabels",e,o,s),ea.trigger("series:transition",e,o,s),e.eachSeries(function(e){var r=t._chartsMap[e.__viewId];n(e,r),i(e,r)}),l=t,c=e,h=l._zr.storage,f=0,h.traverse(function(t){!t.isGroup&&f++}),!(f>c.get("hoverLayerThreshold"))||C.A.node||C.A.worker||c.eachSeries(function(t){if(!t.preventUsingHoverLayer){var e=l._chartsMap[t.__viewId];e.__alive&&e.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}}),ea.trigger("series:afterupdate",e,o,s)},S=function(t){t[ed]=!0,t.getZr().wakeUp()},M=function(t){t[ed]&&(t.getZr().storage.traverse(function(t){tb.LR(t)||e(t)}),t[ed]=!1)},b=function(t){return new(function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return(0,A.__extends)(n,e),n.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},n.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},n.prototype.enterEmphasis=function(e,n){(0,tM.HY)(e,n),S(t)},n.prototype.leaveEmphasis=function(e,n){(0,tM.SD)(e,n),S(t)},n.prototype.enterBlur=function(e){(0,tM.QX)(e),S(t)},n.prototype.leaveBlur=function(e){(0,tM.JC)(e),S(t)},n.prototype.enterSelect=function(e){(0,tM.JI)(e),S(t)},n.prototype.leaveSelect=function(e){(0,tM.gd)(e),S(t)},n.prototype.getModel=function(){return t.getModel()},n.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},n.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},n}(q))(t)},w=function(t){function e(t,e){for(var n=0;n<t.length;n++)t[n][ey]=e}(0,k.each)(eT,function(n,r){t._messageCenter.on(r,function(n){if(eL[t.group]&&0!==t[ey]&&(!n||!n.escapeConnect)){var r=t.makeActionFromEvent(n),i=[];(0,k.each)(eP,function(e){e!==t&&e.group===t.group&&i.push(e)}),e(i,0),(0,k.each)(i,function(t){1!==t[ey]&&t.dispatchAction(r)}),e(i,2)}})})}}(),e}(D.A),eS=ew.prototype;eS.on=ev("on"),eS.off=ev("off"),eS.one=function(t,e,n){var r=this;(0,et.aT)("ECharts#one is deprecated."),this.on.call(this,t,function n(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];e&&e.apply&&e.apply(this,i),r.off(t,n)},n)};var eM=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"],eA={},eT={},ek=[],eC=[],eI=[],eD={},eO={},eP={},eL={},eR=new Date-0,eE=new Date-0,eN="_echarts_instance_";function eB(t,e,n){var r=!(n&&n.ssr);if(r){var i=ej(t);if(i)return i}var o=new ew(t,e,n);return o.id="ec_"+eR++,eP[o.id]=o,r&&O.Bq(t,eN,o.id),w(o),ea.trigger("afterinit",o),o}function eF(t){if((0,k.isArray)(t)){var e=t;t=null,(0,k.each)(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+eE++,(0,k.each)(e,function(e){e.group=t})}return eL[t]=!0,t}function ez(t){eL[t]=!1}var eV=ez;function eH(t){(0,k.isString)(t)?t=eP[t]:t instanceof ew||(t=ej(t)),t instanceof ew&&!t.isDisposed()&&t.dispose()}function ej(t){return eP[O.D$(t,eN)]}function eU(t){return eP[t]}function eW(t,e){eD[t]=e}function eG(t){0>(0,k.indexOf)(eC,t)&&eC.push(t)}function eq(t,e){e2(ek,t,e,2e3)}function eX(t){eZ("afterinit",t)}function eY(t){eZ("afterupdate",t)}function eZ(t,e){ea.on(t,e)}function e$(t,e,n){(0,k.isFunction)(e)&&(n=e,e="");var r=(0,k.isObject)(t)?t.type:[t,t={event:e}][0];t.event=(t.event||r).toLowerCase(),eT[e=t.event]||((0,k.assert)(eg.test(r)&&eg.test(e)),eA[r]||(eA[r]={action:n,actionInfo:t}),eT[e]=r)}function eK(t,e){X.A.register(t,e)}function eQ(t){var e=X.A.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()}function eJ(t,e){e2(eI,t,e,1e3,"layout")}function e0(t,e){e2(eI,t,e,3e3,"visual")}var e1=[];function e2(t,e,n,r,i){if(((0,k.isFunction)(e)||(0,k.isObject)(e))&&(n=e,e=r),!((0,k.indexOf)(e1,n)>=0)){e1.push(n);var o=tH.wrapStageHandler(n,i);o.__prio=e,o.__raw=n,t.push(o)}}function e5(t,e){eO[t]=e}function e3(t){(0,es.Gs)({createCanvas:t})}function e4(t,e,n){var r=(0,eu.v)("registerMap");r&&r(t,e,n)}function e6(t){var e=(0,eu.v)("getMap");return e&&e(t)}var e8=en.v5;e0(2e3,{createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),r=t.visualStyleAccessPath||"itemStyle",i=t.getModel(r),o=tP(t,r)(i),a=i.getShallow("decal");a&&(n.setVisual("decal",a),a.dirty=!0);var s=tL(t,r),u=o[s],l=(0,k.isFunction)(u)?u:null,c="auto"===o.fill||"auto"===o.stroke;if(!o[s]||l||c){var h=t.getColorFromPalette(t.name,null,e.getSeriesCount());o[s]||(o[s]=h,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||(0,k.isFunction)(o.fill)?h:o.fill,o.stroke="auto"===o.stroke||(0,k.isFunction)(o.stroke)?h:o.stroke}if(n.setVisual("style",o),n.setVisual("drawType",s),!e.isSeriesFiltered(t)&&l)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var r=t.getDataParams(n),i=(0,k.extend)({},o);i[s]=l(r),e.setItemVisual(n,"style",i)}}}}),e0(4500,{createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!(t.ignoreStyleOnData||e.isSeriesFiltered(t))){var n=t.getData(),r=t.visualStyleAccessPath||"itemStyle",i=tP(t,r),o=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[r]){tR.option=n[r];var a=i(tR),s=t.ensureUniqueItemVisual(e,"style");(0,k.extend)(s,a),tR.option.decal&&(t.setItemVisual(e,"decal",tR.option.decal),tR.option.decal.dirty=!0),o in a&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}}),e0(4500,{performRawSeries:!0,overallReset:function(t){var e=(0,k.createHashMap)();t.eachSeries(function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var r=t.type+"-"+n,i=e.get(r);i||(i={},e.set(r,i)),tI(t).scope=i}}),t.eachSeries(function(e){if(!(e.isColorBySeries()||t.isSeriesFiltered(e))){var n=e.getRawData(),r={},i=e.getData(),o=tI(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=tL(e,a);i.each(function(t){r[i.getRawIndex(t)]=t}),n.each(function(t){var a=r[t];if(i.getItemVisual(a,"colorFromPalette")){var u=i.ensureUniqueItemVisual(a,"style"),l=n.getName(t)||t+"",c=n.count();u[s]=e.getColorFromPalette(l,o,c)}})}})}}),e0(2e3,{createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var r={},i={},o=!1,a=0;a<t7.length;a++){var s=t7[a],u=t.get(s);(0,k.isFunction)(u)?(o=!0,i[s]=u):r[s]=u}if(r.symbol=r.symbol||t.defaultSymbol,n.setVisual((0,k.extend)({legendIcon:t.legendIcon||r.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},r)),!e.isSeriesFiltered(t)){var l=(0,k.keys)(i);return{dataEach:o?function(e,n){for(var r=t.getRawValue(n),o=t.getDataParams(n),a=0;a<l.length;a++){var s=l[a];e.setItemVisual(n,s,i[s](r,o))}}:null}}}}}),e0(4500,{createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),r=0;r<t9.length;r++){var i=t9[r],o=n.getShallow(i,!0);null!=o&&t.setItemVisual(e,i,o)}}:null}}}),e0(7e3,function(t,e){t.eachRawSeries(function(n){if(!t.isSeriesFiltered(n)){var r=n.getData();r.hasItemVisual()&&r.each(function(t){var n=r.getItemVisual(t,"decal");n&&(r.ensureUniqueItemVisual(t,"style").decal=(0,eo.w)(n,e))});var i=r.getVisual("decal");i&&(r.getVisual("style").decal=(0,eo.w)(i,e))}})}),eG(tp),eq(900,function(t){var e=(0,k.createHashMap)();t.eachSeries(function(t){var n=t.get("stack");if(n){var r=e.get(n)||e.set(n,[]),i=t.getData(),o={stackResultDimension:i.getCalculationInfo("stackResultDimension"),stackedOverDimension:i.getCalculationInfo("stackedOverDimension"),stackedDimension:i.getCalculationInfo("stackedDimension"),stackedByDimension:i.getCalculationInfo("stackedByDimension"),isStackedByIndex:i.getCalculationInfo("isStackedByIndex"),data:i,seriesModel:t};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;r.length&&i.setCalculationInfo("stackedOnSeries",r[r.length-1].seriesModel),r.push(o)}}),e.each(tg)}),eO.default=function(t,e){e=e||{},k.defaults(e,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n,r=new tE.A,i=new t_.A({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});r.add(i);var o=new tN.Ay({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),a=new t_.A({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return r.add(a),e.showSpinner&&((n=new tB.A({shape:{startAngle:-tF/2,endAngle:-tF/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*tF/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*tF/2}).delay(300).start("circularInOut"),r.add(n)),r.resize=function(){var r=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,u=(t.getWidth()-2*s-(e.showSpinner&&r?10:0)-r)/2-(e.showSpinner&&r?0:5+r/2)+(e.showSpinner?0:r/2)+(r?0:s),l=t.getHeight()/2;e.showSpinner&&n.setShape({cx:u,cy:l}),a.setShape({x:u-s,y:l-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},r.resize(),r},e$({type:tM.h5,event:tM.h5,update:tM.h5},k.noop),e$({type:tM.PW,event:tM.PW,update:tM.PW},k.noop),e$({type:tM.Lv,event:tM.Lv,update:tM.Lv},k.noop),e$({type:tM.U2,event:tM.U2,update:tM.U2},k.noop),e$({type:tM.Q6,event:tM.Q6,update:tM.Q6},k.noop),eD.light={color:t0,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],t0]},eD.dark=t4;var e7={}},411:(t,e,n)=>{"use strict";n.r(e),n.d(e,{fastLerp:()=>x,fastMapToColor:()=>b,lerp:()=>w,lift:()=>m,liftColor:()=>D,lum:()=>k,mapToColor:()=>S,modifyAlpha:()=>A,modifyHSL:()=>M,parse:()=>y,random:()=>C,stringify:()=>T,toHex:()=>_});var r=n(4948),i=n(4123),o={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function a(t){return(t=Math.round(t))<0?0:t>255?255:t}function s(t){return t<0?0:t>1?1:t}function u(t){return t.length&&"%"===t.charAt(t.length-1)?a(parseFloat(t)/100*255):a(parseInt(t,10))}function l(t){return t.length&&"%"===t.charAt(t.length-1)?s(parseFloat(t)/100):s(parseFloat(t))}function c(t,e,n){return(n<0?n+=1:n>1&&(n-=1),6*n<1)?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function h(t,e,n,r,i){return t[0]=e,t[1]=n,t[2]=r,t[3]=i,t}function f(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var p=new r.Ay(20),d=null;function g(t,e){d&&f(d,e),d=p.put(t,d||e.slice())}function y(t,e){if(t){e=e||[];var n=p.get(t);if(n)return f(e,n);var r=(t+="").replace(/ /g,"").toLowerCase();if(r in o)return f(e,o[r]),g(t,e),e;var i=r.length;if("#"===r.charAt(0)){if(4===i||5===i){var a=parseInt(r.slice(1,4),16);return a>=0&&a<=4095?(h(e,(3840&a)>>4|(3840&a)>>8,240&a|(240&a)>>4,15&a|(15&a)<<4,5===i?parseInt(r.slice(4),16)/15:1),g(t,e),e):void h(e,0,0,0,1)}if(7===i||9===i){var a=parseInt(r.slice(1,7),16);return a>=0&&a<=0xffffff?(h(e,(0xff0000&a)>>16,(65280&a)>>8,255&a,9===i?parseInt(r.slice(7),16)/255:1),g(t,e),e):void h(e,0,0,0,1)}return}var s=r.indexOf("("),c=r.indexOf(")");if(-1!==s&&c+1===i){var d=r.substr(0,s),y=r.substr(s+1,c-(s+1)).split(","),m=1;switch(d){case"rgba":if(4!==y.length)return 3===y.length?h(e,+y[0],+y[1],+y[2],1):h(e,0,0,0,1);m=l(y.pop());case"rgb":if(y.length>=3)return h(e,u(y[0]),u(y[1]),u(y[2]),3===y.length?m:l(y[3])),g(t,e),e;return void h(e,0,0,0,1);case"hsla":if(4!==y.length)return void h(e,0,0,0,1);return y[3]=l(y[3]),v(y,e),g(t,e),e;case"hsl":if(3!==y.length)return void h(e,0,0,0,1);return v(y,e),g(t,e),e;default:return}}h(e,0,0,0,1)}}function v(t,e){var n=(parseFloat(t[0])%360+360)%360/360,r=l(t[1]),i=l(t[2]),o=i<=.5?i*(r+1):i+r-i*r,s=2*i-o;return h(e=e||[],a(255*c(s,o,n+1/3)),a(255*c(s,o,n)),a(255*c(s,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function m(t,e){var n=y(t);if(n){for(var r=0;r<3;r++)e<0?n[r]=n[r]*(1-e)|0:n[r]=(255-n[r])*e+n[r]|0,n[r]>255?n[r]=255:n[r]<0&&(n[r]=0);return T(n,4===n.length?"rgba":"rgb")}}function _(t){var e=y(t);if(e)return(0x1000000+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function x(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var r,i,o,u,l=t*(e.length-1),c=Math.floor(l),h=Math.ceil(l),f=e[c],p=e[h],d=l-c;return n[0]=a((r=f[0],r+(p[0]-r)*d)),n[1]=a((i=f[1],i+(p[1]-i)*d)),n[2]=a((o=f[2],o+(p[2]-o)*d)),n[3]=s((u=f[3],u+(p[3]-u)*d)),n}}var b=x;function w(t,e,n){if(e&&e.length&&t>=0&&t<=1){var r,i,o,u,l=t*(e.length-1),c=Math.floor(l),h=Math.ceil(l),f=y(e[c]),p=y(e[h]),d=l-c,g=T([a((r=f[0],r+(p[0]-r)*d)),a((i=f[1],i+(p[1]-i)*d)),a((o=f[2],o+(p[2]-o)*d)),s((u=f[3],u+(p[3]-u)*d))],"rgba");return n?{color:g,leftIndex:c,rightIndex:h,value:l}:g}}var S=w;function M(t,e,n,r){var i,o=y(t);if(t)return o=function(t){if(t){var e,n,r=t[0]/255,i=t[1]/255,o=t[2]/255,a=Math.min(r,i,o),s=Math.max(r,i,o),u=s-a,l=(s+a)/2;if(0===u)e=0,n=0;else{n=l<.5?u/(s+a):u/(2-s-a);var c=((s-r)/6+u/2)/u,h=((s-i)/6+u/2)/u,f=((s-o)/6+u/2)/u;r===s?e=f-h:i===s?e=1/3+c-f:o===s&&(e=2/3+h-c),e<0&&(e+=1),e>1&&(e-=1)}var p=[360*e,n,l];return null!=t[3]&&p.push(t[3]),p}}(o),null!=e&&(o[0]=(i=Math.round(i=e))<0?0:i>360?360:i),null!=n&&(o[1]=l(n)),null!=r&&(o[2]=l(r)),T(v(o),"rgba")}function A(t,e){var n=y(t);if(n&&null!=e)return n[3]=s(e),T(n,"rgba")}function T(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(n+=","+t[3]),e+"("+n+")"}}function k(t,e){var n=y(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}function C(){return T([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")}var I=new r.Ay(100);function D(t){if((0,i.isString)(t)){var e=I.get(t);return e||(e=m(t,-.1),I.put(t,e)),e}if((0,i.isGradientObject)(t)){var n=(0,i.extend)({},t);return n.colorStops=(0,i.map)(t.colorStops,function(t){return{offset:t.offset,color:m(t.color,-.1)}}),n}return t}},442:(t,e,n)=>{"use strict";function r(t){return null==t?0:t.length||1}function i(t){return t}n.d(e,{A:()=>o});let o=function(){function t(t,e,n,r,o,a){this._old=t,this._new=e,this._oldKeyGetter=n||i,this._newKeyGetter=r||i,this.context=o,this._diffModeMultiple="multiple"===a}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=Array(t.length),o=Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var a=0;a<t.length;a++){var s=i[a],u=n[s],l=r(u);if(l>1){var c=u.shift();1===u.length&&(n[s]=u[0]),this._update&&this._update(c,a)}else 1===l?(n[s]=null,this._update&&this._update(u,a)):this._remove&&this._remove(a)}this._performRestAdd(o,n)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},o=[],a=[];this._initIndexMap(t,n,o,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var s=0;s<o.length;s++){var u=o[s],l=n[u],c=i[u],h=r(l),f=r(c);if(h>1&&1===f)this._updateManyToOne&&this._updateManyToOne(c,l),i[u]=null;else if(1===h&&f>1)this._updateOneToMany&&this._updateOneToMany(c,l),i[u]=null;else if(1===h&&1===f)this._update&&this._update(c,l),i[u]=null;else if(h>1&&f>1)this._updateManyToMany&&this._updateManyToMany(c,l),i[u]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(a,i)},t.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],o=e[i],a=r(o);if(a>1)for(var s=0;s<a;s++)this._add&&this._add(o[s]);else 1===a&&this._add&&this._add(o);e[i]=null}},t.prototype._initIndexMap=function(t,e,n,i){for(var o=this._diffModeMultiple,a=0;a<t.length;a++){var s="_ec_"+this[i](t[a],a);if(o||(n[a]=s),e){var u=e[s],l=r(u);0===l?(e[s]=a,o&&n.push(s)):1===l?e[s]=[u,a]:u.push(a)}}},t}()},514:(t,e,n)=>{"use strict";n.d(e,{BZ:()=>Q,KN:()=>J,wt:()=>tt,cg:()=>U,Qo:()=>H,gB:()=>G,RG:()=>K,Gs:()=>X,cP:()=>q,uc:()=>Z,dQ:()=>W,rq:()=>$,o4:()=>tn});var r=n(643),i=n(4271),o=n(8248),a=n(1148),s=o.A.CMD,u=[[],[],[]],l=Math.sqrt,c=Math.atan2,h=n(4123),f=Math.sqrt,p=Math.sin,d=Math.cos,g=Math.PI;function y(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function v(t,e){return(t[0]*e[0]+t[1]*e[1])/(y(t)*y(e))}function m(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(v(t,e))}function _(t,e,n,r,i,o,a,s,u,l,c){var h=g/180*u,y=d(h)*(t-n)/2+p(h)*(e-r)/2,_=-1*p(h)*(t-n)/2+d(h)*(e-r)/2,x=y*y/(a*a)+_*_/(s*s);x>1&&(a*=f(x),s*=f(x));var b=(i===o?-1:1)*f((a*a*(s*s)-a*a*(_*_)-s*s*(y*y))/(a*a*(_*_)+s*s*(y*y)))||0,w=b*a*_/s,S=-(b*s)*y/a,M=(t+n)/2+d(h)*w-p(h)*S,A=(e+r)/2+p(h)*w+d(h)*S,T=m([1,0],[(y-w)/a,(_-S)/s]),k=[(y-w)/a,(_-S)/s],C=[(-1*y-w)/a,(-1*_-S)/s],I=m(k,C);if(-1>=v(k,C)&&(I=g),v(k,C)>=1&&(I=0),I<0){var D=Math.round(I/g*1e6)/1e6;I=2*g+D%2*g}c.addData(l,M,A,a,s,T,I,h,o)}var x=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,b=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.__extends)(e,t),e.prototype.applyTransform=function(t){},e}(i.Ay);function S(t){return null!=t.setData}function M(t,e){var n=function(t){var e,n=new o.A;if(!t)return n;var r=0,i=0,a=0,s=0,u=o.A.CMD,l=t.match(x);if(!l)return n;for(var c=0;c<l.length;c++){for(var h=l[c],f=h.charAt(0),p=void 0,d=h.match(b)||[],g=d.length,y=0;y<g;y++)d[y]=parseFloat(d[y]);for(var v=0;v<g;){var m=void 0,w=void 0,S=void 0,M=void 0,A=void 0,T=void 0,k=void 0,C=r,I=i,D=void 0,O=void 0;switch(f){case"l":r+=d[v++],i+=d[v++],p=u.L,n.addData(p,r,i);break;case"L":r=d[v++],i=d[v++],p=u.L,n.addData(p,r,i);break;case"m":r+=d[v++],i+=d[v++],p=u.M,n.addData(p,r,i),a=r,s=i,f="l";break;case"M":r=d[v++],i=d[v++],p=u.M,n.addData(p,r,i),a=r,s=i,f="L";break;case"h":r+=d[v++],p=u.L,n.addData(p,r,i);break;case"H":r=d[v++],p=u.L,n.addData(p,r,i);break;case"v":i+=d[v++],p=u.L,n.addData(p,r,i);break;case"V":i=d[v++],p=u.L,n.addData(p,r,i);break;case"C":p=u.C,n.addData(p,d[v++],d[v++],d[v++],d[v++],d[v++],d[v++]),r=d[v-2],i=d[v-1];break;case"c":p=u.C,n.addData(p,d[v++]+r,d[v++]+i,d[v++]+r,d[v++]+i,d[v++]+r,d[v++]+i),r+=d[v-2],i+=d[v-1];break;case"S":m=r,w=i,D=n.len(),O=n.data,e===u.C&&(m+=r-O[D-4],w+=i-O[D-3]),p=u.C,C=d[v++],I=d[v++],r=d[v++],i=d[v++],n.addData(p,m,w,C,I,r,i);break;case"s":m=r,w=i,D=n.len(),O=n.data,e===u.C&&(m+=r-O[D-4],w+=i-O[D-3]),p=u.C,C=r+d[v++],I=i+d[v++],r+=d[v++],i+=d[v++],n.addData(p,m,w,C,I,r,i);break;case"Q":C=d[v++],I=d[v++],r=d[v++],i=d[v++],p=u.Q,n.addData(p,C,I,r,i);break;case"q":C=d[v++]+r,I=d[v++]+i,r+=d[v++],i+=d[v++],p=u.Q,n.addData(p,C,I,r,i);break;case"T":m=r,w=i,D=n.len(),O=n.data,e===u.Q&&(m+=r-O[D-4],w+=i-O[D-3]),r=d[v++],i=d[v++],p=u.Q,n.addData(p,m,w,r,i);break;case"t":m=r,w=i,D=n.len(),O=n.data,e===u.Q&&(m+=r-O[D-4],w+=i-O[D-3]),r+=d[v++],i+=d[v++],p=u.Q,n.addData(p,m,w,r,i);break;case"A":S=d[v++],M=d[v++],A=d[v++],T=d[v++],k=d[v++],C=r,I=i,r=d[v++],_(C,I,r,i=d[v++],T,k,S,M,A,p=u.A,n);break;case"a":S=d[v++],M=d[v++],A=d[v++],T=d[v++],k=d[v++],C=r,I=i,r+=d[v++],_(C,I,r,i+=d[v++],T,k,S,M,A,p=u.A,n)}}("z"===f||"Z"===f)&&(p=u.Z,n.addData(p),r=a,i=s),e=p}return n.toStatic(),n}(t),r=(0,h.extend)({},e);return r.buildPath=function(t){if(S(t)){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e,1)}else{var e=t;n.rebuildPath(e,1)}},r.applyTransform=function(t){!function(t,e){if(e){var n,r,i,o,h,f,p=t.data,d=t.len(),g=s.M,y=s.C,v=s.L,m=s.R,_=s.A,x=s.Q;for(i=0,o=0;i<d;){switch(n=p[i++],o=i,r=0,n){case g:case v:r=1;break;case y:r=3;break;case x:r=2;break;case _:var b=e[4],w=e[5],S=l(e[0]*e[0]+e[1]*e[1]),M=l(e[2]*e[2]+e[3]*e[3]),A=c(-e[1]/M,e[0]/S);p[i]*=S,p[i++]+=b,p[i]*=M,p[i++]+=w,p[i++]*=S,p[i++]*=M,p[i++]+=A,p[i++]+=A,i+=2,o=i;break;case m:f[0]=p[i++],f[1]=p[i++],(0,a.applyTransform)(f,f,e),p[o++]=f[0],p[o++]=f[1],f[0]+=p[i++],f[1]+=p[i++],(0,a.applyTransform)(f,f,e),p[o++]=f[0],p[o++]=f[1]}for(h=0;h<r;h++){var T=u[h];T[0]=p[i++],T[1]=p[i++],(0,a.applyTransform)(T,T,e),p[o++]=T[0],p[o++]=T[1]}}t.increaseVersion()}}(n,t),this.dirtyShape()},r}var A=n(8346),T=n(3493),k=n(6586),C=n(5960),I=n(7143),D=n(1132),O=n(8544),P=n(9562),L=n(7821),R=n(3948),E=n(6864),N=n(9950),B=n(6600),F=Math.max,z=Math.min,V={};function H(t){return i.Ay.extend(t)}var j=function(t,e){var n=M(t,e),i=w;function o(t){var e=i.call(this,t)||this;return e.applyTransform=n.applyTransform,e.buildPath=n.buildPath,e}return(0,r.__extends)(o,i),o};function U(t,e){return j(t,e)}function W(t,e){V[t]=e}function G(t){if(V.hasOwnProperty(t))return V[t]}function q(t,e,n,r){var i=new w(M(t,e));return n&&("center"===r&&(n=Y(n,i.getBoundingRect())),$(i,n)),i}function X(t,e,n){var r=new T.Ay({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var i={width:t.width,height:t.height};r.setStyle(Y(e,i))}}});return r}function Y(t,e){var n,r=e.width/e.height,i=t.height*r;return n=i<=t.width?t.height:(i=t.width)/r,{x:t.x+t.width/2-i/2,y:t.y+t.height/2-n/2,width:i,height:n}}var Z=function(t,e){for(var n=[],r=t.length,o=0;o<r;o++){var a=t[o];n.push(a.getUpdatedPathProxy(!0))}var s=new i.Ay(e);return s.createPathProxy(),s.buildPath=function(t){if(S(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},s};function $(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function K(t,e){for(var n=A.identity([]);t&&t!==e;)A.mul(n,t.getLocalTransform(),n),t=t.parent;return n}function Q(t,e){return(0,h.map)(t,function(t){var n=t[0];n=z(n=F(n,e.x),e.x+e.width);var r=t[1];return[n,r=z(r=F(r,e.y),e.y+e.height)]})}function J(t,e){var n=F(t.x,e.x),r=z(t.x+t.width,e.x+e.width),i=F(t.y,e.y),o=z(t.y+t.height,e.y+e.height);if(r>=n&&o>=i)return{x:n,y:i,width:r-n,height:o-i}}function tt(t,e,n){var r=(0,h.extend)({rectHover:!0},e),i=r.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),(0,h.defaults)(i,n),new T.Ay(r)):q(t.replace("path://",""),r,n,"center")}function te(t,e){var n;t.isGroup&&(n=e(t)),n||t.traverse(e)}function tn(t,e){if(t)if((0,h.isArray)(t))for(var n=0;n<t.length;n++)te(t[n],e);else te(t,e)}B.M7,V.circle=k.A,V.ellipse=C.A,V.sector=I.A,V.ring=D.A,V.polygon=O.A,V.polyline=P.A,V.rect=L.A,V.line=R.A,V.bezierCurve=E.A,V.arc=N.A},643:(t,e,n)=>{"use strict";n.r(e),n.d(e,{__assign:()=>o,__asyncDelegator:()=>b,__asyncGenerator:()=>x,__asyncValues:()=>w,__await:()=>_,__awaiter:()=>c,__classPrivateFieldGet:()=>k,__classPrivateFieldSet:()=>C,__createBinding:()=>f,__decorate:()=>s,__exportStar:()=>p,__extends:()=>i,__generator:()=>h,__importDefault:()=>T,__importStar:()=>A,__makeTemplateObject:()=>S,__metadata:()=>l,__param:()=>u,__read:()=>g,__rest:()=>a,__spread:()=>y,__spreadArray:()=>m,__spreadArrays:()=>v,__values:()=>d});var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return(o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function a(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)0>e.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n}function s(t,e,n,r){var i,o=arguments.length,a=o<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(o<3?i(a):o>3?i(e,n,a):i(e,n))||a);return o>3&&a&&Object.defineProperty(e,n,a),a}function u(t,e){return function(n,r){e(n,r,t)}}function l(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function c(t,e,n,r){return new(n||(n=Promise))(function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function s(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(a,s)}u((r=r.apply(t,e||[])).next())})}function h(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){var u=[o,s];if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){a.label=u[1];break}if(6===u[0]&&a.label<i[1]){a.label=i[1],i=u;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(u);break}i[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],r=0}finally{n=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}}var f=Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]};function p(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||f(e,t,n)}function d(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function y(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(g(arguments[e]));return t}function v(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;for(var r=Array(t),i=0,e=0;e<n;e++)for(var o=arguments[e],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r}function m(t,e,n){if(n||2==arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||e)}function _(t){return this instanceof _?(this.v=t,this):new _(t)}function x(t,e,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(t,e||[]),o=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(t){i[t]&&(r[t]=function(e){return new Promise(function(n,r){o.push([t,e,n,r])>1||s(t,e)})})}function s(t,e){try{var n;(n=i[t](e)).value instanceof _?Promise.resolve(n.value.v).then(u,l):c(o[0][2],n)}catch(t){c(o[0][3],t)}}function u(t){s("next",t)}function l(t){s("throw",t)}function c(t,e){t(e),o.shift(),o.length&&s(o[0][0],o[0][1])}}function b(t){var e,n;return e={},r("next"),r("throw",function(t){throw t}),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,i){e[r]=t[r]?function(e){return(n=!n)?{value:_(t[r](e)),done:"return"===r}:i?i(e):e}:i}}function w(t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=d(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise(function(r,i){var o,a,s;o=r,a=i,s=(e=t[n](e)).done,Promise.resolve(e.value).then(function(t){o({value:t,done:s})},a)})}}}function S(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}var M=Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e};function A(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&f(e,t,n);return M(e,t),e}function T(t){return t&&t.__esModule?t:{default:t}}function k(t,e,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(t):r?r.value:e.get(t)}function C(t,e,n,r,i){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?i.call(t,n):i?i.value=n:e.set(t,n),n}},706:(t,e,n)=>{"use strict";n.d(e,{A:()=>f});var r=n(643),i=n(4123),o=n(1613),a=n(1929),s=n(2660),u=n(6187),l=n(4524),c=(0,u.$r)(),h=function(t){var e;function n(e,n,r){var i=t.call(this,e,n,r)||this;return i.uid=a.$Q("ec_cpt_model"),i}return(0,r.__extends)(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},n.prototype.mergeDefaultAndTheme=function(t,e){var n=l.ad(this),r=n?l.vs(t):{},o=e.getTheme();i.merge(t,o.get(this.mainType)),i.merge(t,this.getDefaultOption()),n&&l.YA(t,r,n)},n.prototype.mergeOption=function(t,e){i.merge(this.option,t,!0);var n=l.ad(this);n&&l.YA(this.option,t,n)},n.prototype.optionUpdated=function(t,e){},n.prototype.getDefaultOption=function(){var t=this.constructor;if(!(0,s._E)(t))return t.defaultOption;var e=c(this);if(!e.defaultOption){for(var n=[],r=t;r;){var o=r.prototype.defaultOption;o&&n.push(o),r=r.superClass}for(var a={},u=n.length-1;u>=0;u--)a=i.merge(a,n[u],!0);e.defaultOption=a}return e.defaultOption},n.prototype.getReferringComponents=function(t,e){return(0,u.JO)(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)},e)},n.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},n.prototype.getZLevelKey=function(){return""},n.prototype.setZLevel=function(t){this.option.zlevel=t},n.protoInitialize=void((e=n.prototype).type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0),n}(o.A);(0,s.q7)(h,o.A),(0,s.tQ)(h),a.A6(h),a.vf(h,function(t){var e=[];return i.each(h.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=i.map(e,function(t){return(0,s.CC)(t).main}),"dataset"!==t&&0>=i.indexOf(e,"dataset")&&e.unshift("dataset"),e});let f=h},882:(t,e,n)=>{"use strict";n.d(e,{Et:()=>m,YT:()=>v,Yb:()=>p,_E:()=>g,d8:()=>T,gC:()=>S,h0:()=>_,k3:()=>x,kh:()=>A,kx:()=>M,lX:()=>y,qY:()=>w,rD:()=>d,z7:()=>b});var r=n(1148),i=Math.pow,o=Math.sqrt,a=o(3),s=1/3,u=(0,r.create)(),l=(0,r.create)(),c=(0,r.create)();function h(t){return t>-1e-8&&t<1e-8}function f(t){return t>1e-8||t<-1e-8}function p(t,e,n,r,i){var o=1-i;return o*o*(o*t+3*i*e)+i*i*(i*r+3*o*n)}function d(t,e,n,r,i){var o=1-i;return 3*(((e-t)*o+2*(n-e)*i)*o+(r-n)*i*i)}function g(t,e,n,r,u,l){var c=r+3*(e-n)-t,f=3*(n-2*e+t),p=3*(e-t),d=t-u,g=f*f-3*c*p,y=f*p-9*c*d,v=p*p-3*f*d,m=0;if(h(g)&&h(y))if(h(f))l[0]=0;else{var _=-p/f;_>=0&&_<=1&&(l[m++]=_)}else{var x=y*y-4*g*v;if(h(x)){var b=y/g,_=-f/c+b,w=-b/2;_>=0&&_<=1&&(l[m++]=_),w>=0&&w<=1&&(l[m++]=w)}else if(x>0){var S=o(x),M=g*f+1.5*c*(-y+S),A=g*f+1.5*c*(-y-S),_=(-f-((M=M<0?-i(-M,s):i(M,s))+(A=A<0?-i(-A,s):i(A,s))))/(3*c);_>=0&&_<=1&&(l[m++]=_)}else{var T=Math.acos((2*g*f-3*c*y)/(2*o(g*g*g)))/3,k=o(g),C=Math.cos(T),_=(-f-2*k*C)/(3*c),w=(-f+k*(C+a*Math.sin(T)))/(3*c),I=(-f+k*(C-a*Math.sin(T)))/(3*c);_>=0&&_<=1&&(l[m++]=_),w>=0&&w<=1&&(l[m++]=w),I>=0&&I<=1&&(l[m++]=I)}}return m}function y(t,e,n,r,i){var a=6*n-12*e+6*t,s=9*e+3*r-3*t-9*n,u=3*e-3*t,l=0;if(h(s)){if(f(a)){var c=-u/a;c>=0&&c<=1&&(i[l++]=c)}}else{var p=a*a-4*s*u;if(h(p))i[0]=-a/(2*s);else if(p>0){var d=o(p),c=(-a+d)/(2*s),g=(-a-d)/(2*s);c>=0&&c<=1&&(i[l++]=c),g>=0&&g<=1&&(i[l++]=g)}}return l}function v(t,e,n,r,i,o){var a=(e-t)*i+t,s=(n-e)*i+e,u=(r-n)*i+n,l=(s-a)*i+a,c=(u-s)*i+s,h=(c-l)*i+l;o[0]=t,o[1]=a,o[2]=l,o[3]=h,o[4]=h,o[5]=c,o[6]=u,o[7]=r}function m(t,e,n,i,a,s,h,f,d,g,y){var v,m,_,x,b,w=.005,S=1/0;u[0]=d,u[1]=g;for(var M=0;M<1;M+=.05)l[0]=p(t,n,a,h,M),l[1]=p(e,i,s,f,M),(x=(0,r.distSquare)(u,l))<S&&(v=M,S=x);S=1/0;for(var A=0;A<32&&!(w<1e-4);A++)m=v-w,_=v+w,l[0]=p(t,n,a,h,m),l[1]=p(e,i,s,f,m),x=(0,r.distSquare)(l,u),m>=0&&x<S?(v=m,S=x):(c[0]=p(t,n,a,h,_),c[1]=p(e,i,s,f,_),b=(0,r.distSquare)(c,u),_<=1&&b<S?(v=_,S=b):w*=.5);return y&&(y[0]=p(t,n,a,h,v),y[1]=p(e,i,s,f,v)),o(S)}function _(t,e,n,r,i,o,a,s,u){for(var l=t,c=e,h=0,f=1/u,d=1;d<=u;d++){var g=d*f,y=p(t,n,i,a,g),v=p(e,r,o,s,g),m=y-l,_=v-c;h+=Math.sqrt(m*m+_*_),l=y,c=v}return h}function x(t,e,n,r){var i=1-r;return i*(i*t+2*r*e)+r*r*n}function b(t,e,n,r){return 2*((1-r)*(e-t)+r*(n-e))}function w(t,e,n,r,i){var a=t-2*e+n,s=2*(e-t),u=t-r,l=0;if(h(a)){if(f(s)){var c=-u/s;c>=0&&c<=1&&(i[l++]=c)}}else{var p=s*s-4*a*u;if(h(p)){var c=-s/(2*a);c>=0&&c<=1&&(i[l++]=c)}else if(p>0){var d=o(p),c=(-s+d)/(2*a),g=(-s-d)/(2*a);c>=0&&c<=1&&(i[l++]=c),g>=0&&g<=1&&(i[l++]=g)}}return l}function S(t,e,n){var r=t+n-2*e;return 0===r?.5:(t-e)/r}function M(t,e,n,r,i){var o=(e-t)*r+t,a=(n-e)*r+e,s=(a-o)*r+o;i[0]=t,i[1]=o,i[2]=s,i[3]=s,i[4]=a,i[5]=n}function A(t,e,n,i,a,s,h,f,p){var d,g=.005,y=1/0;u[0]=h,u[1]=f;for(var v=0;v<1;v+=.05){l[0]=x(t,n,a,v),l[1]=x(e,i,s,v);var m=(0,r.distSquare)(u,l);m<y&&(d=v,y=m)}y=1/0;for(var _=0;_<32&&!(g<1e-4);_++){var b=d-g,w=d+g;l[0]=x(t,n,a,b),l[1]=x(e,i,s,b);var m=(0,r.distSquare)(l,u);if(b>=0&&m<y)d=b,y=m;else{c[0]=x(t,n,a,w),c[1]=x(e,i,s,w);var S=(0,r.distSquare)(c,u);w<=1&&S<y?(d=w,y=S):g*=.5}}return p&&(p[0]=x(t,n,a,d),p[1]=x(e,i,s,d)),o(y)}function T(t,e,n,r,i,o,a){for(var s=t,u=e,l=0,c=1/a,h=1;h<=a;h++){var f=h*c,p=x(t,n,i,f),d=x(e,r,o,f),g=p-s,y=d-u;l+=Math.sqrt(g*g+y*y),s=p,u=d}return l}},1132:(t,e,n)=>{"use strict";n.d(e,{A:()=>s});var r=n(643),i=n(4271),o=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,r=e.cy,i=2*Math.PI;t.moveTo(n+e.r,r),t.arc(n,r,e.r,0,i,!1),t.moveTo(n+e.r0,r),t.arc(n,r,e.r0,0,i,!0)},e}(i.Ay);a.prototype.type="ring";let s=a},1148:(t,e,n)=>{"use strict";function r(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function i(t,e){return t[0]=e[0],t[1]=e[1],t}function o(t){return[t[0],t[1]]}function a(t,e,n){return t[0]=e,t[1]=n,t}function s(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function u(t,e,n,r){return t[0]=e[0]+n[0]*r,t[1]=e[1]+n[1]*r,t}function l(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function c(t){return Math.sqrt(f(t))}n.r(e),n.d(e,{add:()=>s,applyTransform:()=>A,clone:()=>o,copy:()=>i,create:()=>r,dist:()=>x,distSquare:()=>w,distance:()=>_,distanceSquare:()=>b,div:()=>g,dot:()=>y,len:()=>c,lenSquare:()=>f,length:()=>h,lengthSquare:()=>p,lerp:()=>M,max:()=>k,min:()=>T,mul:()=>d,negate:()=>S,normalize:()=>m,scale:()=>v,scaleAndAdd:()=>u,set:()=>a,sub:()=>l});var h=c;function f(t){return t[0]*t[0]+t[1]*t[1]}var p=f;function d(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t}function g(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t}function y(t,e){return t[0]*e[0]+t[1]*e[1]}function v(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function m(t,e){var n=c(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function _(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var x=_;function b(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var w=b;function S(t,e){return t[0]=-e[0],t[1]=-e[1],t}function M(t,e,n,r){return t[0]=e[0]+r*(n[0]-e[0]),t[1]=e[1]+r*(n[1]-e[1]),t}function A(t,e,n){var r=e[0],i=e[1];return t[0]=n[0]*r+n[2]*i+n[4],t[1]=n[1]*r+n[3]*i+n[5],t}function T(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function k(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}},1270:(t,e,n)=>{"use strict";function r(t,e,n){var r,i,o,a,s,u=0,l=0,c=null;function h(){l=new Date().getTime(),c=null,t.apply(o,a||[])}e=e||0;var f=function(){for(var t=[],f=0;f<arguments.length;f++)t[f]=arguments[f];r=new Date().getTime(),o=this,a=t;var p=s||e,d=s||n;s=null,i=r-(d?u:l)-p,clearTimeout(c),d?c=setTimeout(h,p):i>=0?h():c=setTimeout(h,-i),u=r};return f.clear=function(){c&&(clearTimeout(c),c=null)},f.debounceNextCall=function(t){s=t},f}n.d(e,{nF:()=>r})},1294:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});var r=n(643),i=n(4271);let o=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return(0,r.__extends)(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],r=0;r<n.length;r++)n[r].buildPath(t,n[r].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),i.Ay.prototype.getBoundingRect.call(this)},e}(i.Ay)},1425:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createSensor=void 0;var r=n(3615),i=function(t){return t&&t.__esModule?t:{default:t}}(n(5692));e.createSensor=function(t,e){var n=void 0,o=[],a=(0,i.default)(function(){o.forEach(function(e){e(t)})}),s=function(){var e=new ResizeObserver(a);return e.observe(t),a(),e},u=function(){n.disconnect(),o=[],n=void 0,t.removeAttribute(r.SizeSensorId),e&&e()};return{element:t,bind:function(t){n||(n=s()),-1===o.indexOf(t)&&o.push(t)},destroy:u,unbind:function(t){var e=o.indexOf(t);-1!==e&&o.splice(e,1),0===o.length&&n&&u()}}}},1531:(t,e,n)=>{"use strict";n.d(e,{L:()=>i,s:()=>a});var r=n(3809),i=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],o=(0,r.A)(i),a=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return o(this,t,e)},t}()},1585:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(2115);let i=r.forwardRef(function({title:t,titleId:e,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},n),t?r.createElement("title",{id:e},t):null,r.createElement("path",{fillRule:"evenodd",d:"M8 2a.75.75 0 0 1 .75.75v8.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.22 3.22V2.75A.75.75 0 0 1 8 2Z",clipRule:"evenodd"}))})},1598:(t,e,n)=>{"use strict";n.d(e,{A:()=>f,M:()=>h});var r=n(7439),i=n(9486),o=n(4123),a=n(8535),s=n(2899),u=n(6187),l=n(8142),c=n(4673);function h(t,e){return f(t,e).dimensions}function f(t,e){(0,a.tP)(t)||(t=(0,a.AF)(t));var n,h,f,p,d,g=(e=e||{}).coordDimensions||[],y=e.dimensionsDefine||t.dimensionsDefine||[],v=(0,o.createHashMap)(),m=[],_=(n=t,h=g,f=y,p=e.dimensionsCount,d=Math.max(n.dimensionsDetectedCount||1,h.length,f.length,p||0),(0,o.each)(h,function(t){var e;(0,o.isObject)(t)&&(e=t.dimsDef)&&(d=Math.max(d,e.length))}),d),x=e.canOmitUnusedDimensions&&(0,c.eS)(_),b=y===t.dimensionsDefine,w=b?(0,c.j_)(t):(0,c.io)(y),S=e.encodeDefine;!S&&e.encodeDefaulter&&(S=e.encodeDefaulter(t,_));for(var M=(0,o.createHashMap)(S),A=new s.A_(_),T=0;T<A.length;T++)A[T]=-1;function k(t){var e=A[t];if(e<0){var n=y[t],r=(0,o.isObject)(n)?n:{name:n},a=new i.A,s=r.name;null!=s&&null!=w.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var u=m.length;return A[t]=u,a.storeDimIndex=t,m.push(a),a}return m[e]}if(!x)for(var T=0;T<_;T++)k(T);M.each(function(t,e){var n=(0,u.qB)(t).slice();if(1===n.length&&!(0,o.isString)(n[0])&&n[0]<0)return void M.set(e,!1);var r=M.set(e,[]);(0,o.each)(n,function(t,n){var i=(0,o.isString)(t)?w.get(t):t;null!=i&&i<_&&(r[n]=i,I(k(i),e,n))})});var C=0;function I(t,e,n){null!=r.Pe.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,v.set(e,!0))}(0,o.each)(g,function(t){if((0,o.isString)(t))i=t,r={};else{var e,n,r,i=(r=t).name,a=r.ordinalMeta;r.ordinalMeta=null,(r=(0,o.extend)({},r)).ordinalMeta=a,e=r.dimsDef,n=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=M.get(i);if(!1!==s){if(!(s=(0,u.qB)(s)).length)for(var l=0;l<(e&&e.length||1);l++){for(;C<_&&null!=k(C).coordDim;)C++;C<_&&s.push(C++)}(0,o.each)(s,function(t,a){var s=k(t);if(b&&null!=r.type&&(s.type=r.type),I((0,o.defaults)(s,r),i,a),null==s.name&&e){var u=e[a];(0,o.isObject)(u)||(u={name:u}),s.name=s.displayName=u.name,s.defaultTooltip=u.defaultTooltip}n&&(0,o.defaults)(s.otherDims,n)})}});var D=e.generateCoord,O=e.generateCoordCount,P=null!=O;O=D?O||1:0;var L=D||"value";function R(t){null==t.name&&(t.name=t.coordDim)}if(x)(0,o.each)(m,function(t){R(t)}),m.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var E=0;E<_;E++){var N=k(E);null==N.coordDim&&(N.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var r=0;e.hasKey(t+r);)r++;t+=r}return e.set(t,!0),t}(L,v,P),N.coordDimIndex=0,(!D||O<=0)&&(N.isExtraCoord=!0),O--),R(N),null==N.type&&((0,l.PU)(t,E)===l.sc.Must||N.isExtraCoord&&(null!=N.otherDims.itemName||null!=N.otherDims.seriesName))&&(N.type="ordinal")}return function(t){for(var e=(0,o.createHashMap)(),n=0;n<t.length;n++){var r=t[n],i=r.name,a=e.get(i)||0;a>0&&(r.name=i+(a-1)),a++,e.set(i,a)}}(m),new c.Tc({source:t,dimensions:m,fullDimensionCount:_,dimensionOmitted:x})}},1613:(t,e,n)=>{"use strict";n.d(e,{A:()=>v});var r=n(7170),i=n(2660),o=(0,n(3809).A)([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),a=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return o(this,t,e)},t}(),s=n(169),u=n(3875),l=["textStyle","color"],c=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],h=new u.Ay,f=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(l):null)},t.prototype.getFont=function(){return(0,s.c8)({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<c.length;n++)e[c[n]]=this.getShallow(c[n]);return h.useStyle(e),h.update(),h.getBoundingRect()},t}(),p=n(1764),d=n(1531),g=n(4123),y=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i]},t.prototype.mergeOption=function(t,e){(0,g.merge)(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,r=null==n?n:n[t];if(null==r&&!e){var i=this.parentModel;i&&(r=i.getShallow(t))}return r},t.prototype.getModel=function(e,n){var r=null!=e,i=r?this.parsePath(e):null;return new t(r?this._doGet(i):this.option,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(i)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new this.constructor((0,g.clone)(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!r.A.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;else if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var r=0;r<t.length&&(!t[r]||null!=(n=n&&"object"==typeof n?n[t[r]]:null));r++);return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();(0,i.gq)(y),(0,i.Od)(y),(0,g.mixin)(y,p.J),(0,g.mixin)(y,d.s),(0,g.mixin)(y,a),(0,g.mixin)(y,f);let v=y},1679:(t,e,n)=>{"use strict";n.r(e),n.d(e,{Axis:()=>ex,ChartView:()=>p.A,ComponentModel:()=>c.A,ComponentView:()=>h.A,List:()=>d.A,Model:()=>tb.A,PRIORITY:()=>l.FQ,SeriesModel:()=>f.A,color:()=>_,connect:()=>l.Ng,dataTool:()=>l.p5,dependencies:()=>l.El,disConnect:()=>l.zm,disconnect:()=>l.Zf,dispose:()=>l.AS,env:()=>el.A,extendChartView:()=>eA,extendComponentModel:()=>ew,extendComponentView:()=>eS,extendSeriesModel:()=>eM,format:()=>s,getCoordinateSystemDimensions:()=>l.Bo,getInstanceByDom:()=>l.FP,getInstanceById:()=>l.aQ,getMap:()=>l.ZB,graphic:()=>a,helper:()=>r,init:()=>l.Ts,innerDrawElementOnCanvas:()=>eb.Xi,matrix:()=>y,number:()=>i,parseGeoJSON:()=>tX,parseGeoJson:()=>tX,registerAction:()=>l.OH,registerCoordinateSystem:()=>l.pX,registerLayout:()=>l.Oh,registerLoading:()=>l.Ej,registerLocale:()=>l.E,registerMap:()=>l.mz,registerPostInit:()=>l.cf,registerPostUpdate:()=>l.tb,registerPreprocessor:()=>l.lP,registerProcessor:()=>l.qg,registerTheme:()=>l.bf,registerTransform:()=>l.iY,registerUpdateLifecycle:()=>l.xV,registerVisual:()=>l.AF,setCanvasCreator:()=>l.vV,setPlatformAPI:()=>tL.Gs,throttle:()=>x.nF,time:()=>o,use:()=>tP.Y,util:()=>u,vector:()=>v,version:()=>l.rE,zrUtil:()=>m,zrender:()=>g});var r={};n.r(r),n.d(r,{createDimensions:()=>b.M,createList:()=>tk,createScale:()=>tI,createSymbol:()=>tA.v5,createTextStyle:()=>tO,dataStack:()=>tC,enableHoverEmphasis:()=>tT.iJ,getECData:()=>tS.z,getLayoutRect:()=>tw.dV,mixinAxisModelCommonMethods:()=>tD});var i={};n.r(i),n.d(i,{MAX_SAFE_INTEGER:()=>H.Is,asc:()=>H.Y6,getPercentWithPrecision:()=>H.wp,getPixelPrecision:()=>H.hb,getPrecision:()=>H.XV,getPrecisionSafe:()=>H.y6,isNumeric:()=>H.kf,isRadianAroundZero:()=>H.dh,linearMap:()=>H.Cb,nice:()=>H.Cm,numericToNumber:()=>H.Sm,parseDate:()=>H._U,quantile:()=>H.YV,quantity:()=>H.au,quantityExponent:()=>H.NX,reformIntervals:()=>H.sL,remRadian:()=>H._7,round:()=>H.LI});var o={};n.r(o),n.d(o,{format:()=>Q.GP,parse:()=>H._U});var a={};n.r(a),n.d(a,{Arc:()=>t7.A,BezierCurve:()=>t8.A,BoundingRect:()=>tR.A,Circle:()=>tJ.A,CompoundPath:()=>en.A,Ellipse:()=>t0.A,Group:()=>t$.A,Image:()=>tK.Ay,IncrementalDisplayable:()=>ee,Line:()=>t6.A,LinearGradient:()=>ei,Polygon:()=>t5.A,Polyline:()=>t3.A,RadialGradient:()=>eo,Rect:()=>t4.A,Ring:()=>t2.A,Sector:()=>t1.A,Text:()=>tQ.Ay,clipPointsByRect:()=>tY.BZ,clipRectByRect:()=>tY.KN,createIcon:()=>tY.wt,extendPath:()=>tY.cg,extendShape:()=>tY.Qo,getShapeClass:()=>tY.gB,getTransform:()=>tY.RG,initProps:()=>tZ.LW,makeImage:()=>tY.Gs,makePath:()=>tY.cP,mergePath:()=>tY.uc,registerShape:()=>tY.dQ,resizePath:()=>tY.rq,updateProps:()=>tZ.oi});var s={};n.r(s),n.d(s,{addCommas:()=>Y.ob,capitalFirst:()=>Y.x5,encodeHTML:()=>ea.Me,formatTime:()=>Y.fU,formatTpl:()=>Y.YK,getTextRect:()=>eu,getTooltipMarker:()=>Y.qg,normalizeCssArray:()=>Y.QX,toCamelCase:()=>Y.Cb,truncateText:()=>es.EJ});var u={};n.r(u),n.d(u,{bind:()=>m.bind,clone:()=>m.clone,curry:()=>m.curry,defaults:()=>m.defaults,each:()=>m.each,extend:()=>m.extend,filter:()=>m.filter,indexOf:()=>m.indexOf,inherits:()=>m.inherits,isArray:()=>m.isArray,isFunction:()=>m.isFunction,isObject:()=>m.isObject,isString:()=>m.isString,map:()=>m.map,merge:()=>m.merge,reduce:()=>m.reduce});var l=n(202),c=n(706),h=n(6036),f=n(2403),p=n(4436),d=n(5327),g=n(7172),y=n(8346),v=n(1148),m=n(4123),_=n(411),x=n(1270),b=n(1598),w=n(3213),S=n(6187),M=n(7416),A=function(t){this.coordSysDims=[],this.axisMap=(0,m.createHashMap)(),this.categoryAxisMap=(0,m.createHashMap)(),this.coordSysName=t},T={cartesian2d:function(t,e,n,r){var i=t.getReferringComponents("xAxis",S.US).models[0],o=t.getReferringComponents("yAxis",S.US).models[0];e.coordSysDims=["x","y"],n.set("x",i),n.set("y",o),k(i)&&(r.set("x",i),e.firstCategoryDimIndex=0),k(o)&&(r.set("y",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,r){var i=t.getReferringComponents("singleAxis",S.US).models[0];e.coordSysDims=["single"],n.set("single",i),k(i)&&(r.set("single",i),e.firstCategoryDimIndex=0)},polar:function(t,e,n,r){var i=t.getReferringComponents("polar",S.US).models[0],o=i.findAxisModel("radiusAxis"),a=i.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),k(o)&&(r.set("radius",o),e.firstCategoryDimIndex=0),k(a)&&(r.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,r){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,r){var i=t.ecModel,o=i.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();(0,m.each)(o.parallelAxisIndex,function(t,o){var s=i.getComponent("parallelAxis",t),u=a[o];n.set(u,s),k(s)&&(r.set(u,s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=o))})}};function k(t){return"category"===t.get("type")}var C=n(8535),I=n(4673);function D(t,e,n){var r,i,o,a,s,u,l,c,h=(n=n||{}).byIndex,f=n.stackedCoordDimension;(r=e,(0,I.fg)(r.schema))?(i=(o=e.schema).dimensions,a=e.store):i=e;var p=!!(t&&t.get("stack"));if((0,m.each)(i,function(t,e){(0,m.isString)(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(h||s||!t.ordinalMeta||(s=t),u||"ordinal"===t.type||"time"===t.type||f&&f!==t.coordDim||(u=t))}),!u||h||s||(h=!0),u){l="__\0ecstackresult_"+t.id,c="__\0ecstackedover_"+t.id,s&&(s.createInvertedIndices=!0);var d=u.coordDim,g=u.type,y=0;(0,m.each)(i,function(t){t.coordDim===d&&y++});var v={name:l,coordDim:d,coordDimIndex:y,type:g,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},_={name:c,coordDim:c,coordDimIndex:y+1,type:g,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};o?(a&&(v.storeDimIndex=a.ensureCalculationDimension(c,g),_.storeDimIndex=a.ensureCalculationDimension(l,g)),o.appendCalculationDimension(v),o.appendCalculationDimension(_)):(i.push(v),i.push(_))}return{stackedDimension:u&&u.name,stackedByDimension:s&&s.name,isStackedByIndex:h,stackedOverDimension:c,stackResultDimension:l}}function O(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}var P=n(8142),L=n(7439);let R=function(t,e,n){n=n||{};var r,i,o,a,s,u,l,c,h=e.getSourceManager(),f=!1;t?(f=!0,c=(0,C.AF)(t)):f=(c=h.getSource()).sourceFormat===L.mK;var p=function(t){var e=t.get("coordinateSystem"),n=new A(e),r=T[e];if(r)return r(t,n,n.axisMap,n.categoryAxisMap),n}(e),g=(i=e.get("coordinateSystem"),o=M.A.get(i),p&&p.coordSysDims&&(r=m.map(p.coordSysDims,function(t){var e={name:t},n=p.axisMap.get(t);if(n){var r=n.get("type");e.type=(0,w.B)(r)}return e})),r||(r=o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"]),r),y=n.useEncodeDefaulter,v=m.isFunction(y)?y:y?m.curry(P.OC,g,e):null,_={coordDimensions:g,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:v,canOmitUnusedDimensions:!f},x=(0,b.A)(c,_),k=(a=x.dimensions,s=n.createInvertedIndices,p&&m.each(a,function(t,e){var n=t.coordDim,r=p.categoryAxisMap.get(n);r&&(null==u&&(u=e),t.ordinalMeta=r.getOrdinalMeta(),s&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(l=!0)}),l||null==u||(a[u].otherDims.itemName=0),u),I=f?null:h.getSharedDataStore(x),O=D(e,{schema:x,store:I}),R=new d.A(x,e);R.setCalculationInfo(O);var E=null!=k&&function(t){if(t.sourceFormat===L.mK){var e=function(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]);return!m.isArray((0,S.vj)(e))}}(c)?function(t,e,n,r){return r===k?n:this.defaultDimValueGetter(t,e,n,r)}:null;return R.hasItemOption=!1,R.initData(f?c:I,null,E),R};var E=n(643),N=n(2660),B=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();N.tQ(B);var F=0,z=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++F}return t.createByAxisModel=function(e){var n=e.option,r=n.data,i=r&&(0,m.map)(r,V);return new t({categories:i,needCollect:!i,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!(0,m.isString)(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var r=this._getOrCreateMap();return null==(e=r.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,r.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=(0,m.createHashMap)(this.categories))},t}();function V(t){return(0,m.isObject)(t)&&null!=t.value?t.value:t+""}var H=n(3607);function j(t){return(0,H.XV)(t)+2}function U(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function W(t,e){return t>=e[0]&&t<=e[1]}function G(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function q(t,e){return t*(e[1]-e[0])+e[0]}var X=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var r=n.getSetting("ordinalMeta");return r||(r=new z({})),(0,m.isArray)(r)&&(r=new z({categories:(0,m.map)(r,function(t){return(0,m.isObject)(t)?t.value:t})})),n._ordinalMeta=r,n._extent=n.getSetting("extent")||[0,r.categories.length-1],n}return(0,E.__extends)(e,t),e.prototype.parse=function(t){return null==t?NaN:(0,m.isString)(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return W(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},e.prototype.normalize=function(t){return G(t=this._getTickNumber(this.parse(t)),this._extent)},e.prototype.scale=function(t){return t=Math.round(q(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null==t){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],r=this._ticksByOrdinalNumber=[],i=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);i<a;++i){var s=e[i];n[i]=s,r[s]=i}for(var u=0;i<o;++i){for(;null!=r[u];)u++;n.push(u),r[u]=i}},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(B);B.registerClass(X);var Y=n(2261),Z=H.LI,$=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return(0,E.__extends)(e,t),e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return W(t,this._extent)},e.prototype.normalize=function(t){return G(t,this._extent)},e.prototype.scale=function(t){return q(t,this._extent)},e.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},e.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=j(t)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,r=this._niceExtent,i=this._intervalPrecision,o=[];if(!e)return o;n[0]<r[0]&&(t?o.push({value:Z(r[0]-e,i)}):o.push({value:n[0]}));for(var a=r[0];a<=r[1]&&(o.push({value:a}),(a=Z(a+e,i))!==o[o.length-1].value);)if(o.length>1e4)return[];var s=o.length?o[o.length-1].value:r[1];return n[1]>s&&(t?o.push({value:Z(s+e,i)}):o.push({value:n[1]})),o},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],r=this.getExtent(),i=1;i<e.length;i++){for(var o=e[i],a=e[i-1],s=0,u=[],l=(o.value-a.value)/t;s<t-1;){var c=Z(a.value+(s+1)*l);c>r[0]&&c<r[1]&&u.push(c),s++}n.push(u)}return n},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;null==n?n=H.XV(t.value)||0:"auto"===n&&(n=this._intervalPrecision);var r=Z(t.value,n,!0);return Y.ob(r)},e.prototype.calcNiceTicks=function(t,e,n){t=t||5;var r=this._extent,i=r[1]-r[0];if(isFinite(i)){i<0&&(i=-i,r.reverse());var o,a,s,u,l,c,h,f=(s=t,u={},l=r[1]-r[0],c=u.interval=(0,H.Cm)(l/s,!0),null!=e&&c<e&&(c=u.interval=e),null!=n&&c>n&&(c=u.interval=n),h=u.intervalPrecision=j(c),o=u.niceTickExtent=[(0,H.LI)(Math.ceil(r[0]/c)*c,h),(0,H.LI)(Math.floor(r[1]/c)*c,h)],a=r,isFinite(o[0])||(o[0]=a[0]),isFinite(o[1])||(o[1]=a[1]),U(o,0,a),U(o,1,a),o[0]>o[1]&&(o[0]=o[1]),u);this._intervalPrecision=f.intervalPrecision,this._interval=f.interval,this._niceExtent=f.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=Math.abs(e[0]);t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;isFinite(e[1]-e[0])||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Z(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Z(Math.ceil(e[1]/r)*r))},e.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},e.type="interval",e}(B);B.registerClass($);function K(t){return t.dim+t.index}var Q=n(9799),J=function(t,e,n,r){for(;n<r;){var i=n+r>>>1;t[i][1]<e?n=i+1:r=i}return n},tt=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return(0,E.__extends)(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return(0,Q.GP)(t.value,Q.Lm[(0,Q.$9)((0,Q.ym)(this._minLevelUnit))]||Q.Lm.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var r=this.getSetting("useUTC"),i=this.getSetting("locale");return(0,Q.X_)(t,e,n,i,r)},e.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];if(!t)return n;n.push({value:e[0],level:0});var r=this.getSetting("useUTC"),i=function(t,e,n,r){for(var i=Q.F7,o=0,a=[],s=[],u=0,l=0,c=0;c<i.length&&o++<1e4;++c){var h=(0,Q.ym)(i[c]);if((0,Q.ce)(i[c])&&(!function(t,i,o){var a=[],s=!i.length;if(!function(t,e,n,r){var i=H._U(e),o=H._U(n),a=function(t){return(0,Q.g0)(i,t,r)===(0,Q.g0)(o,t,r)},s=function(){return a("year")},u=function(){return s()&&a("month")},l=function(){return u()&&a("day")},c=function(){return l()&&a("hour")},h=function(){return c()&&a("minute")},f=function(){return h()&&a("second")};switch(t){case"year":return s();case"month":return u();case"day":return l();case"hour":return c();case"minute":return h();case"second":return f();case"millisecond":return f()&&a("millisecond")}}((0,Q.ym)(t),r[0],r[1],n)){s&&(i=[{value:function(t,e,n){var r=new Date(t);switch((0,Q.ym)(e)){case"year":case"month":r[(0,Q.xu)(n)](0);case"day":r[(0,Q.ti)(n)](1);case"hour":r[(0,Q.Yd)(n)](0);case"minute":r[(0,Q.KF)(n)](0);case"second":r[(0,Q.ww)(n)](0),r[(0,Q.FP)(n)](0)}return r.getTime()}(new Date(r[0]),t,n)},{value:r[1]}]);for(var u=0;u<i.length-1;u++){var l=i[u].value,c=i[u+1].value;if(l!==c){var h,f,p,d=void 0,g=void 0,y=void 0;switch(t){case"year":d=Math.max(1,Math.round(e/Q.CZ/365)),g=(0,Q.hY)(n),y=(0,Q.tM)(n);break;case"half-year":case"quarter":case"month":d=(h=e/(30*Q.CZ))>6?6:h>3?3:h>2?2:1,g=(0,Q.jJ)(n),y=(0,Q.xu)(n);break;case"week":case"half-week":case"day":d=(f=e/Q.CZ)>16?16:f>7.5?7:f>3.5?4:f>1.5?2:1,g=(0,Q.bP)(n),y=(0,Q.ti)(n);break;case"half-day":case"quarter-day":case"hour":d=(p=e/Q.MA)>12?12:p>6?6:p>3.5?4:p>2?2:1,g=(0,Q.iC)(n),y=(0,Q.Yd)(n);break;case"minute":d=tn(e,!0),g=(0,Q.yB)(n),y=(0,Q.KF)(n);break;case"second":d=tn(e,!1),g=(0,Q.Wf)(n),y=(0,Q.ww)(n);break;case"millisecond":d=H.Cm(e,!0),g=(0,Q.Zz)(n),y=(0,Q.FP)(n)}!function(t,e,n,i,o,a,s){for(var u=new Date(e),l=e,c=u[i]();l<n&&l<=r[1];)s.push({value:l}),c+=t,u[o](c),l=u.getTime();s.push({value:l,notAdd:!0})}(d,l,c,g,y,0,a),"year"===t&&o.length>1&&0===u&&o.unshift({value:o[0].value-d})}}for(var u=0;u<a.length;u++)o.push(a[u])}}(i[c],a[a.length-1]||[],s),h!==(i[c+1]?(0,Q.ym)(i[c+1]):null))){if(s.length){l=u,s.sort(function(t,e){return t.value-e.value});for(var f=[],p=0;p<s.length;++p){var d=s[p].value;(0===p||s[p-1].value!==d)&&(f.push(s[p]),d>=r[0]&&d<=r[1]&&u++)}var g=(r[1]-r[0])/e;if(u>1.5*g&&l>g/1.5||(a.push(f),u>g||t===i[c]))break}s=[]}}for(var y=(0,m.filter)((0,m.map)(a,function(t){return(0,m.filter)(t,function(t){return t.value>=r[0]&&t.value<=r[1]&&!t.notAdd})}),function(t){return t.length>0}),v=[],_=y.length-1,c=0;c<y.length;++c)for(var x=y[c],b=0;b<x.length;++b)v.push({value:x[b].value,level:_-c});v.sort(function(t,e){return t.value-e.value});for(var w=[],c=0;c<v.length;++c)(0===c||v[c].value!==v[c-1].value)&&w.push(v[c]);return w}(this._minLevelUnit,this._approxInterval,r,e);return(n=n.concat(i)).push({value:e[1],level:0}),n},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=Q.CZ,e[1]+=Q.CZ),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-Q.CZ}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,e,n){t=t||10;var r=this._extent,i=r[1]-r[0];this._approxInterval=i/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=te.length,a=Math.min(J(te,this._approxInterval,0,o),o-1);this._interval=te[a][1],this._minLevelUnit=te[Math.max(a-1,0)][0]},e.prototype.parse=function(t){return(0,m.isNumber)(t)?t:+H._U(t)},e.prototype.contain=function(t){return W(this.parse(t),this._extent)},e.prototype.normalize=function(t){return G(this.parse(t),this._extent)},e.prototype.scale=function(t){return q(t,this._extent)},e.type="time",e}($),te=[["second",Q.OY],["minute",Q.iW],["hour",Q.MA],["quarter-day",6*Q.MA],["half-day",12*Q.MA],["day",1.2*Q.CZ],["half-week",3.5*Q.CZ],["week",7*Q.CZ],["month",31*Q.CZ],["quarter",95*Q.CZ],["half-year",Q.$H/2],["year",Q.$H]];function tn(t,e){return(t/=e?Q.iW:Q.OY)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}B.registerClass(tt);var tr=B.prototype,ti=$.prototype,to=H.LI,ta=Math.floor,ts=Math.ceil,tu=Math.pow,tl=Math.log,tc=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new $,e._interval=0,e}return(0,E.__extends)(e,t),e.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,r=e.getExtent(),i=ti.getTicks.call(this,t);return m.map(i,function(t){var e=t.value,i=H.LI(tu(this.base,e));return i=e===n[0]&&this._fixMin?tf(i,r[0]):i,{value:i=e===n[1]&&this._fixMax?tf(i,r[1]):i}},this)},e.prototype.setExtent=function(t,e){var n=tl(this.base);t=tl(Math.max(0,t))/n,e=tl(Math.max(0,e))/n,ti.setExtent.call(this,t,e)},e.prototype.getExtent=function(){var t=this.base,e=tr.getExtent.call(this);e[0]=tu(t,e[0]),e[1]=tu(t,e[1]);var n=this._originalScale.getExtent();return this._fixMin&&(e[0]=tf(e[0],n[0])),this._fixMax&&(e[1]=tf(e[1],n[1])),e},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=tl(t[0])/tl(e),t[1]=tl(t[1])/tl(e),tr.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(n!==1/0&&!(n<=0)){var r=H.au(n);for(t/n*r<=.5&&(r*=10);!isNaN(r)&&1>Math.abs(r)&&Math.abs(r)>0;)r*=10;var i=[H.LI(ts(e[0]/r)*r),H.LI(ta(e[1]/r)*r)];this._interval=r,this._niceExtent=i}},e.prototype.calcNiceExtent=function(t){ti.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return W(t=tl(t)/tl(this.base),this._extent)},e.prototype.normalize=function(t){return G(t=tl(t)/tl(this.base),this._extent)},e.prototype.scale=function(t){return t=q(t,this._extent),tu(this.base,t)},e.type="log",e}(B),th=tc.prototype;function tf(t,e){return to(t,H.XV(e))}th.getMinorTicks=ti.getMinorTicks,th.getLabel=ti.getLabel,B.registerClass(tc);var tp=n(6847),td=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var r=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var i=e.get("min",!0);null==i&&(i=e.get("startValue",!0));var o=this._modelMinRaw=i;(0,m.isFunction)(o)?this._modelMinNum=tv(t,o({min:n[0],max:n[1]})):"dataMin"!==o&&(this._modelMinNum=tv(t,o));var a=this._modelMaxRaw=e.get("max",!0);if((0,m.isFunction)(a)?this._modelMaxNum=tv(t,a({min:n[0],max:n[1]})):"dataMax"!==a&&(this._modelMaxNum=tv(t,a)),r)this._axisDataLen=e.getCategories().length;else{var s=e.get("boundaryGap"),u=(0,m.isArray)(s)?s:[s||0,s||0];"boolean"==typeof u[0]||"boolean"==typeof u[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[(0,tp.lo)(u[0],1),(0,tp.lo)(u[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,r=this._axisDataLen,i=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,u=null!=a,l=null!=s;null==a&&(a=t?r?0:NaN:e-i[0]*o),null==s&&(s=t?r?r-1:NaN:n+i[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN);var c=(0,m.eqNaN)(a)||(0,m.eqNaN)(s)||t&&!r;this._needCrossZero&&(a>0&&s>0&&!u&&(a=0),a<0&&s<0&&!l&&(s=0));var h=this._determinedMin,f=this._determinedMax;return null!=h&&(a=h,u=!0),null!=f&&(s=f,l=!0),{min:a,max:s,minFixed:u,maxFixed:l,isBlank:c}},t.prototype.modifyDataMinMax=function(t,e){this[ty[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){this[tg[t]]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),tg={min:"_determinedMin",max:"_determinedMax"},ty={min:"_dataMin",max:"_dataMax"};function tv(t,e){return null==e?null:(0,m.eqNaN)(e)?NaN:t.parse(e)}function tm(t){var e=t.getLabelModel().get("formatter"),n="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?function(n,r){return t.scale.getFormattedLabel(n,r,e)}:m.isString(e)?function(n){var r=t.scale.getLabel(n);return e.replace("{value}",null!=r?r:"")}:m.isFunction(e)?function(r,i){var o,a;return null!=n&&(i=r.value-n),e((o=t,a=r,"category"===o.type?o.scale.getLabel(a):a.value),i,null!=r.level?{level:r.level}:null)}:function(e){return t.scale.getLabel(e)}}function t_(t){var e=t.get("interval");return null==e?"auto":e}var tx=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}(),tb=n(1613),tw=n(4524),tS=n(2663),tM=n(169),tA=n(4670),tT=n(2392);function tk(t){return R(null,t)}var tC={isDimensionStacked:O,enableDataStack:D,getStackedDimension:function(t,e){return O(t,e)?t.getCalculationInfo("stackResultDimension"):e}};function tI(t,e){var n,r,i,o,a,s,u,l=e;e instanceof tb.A||(l=new tb.A(e));var c=function(t,e){if(e=e||t.get("type"))switch(e){case"category":return new X({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new tt({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(B.getClass(e)||$)}}(l);return c.setExtent(t[0],t[1]),i=(r=function(t,e){var n=t.type,r=(f=t.getExtent(),(p=t.rawExtentInfo)||(p=new td(t,e,f),t.rawExtentInfo=p),p).calculate();t.setBlank(r.isBlank);var i=r.min,o=r.max,a=e.ecModel;if(a&&"time"===n){var s=(d=[],a.eachSeriesByType("bar",function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&d.push(t)}),d),u=!1;if(m.each(s,function(t){u=u||t.getBaseAxis()===e.axis}),u){var l,c,h,f,p,d,g,y,v=function(t,e,n,r){var i=n.axis.getExtent(),o=Math.abs(i[1]-i[0]),a=function(t,e,n){if(t&&e)return t[K(e)]}(r,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;m.each(a,function(t){s=Math.min(t.offset,s)});var u=-1/0;m.each(a,function(t){u=Math.max(t.offset+t.width,u)});var l=(s=Math.abs(s))+(u=Math.abs(u)),c=e-t,h=c/(1-(s+u)/o)-c;return e+=u/l*h,{min:t-=s/l*h,max:e}}(i,o,e,(g=function(t){var e={};(0,m.each)(t,function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var r=t.getData(),i=n.dim+"_"+n.index,o=r.getDimensionIndex(r.mapDimension(n.dim)),a=r.getStore(),s=0,u=a.count();s<u;++s){var l=a.get(o,s);e[i]?e[i].push(l):e[i]=[l]}});var n={};for(var r in e)if(e.hasOwnProperty(r)){var i=e[r];if(i){i.sort(function(t,e){return t-e});for(var o=null,a=1;a<i.length;++a){var s=i[a]-i[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[r]=o}}return n}(s),y=[],(0,m.each)(s,function(t){var e,n,r,i=t.coordinateSystem.getBaseAxis(),o=i.getExtent();if("category"===i.type)r=i.getBandWidth();else if("value"===i.type||"time"===i.type){var a=g[i.dim+"_"+i.index],s=Math.abs(o[1]-o[0]),u=i.scale.getExtent(),l=Math.abs(u[1]-u[0]);r=a?s/l*a:s}else{var c=t.getData();r=Math.abs(o[1]-o[0])/c.count()}var h=(0,H.lo)(t.get("barWidth"),r),f=(0,H.lo)(t.get("barMaxWidth"),r),p=(0,H.lo)(t.get("barMinWidth")||((e=t).pipelineContext&&e.pipelineContext.large?.5:1),r),d=t.get("barGap"),v=t.get("barCategoryGap");y.push({bandWidth:r,barWidth:h,barMaxWidth:f,barMinWidth:p,barGap:d,barCategoryGap:v,axisKey:K(i),stackId:(n=t).get("stack")||"__ec_stack_"+n.seriesIndex})}),l=y,c={},(0,m.each)(l,function(t,e){var n=t.axisKey,r=t.bandWidth,i=c[n]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},o=i.stacks;c[n]=i;var a=t.stackId;!o[a]&&i.autoWidthCount++,o[a]=o[a]||{width:0,maxWidth:0};var s=t.barWidth;s&&!o[a].width&&(o[a].width=s,s=Math.min(i.remainedWidth,s),i.remainedWidth-=s);var u=t.barMaxWidth;u&&(o[a].maxWidth=u);var l=t.barMinWidth;l&&(o[a].minWidth=l);var h=t.barGap;null!=h&&(i.gap=h);var f=t.barCategoryGap;null!=f&&(i.categoryGap=f)}),h={},(0,m.each)(c,function(t,e){h[e]={};var n,r=t.stacks,i=t.bandWidth,o=t.categoryGap;null==o&&(o=Math.max(35-4*(0,m.keys)(r).length,15)+"%");var a=(0,H.lo)(o,i),s=(0,H.lo)(t.gap,1),u=t.remainedWidth,l=t.autoWidthCount,c=(u-a)/(l+(l-1)*s);c=Math.max(c,0),(0,m.each)(r,function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){var r=t.width;e&&(r=Math.min(r,e)),n&&(r=Math.max(r,n)),t.width=r,u-=r+s*r,l--}else{var r=c;e&&e<r&&(r=Math.min(e,u)),n&&n>r&&(r=n),r!==c&&(t.width=r,u-=r+s*r,l--)}}),c=Math.max(c=(u-a)/(l+(l-1)*s),0);var f=0;(0,m.each)(r,function(t,e){t.width||(t.width=c),n=t,f+=t.width*(1+s)}),n&&(f-=n.width*s);var p=-f/2;(0,m.each)(r,function(t,n){h[e][n]=h[e][n]||{bandWidth:i,offset:p,width:t.width},p+=t.width*(1+s)})}),h));i=v.min,o=v.max}}return{extent:[i,o],fixMin:r.minFixed,fixMax:r.maxFixed}}(c,n=l)).extent,o=n.get("splitNumber"),c instanceof tc&&(c.base=n.get("logBase")),a=c.type,s=n.get("interval"),u="interval"===a||"time"===a,c.setExtent(i[0],i[1]),c.calcNiceExtent({splitNumber:o,fixMin:r.fixMin,fixMax:r.fixMax,minInterval:u?n.get("minInterval"):null,maxInterval:u?n.get("maxInterval"):null}),null!=s&&c.setInterval&&c.setInterval(s),c}function tD(t){m.mixin(t,tx)}function tO(t,e){return e=e||{},(0,tM.VB)(t,null,null,"normal"!==e.state)}var tP=n(9566),tL=n(2316),tR=n(7669),tE=n(179);function tN(t,e){return 1e-8>Math.abs(t-e)}function tB(t,e,n){var r=0,i=t[0];if(!i)return!1;for(var o=1;o<t.length;o++){var a=t[o];r+=(0,tE.A)(i[0],i[1],a[0],a[1],e,n),i=a}var s=t[0];return tN(i[0],s[0])&&tN(i[1],s[1])||(r+=(0,tE.A)(i[0],i[1],s[0],s[1],e,n)),0!==r}var tF=[];function tz(t,e){for(var n=0;n<t.length;n++)v.applyTransform(t[n],t[n],e)}function tV(t,e,n,r){for(var i=0;i<t.length;i++){var o=t[i];r&&(o=r.project(o)),o&&isFinite(o[0])&&isFinite(o[1])&&(v.min(e,e,o),v.max(n,n,o))}}var tH=function(){function t(t){this.name=t}return t.prototype.setCenter=function(t){this._center=t},t.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this.calcCenter()),t},t}(),tj=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},tU=function(t){this.type="linestring",this.points=t},tW=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.type="geoJSON",i.geometries=n,i._center=r&&[r[0],r[1]],i}return(0,E.__extends)(e,t),e.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,r=0;r<e.length;r++){var i=e[r],o=i.exterior,a=o&&o.length;a>n&&(t=i,n=a)}if(t)return function(t){for(var e=0,n=0,r=0,i=t.length,o=t[i-1][0],a=t[i-1][1],s=0;s<i;s++){var u=t[s][0],l=t[s][1],c=o*l-u*a;e+=c,n+=(o+u)*c,r+=(a+l)*c,o=u,a=l}return e?[n/e/3,r/e/3,e]:[t[0][0]||0,t[0][1]||0]}(t.exterior);var s=this.getBoundingRect();return[s.x+s.width/2,s.y+s.height/2]},e.prototype.getBoundingRect=function(t){var e=this._rect;if(e&&!t)return e;var n=[1/0,1/0],r=[-1/0,-1/0],i=this.geometries;return(0,m.each)(i,function(e){"polygon"===e.type?tV(e.exterior,n,r,t):(0,m.each)(e.points,function(e){tV(e,n,r,t)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(r[0])&&isFinite(r[1])||(n[0]=n[1]=r[0]=r[1]=0),e=new tR.A(n[0],n[1],r[0]-n[0],r[1]-n[1]),t||(this._rect=e),e},e.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var r=0,i=n.length;r<i;r++){var o=n[r];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(tB(a,t[0],t[1])){for(var u=0;u<(s?s.length:0);u++)if(tB(s[u],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,e,n,r){var i=this.getBoundingRect(),o=i.width/i.height;n?r||(r=n/o):n=o*r;for(var a=new tR.A(t,e,n,r),s=i.calculateTransform(a),u=this.geometries,l=0;l<u.length;l++){var c=u[l];"polygon"===c.type?(tz(c.exterior,s),(0,m.each)(c.interiors,function(t){tz(t,s)})):(0,m.each)(c.points,function(t){tz(t,s)})}(i=this._rect).copy(a),this._center=[i.x+i.width/2,i.y+i.height/2]},e.prototype.cloneShallow=function(t){null==t&&(t=this.name);var n=new e(t,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},e}(tH);function tG(t,e,n){for(var r=0;r<t.length;r++)t[r]=tq(t[r],e[r],n)}function tq(t,e,n){for(var r=[],i=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,u=t.charCodeAt(a+1)-64;u=u>>1^-(1&u),s=(s>>1^-(1&s))+i,u+=o,i=s,o=u,r.push([s/n,u/n])}return r}function tX(t,e){return t=function(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);var n=t.features;return m.each(n,function(t){var n=t.geometry,r=n.encodeOffsets,i=n.coordinates;if(r)switch(n.type){case"LineString":n.coordinates=tq(i,r,e);break;case"Polygon":case"MultiLineString":tG(i,r,e);break;case"MultiPolygon":m.each(i,function(t,n){return tG(t,r[n],e)})}}),t.UTF8Encoding=!1,t}(t),m.map(m.filter(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var n=t.properties,r=t.geometry,i=[];switch(r.type){case"Polygon":var o=r.coordinates;i.push(new tj(o[0],o.slice(1)));break;case"MultiPolygon":m.each(r.coordinates,function(t){t[0]&&i.push(new tj(t[0],t.slice(1)))});break;case"LineString":i.push(new tU([r.coordinates]));break;case"MultiLineString":i.push(new tU(r.coordinates))}var a=new tW(n[e||"name"],i,n.cp);return a.properties=n,a})}!function(t){function e(e,n){var r=t.call(this,e)||this;return r.type="geoSVG",r._elOnlyForCalculate=n,r}(0,E.__extends)(e,t),e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),n=[e.x+e.width/2,e.y+e.height/2],r=y.identity(tF),i=t;i&&!i.isGeoSVGGraphicRoot;)y.mul(r,i.getLocalTransform(),r),i=i.parent;return y.invert(r,r),v.applyTransform(n,n,r),n}}(tH);var tY=n(514),tZ=n(8991),t$=n(6631),tK=n(3493),tQ=n(3875),tJ=n(6586),t0=n(5960),t1=n(7143),t2=n(1132),t5=n(8544),t3=n(9562),t4=n(7821),t6=n(3948),t8=n(6864),t7=n(9950),t9=n(5921),et=[];let ee=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return(0,E.__extends)(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new tR.A(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],r=n.getBoundingRect().clone();n.needLocalTransform()&&r.applyTransform(n.getLocalTransform(et)),t.union(r)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1])){for(var r=0;r<this._displayables.length;r++)if(this._displayables[r].contain(t,e))return!0}return!1},e}(t9.Ay);var en=n(1294),er=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();let ei=function(t){function e(e,n,r,i,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==r?1:r,s.y2=null==i?0:i,s.type="linear",s.global=a||!1,s}return(0,E.__extends)(e,t),e}(er),eo=function(t){function e(e,n,r,i,o){var a=t.call(this,i)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==r?.5:r,a.type="radial",a.global=o||!1,a}return(0,E.__extends)(e,t),e}(er);var ea=n(4958),es=n(1823);function eu(t,e,n,r,i,o,a,s){return new tQ.Ay({style:{text:t,font:e,align:n,verticalAlign:r,padding:i,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()}var el=n(7170),ec=(0,S.$r)();function eh(t,e){var n=m.map(e,function(e){return t.scale.parse(e)});return"time"===t.type&&n.length>0&&(n.sort(),n.unshift(n[0]),n.push(n[n.length-1])),n}function ef(t,e){var n,r,i,o,a=ep(t,"labels"),s=t_(e),u=ed(a,s);return u||(m.isFunction(s)?i=ev(t,s):(o="auto"===s?null!=(r=ec(n=t).autoInterval)?r:ec(n).autoInterval=n.calculateCategoryInterval():s,i=ey(t,o)),eg(a,s,{labels:i,labelCategoryInterval:o}))}function ep(t,e){return ec(t)[e]||(ec(t)[e]=[])}function ed(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function eg(t,e,n){return t.push({key:e,value:n}),n}function ey(t,e,n){var r=tm(t),i=t.scale,o=i.getExtent(),a=t.getLabelModel(),s=[],u=Math.max((e||0)+1,1),l=o[0],c=i.count();0!==l&&u>1&&c/u>2&&(l=Math.round(Math.ceil(l/u)*u));var h="category"===t.type&&0===t_(t.getLabelModel()),f=a.get("showMinLabel")||h,p=a.get("showMaxLabel")||h;f&&l!==o[0]&&g(o[0]);for(var d=l;d<=o[1];d+=u)g(d);function g(t){var e={value:t};s.push(n?t:{formattedLabel:r(e),rawLabel:i.getLabel(e),tickValue:t})}return p&&d-u!==o[1]&&g(o[1]),s}function ev(t,e,n){var r=t.scale,i=tm(t),o=[];return m.each(r.getTicks(),function(t){var a=r.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:i(t),rawLabel:a,tickValue:s})}),o}var em=[0,1];function e_(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}let ex=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),r=Math.max(e[0],e[1]);return t>=n&&t<=r},t.prototype.containData=function(t){return this.scale.contain(t)},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return(0,H.hb)(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,r=this.scale;return t=r.normalize(t),this.onBand&&"ordinal"===r.type&&e_(n=n.slice(),r.count()),(0,H.Cb)(t,em,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,r=this.scale;this.onBand&&"ordinal"===r.type&&e_(n=n.slice(),r.count());var i=(0,H.Cb)(t,n,em,e);return this.scale.scale(i)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=function(t,e){var n=t.getTickModel().get("customValues");if(n){var r=t.scale.getExtent(),i=eh(t,n);return{ticks:m.filter(i,function(t){return t>=r[0]&&t<=r[1]})}}return"category"===t.type?function(t,e){var n,r,i=ep(t,"ticks"),o=t_(e),a=ed(i,o);if(a)return a;if((!e.get("show")||t.scale.isBlank())&&(n=[]),m.isFunction(o))n=ev(t,o,!0);else if("auto"===o){var s=ef(t,t.getLabelModel());r=s.labelCategoryInterval,n=m.map(s.labels,function(t){return t.tickValue})}else n=ey(t,r=o,!0);return eg(i,o,{ticks:n,tickCategoryInterval:r})}(t,e):{ticks:m.map(t.scale.getTicks(),function(t){return t.value})}}(this,e).ticks,r=(0,m.map)(n,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this);return function(t,e,n,r){var i,o,a=e.length;if(t.onBand&&!n&&a){var s=t.getExtent();if(1===a)e[0].coord=s[0],i=e[1]={coord:s[1],tickValue:e[0].tickValue};else{var u=e[a-1].tickValue-e[0].tickValue,l=(e[a-1].coord-e[0].coord)/u;(0,m.each)(e,function(t){t.coord-=l/2});var c=t.scale.getExtent();o=1+c[1]-e[a-1].tickValue,i={coord:e[a-1].coord+l*o,tickValue:c[1]+1},e.push(i)}var h=s[0]>s[1];f(e[0].coord,s[0])&&(r?e[0].coord=s[0]:e.shift()),r&&f(s[0],e[0].coord)&&e.unshift({coord:s[0]}),f(s[1],i.coord)&&(r?i.coord=s[1]:e.pop()),r&&f(i.coord,s[1])&&e.push({coord:s[1]})}function f(t,e){return t=(0,H.LI)(t),e=(0,H.LI)(e),h?t>e:t<e}}(this,r,e.get("alignWithLabel"),t.clamp),r},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");t>0&&t<100||(t=5);var e=this.scale.getMinorTicks(t);return(0,m.map)(e,function(t){return(0,m.map)(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this)},t.prototype.getViewLabels=function(){return function(t){var e,n,r,i,o,a,s=t.getLabelModel().get("customValues");if(s){var u=tm(t),l=t.scale.getExtent(),c=eh(t,s),h=m.filter(c,function(t){return t>=l[0]&&t<=l[1]});return{labels:m.map(h,function(e){var n={value:e};return{formattedLabel:u(n),rawLabel:t.scale.getLabel(n),tickValue:e}})}}return"category"===t.type?(n=(e=t).getLabelModel(),r=ef(e,n),!n.get("show")||e.scale.isBlank()?{labels:[],labelCategoryInterval:r.labelCategoryInterval}:r):(o=(i=t).scale.getTicks(),a=tm(i),{labels:m.map(o,function(t,e){return{level:t.level,formattedLabel:a(t,e),rawLabel:i.scale.getLabel(t),tickValue:t.value}})})}(this).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+ +!!this.onBand;return 0===n&&(n=1),Math.abs(Math.abs(t[1]-t[0]))/n},t.prototype.calculateCategoryInterval=function(){return function(t){var e,n,r=(n=(e=t).getLabelModel(),{axisRotate:e.getRotate?e.getRotate():e.isHorizontal&&!e.isHorizontal()?90:0,labelRotate:n.get("rotate")||0,font:n.getFont()}),i=tm(t),o=(r.axisRotate-r.labelRotate)/180*Math.PI,a=t.scale,s=a.getExtent(),u=a.count();if(s[1]-s[0]<1)return 0;var l=1;u>40&&(l=Math.max(1,Math.floor(u/40)));for(var c=s[0],h=t.dataToCoord(c+1)-t.dataToCoord(c),f=Math.abs(h*Math.cos(o)),p=Math.abs(h*Math.sin(o)),d=0,g=0;c<=s[1];c+=l){var y=0,v=0,m=tp.NO(i({value:c}),r.font,"center","top");y=1.3*m.width,v=1.3*m.height,d=Math.max(d,y,7),g=Math.max(g,v,7)}var _=d/f,x=g/p;isNaN(_)&&(_=1/0),isNaN(x)&&(x=1/0);var b=Math.max(0,Math.floor(Math.min(_,x))),w=ec(t.model),S=t.getExtent(),M=w.lastAutoInterval,A=w.lastTickCount;return null!=M&&null!=A&&1>=Math.abs(M-b)&&1>=Math.abs(A-u)&&M>b&&w.axisExtent0===S[0]&&w.axisExtent1===S[1]?b=M:(w.lastTickCount=u,w.lastAutoInterval=b,w.axisExtent0=S[0],w.axisExtent1=S[1]),b}(this)},t}();var eb=n(1834);function ew(t){var e=c.A.extend(t);return c.A.registerClass(e),e}function eS(t){var e=h.A.extend(t);return h.A.registerClass(e),e}function eM(t){var e=f.A.extend(t);return f.A.registerClass(e),e}function eA(t){var e=p.A.extend(t);return p.A.registerClass(e),e}var eT=n(4621),ek=n(2227),eC=n(4271),eI=n(8248),eD=n(6748),eO=n(882),eP=2*Math.PI,eL=eI.A.CMD,eR=["top","right","bottom","left"];function eE(t,e,n,r,i,o,a,s){var u=n-t,l=r-e,c=Math.sqrt(u*u+l*l),h=((i-t)*(u/=c)+(o-e)*(l/=c))/c;s&&(h=Math.min(Math.max(h,0),1)),h*=c;var f=a[0]=t+h*u,p=a[1]=e+h*l;return Math.sqrt((f-i)*(f-i)+(p-o)*(p-o))}function eN(t,e,n,r,i,o,a){n<0&&(t+=n,n=-n),r<0&&(e+=r,r=-r);var s=t+n,u=e+r,l=a[0]=Math.min(Math.max(i,t),s),c=a[1]=Math.min(Math.max(o,e),u);return Math.sqrt((l-i)*(l-i)+(c-o)*(c-o))}var eB=[],eF=new ek.A,ez=new ek.A,eV=new ek.A,eH=new ek.A,ej=new ek.A;function eU(t,e){if(t){var n=t.getTextGuideLine(),r=t.getTextContent();if(r&&n){var i=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=i.candidates||eR,s=r.getBoundingRect().clone();s.applyTransform(r.getComputedTransform());var u=1/0,l=i.anchor,c=t.getComputedTransform(),h=c&&(0,y.invert)([],c),f=e.get("length2")||0;l&&eV.copy(l);for(var p=0;p<a.length;p++){!function(t,e,n,r,i){var o=n.width,a=n.height;switch(t){case"top":r.set(n.x+o/2,n.y-0),i.set(0,-1);break;case"bottom":r.set(n.x+o/2,n.y+a+0),i.set(0,1);break;case"left":r.set(n.x-e,n.y+a/2),i.set(-1,0);break;case"right":r.set(n.x+o+e,n.y+a/2),i.set(1,0)}}(a[p],0,s,eF,eH),ek.A.scaleAndAdd(ez,eF,eH,f),ez.transform(h);var d=t.getBoundingRect(),g=l?l.distance(ez):t instanceof eC.Ay?function(t,e,n){for(var r,i,o=0,a=0,s=0,u=0,l=1/0,c=e.data,h=t.x,f=t.y,p=0;p<c.length;){var d=c[p++];1===p&&(o=c[p],a=c[p+1],s=o,u=a);var g=l;switch(d){case eL.M:s=c[p++],u=c[p++],o=s,a=u;break;case eL.L:g=eE(o,a,c[p],c[p+1],h,f,eB,!0),o=c[p++],a=c[p++];break;case eL.C:g=(0,eO.Et)(o,a,c[p++],c[p++],c[p++],c[p++],c[p],c[p+1],h,f,eB),o=c[p++],a=c[p++];break;case eL.Q:g=(0,eO.kh)(o,a,c[p++],c[p++],c[p],c[p+1],h,f,eB),o=c[p++],a=c[p++];break;case eL.A:var y=c[p++],v=c[p++],m=c[p++],_=c[p++],x=c[p++],b=c[p++];p+=1;var w=!!(1-c[p++]);r=Math.cos(x)*m+y,i=Math.sin(x)*_+v,p<=1&&(s=r,u=i);var S=(h-y)*_/m+y;g=function(t,e,n,r,i,o,a,s,u){var l=Math.sqrt((a-=t)*a+(s-=e)*s),c=(a/=l)*n+t,h=(s/=l)*n+e;if(Math.abs(r-i)%eP<1e-4)return u[0]=c,u[1]=h,l-n;if(o){var f=r;r=(0,eD.n)(i),i=(0,eD.n)(f)}else r=(0,eD.n)(r),i=(0,eD.n)(i);r>i&&(i+=eP);var p=Math.atan2(s,a);if(p<0&&(p+=eP),p>=r&&p<=i||p+eP>=r&&p+eP<=i)return u[0]=c,u[1]=h,l-n;var d=n*Math.cos(r)+t,g=n*Math.sin(r)+e,y=n*Math.cos(i)+t,v=n*Math.sin(i)+e,m=(d-a)*(d-a)+(g-s)*(g-s),_=(y-a)*(y-a)+(v-s)*(v-s);return m<_?(u[0]=d,u[1]=g,Math.sqrt(m)):(u[0]=y,u[1]=v,Math.sqrt(_))}(y,v,_,x,x+b,w,S,f,eB),o=Math.cos(x+b)*m+y,a=Math.sin(x+b)*_+v;break;case eL.R:g=eN(s=o=c[p++],u=a=c[p++],c[p++],c[p++],h,f,eB);break;case eL.Z:g=eE(o,a,s,u,h,f,eB,!0),o=s,a=u}g<l&&(l=g,n.set(eB[0],eB[1]))}return l}(ez,t.path,eV):function(t,e,n){var r=eN(e.x,e.y,e.width,e.height,t.x,t.y,eB);return n.set(eB[0],eB[1]),r}(ez,d,eV);g<u&&(u=g,ez.transform(c),eV.transform(c),eV.toArray(o[0]),ez.toArray(o[1]),eF.toArray(o[2]))}(function(t,e){if(e<=180&&e>0){e=e/180*Math.PI,eF.fromArray(t[0]),ez.fromArray(t[1]),eV.fromArray(t[2]),ek.A.sub(eH,eF,ez),ek.A.sub(ej,eV,ez);var n=eH.len(),r=ej.len();if(!(n<.001)&&!(r<.001)&&(eH.scale(1/n),ej.scale(1/r),Math.cos(e)<eH.dot(ej))){var i=eE(ez.x,ez.y,eV.x,eV.y,eF.x,eF.y,eW,!1);eG.fromArray(eW),eG.scaleAndAdd(ej,i/Math.tan(Math.PI-e));var o=eV.x!==ez.x?(eG.x-ez.x)/(eV.x-ez.x):(eG.y-ez.y)/(eV.y-ez.y);if(isNaN(o))return;o<0?ek.A.copy(eG,ez):o>1&&ek.A.copy(eG,eV),eG.toArray(t[1])}}})(o,e.get("minTurnAngle")),n.setShape({points:o})}}}var eW=[],eG=new ek.A;function eq(t,e,n,r){var i="normal"===n,o=i?t:t.ensureState(n);o.ignore=e;var a=r.get("smooth");a&&!0===a&&(a=.3),o.shape=o.shape||{},a>0&&(o.shape.smooth=a);var s=r.getModel("lineStyle").getLineStyle();i?t.useStyle(s):o.style=s}function eX(t,e){var n=e.smooth,r=e.points;if(r)if(t.moveTo(r[0][0],r[0][1]),n>0&&r.length>=3){var i=v.dist(r[0],r[1]),o=v.dist(r[1],r[2]);if(!i||!o){t.lineTo(r[1][0],r[1][1]),t.lineTo(r[2][0],r[2][1]);return}var a=Math.min(i,o)*n,s=v.lerp([],r[1],r[0],a/i),u=v.lerp([],r[1],r[2],a/o),l=v.lerp([],s,u,.5);t.bezierCurveTo(s[0],s[1],s[0],s[1],l[0],l[1]),t.bezierCurveTo(u[0],u[1],u[0],u[1],r[2][0],r[2][1])}else for(var c=1;c<r.length;c++)t.lineTo(r[c][0],r[c][1])}var eY=[0,0],eZ=[0,0],e$=new ek.A,eK=new ek.A,eQ=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new ek.A;for(var n=0;n<2;n++)this._axes[n]=new ek.A;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,r=this._axes,i=t.x,o=t.y,a=i+t.width,s=o+t.height;if(n[0].set(i,o),n[1].set(a,o),n[2].set(a,s),n[3].set(i,s),e)for(var u=0;u<4;u++)n[u].transform(e);ek.A.sub(r[0],n[1],n[0]),ek.A.sub(r[1],n[3],n[0]),r[0].normalize(),r[1].normalize();for(var u=0;u<2;u++)this._origin[u]=r[u].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,r=!e;return(e$.set(1/0,1/0),eK.set(0,0),!this._intersectCheckOneSide(this,t,e$,eK,r,1)&&(n=!1,r)||!this._intersectCheckOneSide(t,this,e$,eK,r,-1)&&(n=!1,r))?n:(r||ek.A.copy(e,n?e$:eK),n)},t.prototype._intersectCheckOneSide=function(t,e,n,r,i,o){for(var a=!0,s=0;s<2;s++){var u=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,eY),this._getProjMinMaxOnAxis(s,e._corners,eZ),eY[1]<eZ[0]||eY[0]>eZ[1]){if(a=!1,i)return a;var l=Math.abs(eZ[0]-eY[1]),c=Math.abs(eY[0]-eZ[1]);Math.min(l,c)>r.len()&&(l<c?ek.A.scale(r,u,-l*o):ek.A.scale(r,u,c*o))}else if(n){var l=Math.abs(eZ[0]-eY[1]),c=Math.abs(eY[0]-eZ[1]);Math.min(l,c)<n.len()&&(l<c?ek.A.scale(n,u,l*o):ek.A.scale(n,u,-c*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var r=this._axes[t],i=this._origin,o=e[0].dot(r)+i[t],a=o,s=o,u=1;u<e.length;u++){var l=e[u].dot(r)+i[t];a=Math.min(l,a),s=Math.max(l,s)}n[0]=a,n[1]=s},t}();function eJ(t,e,n,r,i,o){var a,s,u,l=t.length;if(!(l<2)){t.sort(function(t,n){return t.rect[e]-n.rect[e]});for(var c=0,h=!1,f=[],p=0,d=0;d<l;d++){var g=t[d],y=g.rect;(a=y[e]-c)<0&&(y[e]-=a,g.label[e]-=a,h=!0);var v=Math.max(-a,0);f.push(v),p+=v,c=y[e]+y[n]}p>0&&o&&w(-p/l,0,l);var m=t[0],_=t[l-1];return x(),s<0&&S(-s,.8),u<0&&S(u,.8),x(),b(s,u,1),b(u,s,-1),x(),s<0&&M(-s),u<0&&M(u),h}function x(){s=m.rect[e]-r,u=i-_.rect[e]-_.rect[n]}function b(t,e,n){if(t<0){var r=Math.min(e,-t);if(r>0){w(r*n,0,l);var i=r+t;i<0&&S(-i*n,1)}else S(-t*n,1)}}function w(n,r,i){0!==n&&(h=!0);for(var o=r;o<i;o++){var a=t[o],s=a.rect;s[e]+=n,a.label[e]+=n}}function S(r,i){for(var o=[],a=0,s=1;s<l;s++){var u=t[s-1].rect,c=Math.max(t[s].rect[e]-u[e]-u[n],0);o.push(c),a+=c}if(a){var h=Math.min(Math.abs(r)/a,i);if(r>0)for(var s=0;s<l-1;s++){var f=o[s]*h;w(f,0,s+1)}else for(var s=l-1;s>0;s--){var f=o[s-1]*h;w(-f,s,l)}}}function M(t){for(var e=t<0?-1:1,n=Math.ceil((t=Math.abs(t))/(l-1)),r=0;r<l-1;r++)if(e>0?w(n,0,r+1):w(-n,l-r-1,l),(t-=n)<=0)return}}var e0=["align","verticalAlign","width","height","fontSize"],e1=new eT.Ay,e2=(0,S.$r)(),e5=(0,S.$r)();function e3(t,e,n){for(var r=0;r<n.length;r++){var i=n[r];null!=e[i]&&(t[i]=e[i])}}var e4=["x","y","rotation"],e6=function(){function t(){this._labelList=[],this._chartViewList=[]}return t.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},t.prototype._addLabel=function(t,e,n,r,i){var o,a=r.style,s=r.__hostTarget.textConfig||{},u=r.getComputedTransform(),l=r.getBoundingRect().plain();tR.A.applyTransform(l,l,u),u?e1.setLocalTransform(u):(e1.x=e1.y=e1.rotation=e1.originX=e1.originY=0,e1.scaleX=e1.scaleY=1),e1.rotation=(0,eD.n)(e1.rotation);var c=r.__hostTarget;if(c){o=c.getBoundingRect().plain();var h=c.getComputedTransform();tR.A.applyTransform(o,o,h)}var f=o&&c.getTextGuideLine();this._labelList.push({label:r,labelLine:f,seriesModel:n,dataIndex:t,dataType:e,layoutOption:i,computedLayoutOption:null,rect:l,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:r.ignore,labelGuideIgnore:f&&f.ignore,x:e1.x,y:e1.y,scaleX:e1.scaleX,scaleY:e1.scaleY,rotation:e1.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:r.cursor,attachedPos:s.position,attachedRot:s.rotation}})},t.prototype.addLabelsOfSeries=function(t){var e=this;this._chartViewList.push(t);var n=t.__model,r=n.get("labelLayout");((0,m.isFunction)(r)||(0,m.keys)(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var i=t.getTextContent(),o=(0,tS.z)(t);i&&!i.disableLabelLayout&&e._addLabel(o.dataIndex,o.dataType,n,i,r)})},t.prototype.updateLayoutConfig=function(t){for(var e=t.getWidth(),n=t.getHeight(),r=0;r<this._labelList.length;r++){var i=this._labelList[r],o=i.label,a=o.__hostTarget,s=i.defaultAttr,u=void 0;u=(u=(0,m.isFunction)(i.layoutOption)?i.layoutOption(function(t,e){var n=t.label,r=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(r&&r.shape.points)}}(i,a)):i.layoutOption)||{},i.computedLayoutOption=u;var l=Math.PI/180;a&&a.setTextConfig({local:!1,position:null!=u.x||null!=u.y?null:s.attachedPos,rotation:null!=u.rotate?u.rotate*l:s.attachedRot,offset:[u.dx||0,u.dy||0]});var c=!1;if(null!=u.x?(o.x=(0,H.lo)(u.x,e),o.setStyle("x",0),c=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=u.y?(o.y=(0,H.lo)(u.y,n),o.setStyle("y",0),c=!0):(o.y=s.y,o.setStyle("y",s.style.y)),u.labelLinePoints){var h=a.getTextGuideLine();h&&(h.setShape({points:u.labelLinePoints}),c=!1)}e2(o).needsUpdateLabelLine=c,o.rotation=null!=u.rotate?u.rotate*l:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var f=0;f<e0.length;f++){var p=e0[f];o.setStyle(p,null!=u[p]?u[p]:s.style[p])}if(u.draggable){if(o.draggable=!0,o.cursor="move",a){var d=i.seriesModel;null!=i.dataIndex&&(d=i.seriesModel.getData(i.dataType).getItemModel(i.dataIndex)),o.on("drag",function(t,e){return function(){eU(t,e)}}(a,d.getModel("labelLine")))}}else o.off("drag"),o.cursor=s.cursor}},t.prototype.layout=function(t){var e=t.getWidth(),n=t.getHeight(),r=function(t){for(var e=[],n=0;n<t.length;n++){var r=t[n];if(!r.defaultAttr.ignore){var i=r.label,o=i.getComputedTransform(),a=i.getBoundingRect(),s=!o||o[1]<1e-5&&o[2]<1e-5,u=i.style.margin||0,l=a.clone();l.applyTransform(o),l.x-=u/2,l.y-=u/2,l.width+=u,l.height+=u;var c=s?new eQ(a,o):null;e.push({label:i,labelLine:r.labelLine,rect:l,localRect:a,obb:c,priority:r.priority,defaultAttr:r.defaultAttr,layoutOption:r.computedLayoutOption,axisAligned:s,transform:o})}}return e}(this._labelList),i=(0,m.filter)(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),o=(0,m.filter)(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});eJ(i,"x","width",0,e,void 0),eJ(o,"y","height",0,n,void 0),function(t){var e=[];t.sort(function(t,e){return e.priority-t.priority});var n=new tR.A(0,0,0,0);function r(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var i=0;i<t.length;i++){var o=t[i],a=o.axisAligned,s=o.localRect,u=o.transform,l=o.label,c=o.labelLine;n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var h=o.obb,f=!1,p=0;p<e.length;p++){var d=e[p];if(n.intersect(d.rect)&&(a&&d.axisAligned||(d.obb||(d.obb=new eQ(d.localRect,d.transform)),h||(h=new eQ(s,u)),h.intersect(d.obb)))){f=!0;break}}f?(r(l),c&&r(c)):(l.attr("ignore",o.defaultAttr.ignore),c&&c.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}((0,m.filter)(r,function(t){return t.layoutOption.hideOverlap}))},t.prototype.processLabelsOverall=function(){var t=this;(0,m.each)(this._chartViewList,function(e){var n=e.__model,r=e.ignoreLabelLineUpdate,i=n.isAnimationEnabled();e.group.traverse(function(e){if(e.ignore&&!e.forceLabelAnimation)return!0;var o=!r,a=e.getTextContent();!o&&a&&(o=e2(a).needsUpdateLabelLine),o&&t._updateLabelLine(e,n),i&&t._animateLabels(e,n)})})},t.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),r=(0,tS.z)(t),i=r.dataIndex;if(n&&null!=i){var o=e.getData(r.dataType),a=o.getItemModel(i),s={},u=o.getItemVisual(i,"style");u&&(s.stroke=u[o.getVisual("drawType")]);var l=a.getModel("labelLine");!function(t,e,n){var r=t.getTextGuideLine(),i=t.getTextContent();if(!i){r&&t.removeTextGuideLine();return}for(var o=e.normal,a=o.get("show"),s=i.ignore,u=0;u<tT.wV.length;u++){var l=tT.wV[u],c=e[l],h="normal"===l;if(c){var f=c.get("show");if((h?s:(0,m.retrieve2)(i.states[l]&&i.states[l].ignore,s))||!(0,m.retrieve2)(f,a)){var p=h?r:r&&r.states[l];p&&(p.ignore=!0),r&&eq(r,!0,l,c);continue}!r&&(r=new t3.A,t.setTextGuideLine(r),h||!s&&a||eq(r,!0,"normal",e.normal),t.stateProxy&&(r.stateProxy=t.stateProxy)),eq(r,!1,l,c)}}if(r){(0,m.defaults)(r.style,n),r.style.fill=null;var d=o.get("showAbove");(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=d||!1,r.buildPath=eX}}(t,function(t,e){e=e||"labelLine";for(var n={normal:t.getModel(e)},r=0;r<tT.BV.length;r++){var i=tT.BV[r];n[i]=t.getModel([i,e])}return n}(a),s),eU(t,l)}},t.prototype._animateLabels=function(t,e){var n=t.getTextContent(),r=t.getTextGuideLine();if(n&&(t.forceLabelAnimation||!n.ignore&&!n.invisible&&!t.disableLabelAnimation&&!(0,tZ.LR)(t))){var i=e2(n),o=i.oldLayout,a=(0,tS.z)(t),s=a.dataIndex,u={x:n.x,y:n.y,rotation:n.rotation},l=e.getData(a.dataType);if(o){n.attr(o);var c=t.prevStates;c&&((0,m.indexOf)(c,"select")>=0&&n.attr(i.oldLayoutSelect),(0,m.indexOf)(c,"emphasis")>=0&&n.attr(i.oldLayoutEmphasis)),(0,tZ.oi)(n,u,e,s)}else if(n.attr(u),!(0,tM.Lu)(n).valueAnimation){var h=(0,m.retrieve2)(n.style.opacity,1);n.style.opacity=0,(0,tZ.LW)(n,{style:{opacity:h}},e,s)}if(i.oldLayout=u,n.states.select){var f=i.oldLayoutSelect={};e3(f,u,e4),e3(f,n.states.select,e4)}if(n.states.emphasis){var p=i.oldLayoutEmphasis={};e3(p,u,e4),e3(p,n.states.emphasis,e4)}(0,tM.xb)(n,s,l,e,e)}if(r&&!r.ignore&&!r.invisible){var i=e5(r),o=i.oldLayout,d={points:r.shape.points};o?(r.attr({shape:o}),(0,tZ.oi)(r,{shape:d},e)):(r.setShape(d),r.style.strokePercent=0,(0,tZ.LW)(r,{style:{strokePercent:1}},e)),i.oldLayout=d}},t}(),e8=(0,S.$r)();(0,tP.Y)(function(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){var r=e8(e).labelManager;r||(r=e8(e).labelManager=new e6),r.clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var r=e8(e).labelManager;n.updatedSeries.forEach(function(t){r.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),r.updateLayoutConfig(e),r.layout(e),r.processLabelsOverall()})})},1764:(t,e,n)=>{"use strict";n.d(e,{J:()=>a,m:()=>i});var r=n(3809),i=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],o=(0,r.A)(i),a=function(){function t(){}return t.prototype.getLineStyle=function(t){return o(this,t)},t}()},1798:(t,e,n)=>{"use strict";n.d(e,{py:()=>v,v5:()=>y});var r=n(7439),i=n(6187),o=n(4123),a=n(2170),s=n(3424),u=n(3312),l=n(8535),c=function(){function t(){}return t.prototype.getRawData=function(){throw Error("not supported")},t.prototype.getRawDataItem=function(t){throw Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return(0,s.Pn)(t,e)},t}();function h(t){return m(t.sourceFormat)||(0,u.$8)(""),t.data}function f(t){var e=t.sourceFormat,n=t.data;if(m(e)||(0,u.$8)(""),e===r.Km){for(var i=[],a=0,s=n.length;a<s;a++)i.push(n[a].slice());return i}if(e===r.Wk){for(var i=[],a=0,s=n.length;a<s;a++)i.push((0,o.extend)({},n[a]));return i}}function p(t,e,n){if(null!=n){if((0,o.isNumber)(n)||!isNaN(n)&&!(0,o.hasOwn)(e,n))return t[n];else if((0,o.hasOwn)(e,n))return e[n]}}function d(t){return(0,o.clone)(t)}var g=(0,o.createHashMap)();function y(t){var e=(t=(0,o.clone)(t)).type;e||(0,u.$8)("");var n=e.split(":");2!==n.length&&(0,u.$8)("");var r=!1;"echarts"===n[0]&&(e=n[1],r=!0),t.__isBuiltIn=r,g.set(e,t)}function v(t,e,n){var s=(0,i.qB)(t),y=s.length;y||(0,u.$8)("");for(var v=0;v<y;v++)e=function(t,e,n,s){e.length||(0,u.$8)(""),(0,o.isObject)(t)||(0,u.$8)("");var y=t.type,v=g.get(y);v||(0,u.$8)("");var _=(0,o.map)(e,function(t){return function(t,e){var n=new c,i=t.data,s=n.sourceFormat=t.sourceFormat,l=t.startIndex;t.seriesLayoutBy!==r.i_&&(0,u.$8)("");var g=[],y={},v=t.dimensionsDefine;if(v)(0,o.each)(v,function(t,e){var n=t.name,r={index:e,name:n,displayName:t.displayName};g.push(r),null!=n&&((0,o.hasOwn)(y,n)&&(0,u.$8)(""),y[n]=r)});else for(var m=0;m<t.dimensionsDetectedCount||0;m++)g.push({index:m});var _=(0,a.sC)(s,r.i_);e.__isBuiltIn&&(n.getRawDataItem=function(t){return _(i,l,g,t)},n.getRawData=(0,o.bind)(h,null,t)),n.cloneRawData=(0,o.bind)(f,null,t);var x=(0,a.Kd)(s,r.i_);n.count=(0,o.bind)(x,null,i,l,g);var b=(0,a.uQ)(s);n.retrieveValue=function(t,e){return w(_(i,l,g,t),e)};var w=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=g[e];if(n)return b(t,e,n.name)}};return n.getDimensionInfo=(0,o.bind)(p,null,g,y),n.cloneAllDimensionInfo=(0,o.bind)(d,null,g),n}(t,v)}),x=(0,i.qB)(v.transform({upstream:_[0],upstreamList:_,config:(0,o.clone)(t.config)}));return(0,o.map)(x,function(t,n){(0,o.isObject)(t)||(0,u.$8)(""),t.data||(0,u.$8)(""),m((0,l.wZ)(t.data))||(0,u.$8)("");var i,a=e[0];if(a&&0===n&&!t.dimensions){var s=a.startIndex;s&&(t.data=a.data.slice(0,s).concat(t.data)),i={seriesLayoutBy:r.i_,sourceHeader:s,dimensions:a.metaRawOption.dimensions}}else i={seriesLayoutBy:r.i_,sourceHeader:0,dimensions:t.dimensions};return(0,l.gV)(t.data,i,null)})}(s[v],e,0,0),v!==y-1&&(e.length=Math.max(e.length,1));return e}function m(t){return t===r.Km||t===r.Wk}},1823:(t,e,n)=>{"use strict";n.d(e,{EJ:()=>s,FQ:()=>g,j_:()=>h});var r=n(9662),i=n(4123),o=n(6847),a=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function s(t,e,n,r,i){var o={};return u(o,t,e,n,r,i),o.text}function u(t,e,n,r,i,o){if(!n){t.text="",t.isTruncated=!1;return}var a=(e+"").split("\n");o=l(n,r,i,o);for(var s=!1,u={},h=0,f=a.length;h<f;h++)c(u,a[h],o),a[h]=u.textLine,s=s||u.isTruncated;t.text=a.join("\n"),t.isTruncated=s}function l(t,e,n,r){r=r||{};var a=(0,i.extend)({},r);a.font=e,n=(0,i.retrieve2)(n,"..."),a.maxIterations=(0,i.retrieve2)(r.maxIterations,2);var s=a.minChar=(0,i.retrieve2)(r.minChar,0);a.cnCharWidth=(0,o.RG)("国",e);var u=a.ascCharWidth=(0,o.RG)("a",e);a.placeholder=(0,i.retrieve2)(r.placeholder,"");for(var l=t=Math.max(0,t-1),c=0;c<s&&l>=u;c++)l-=u;var h=(0,o.RG)(n,e);return h>l&&(n="",h=0),l=t-h,a.ellipsis=n,a.ellipsisWidth=h,a.contentWidth=l,a.containerWidth=t,a}function c(t,e,n){var r=n.containerWidth,i=n.font,a=n.contentWidth;if(!r){t.textLine="",t.isTruncated=!1;return}var s=(0,o.RG)(e,i);if(s<=r){t.textLine=e,t.isTruncated=!1;return}for(var u=0;;u++){if(s<=a||u>=n.maxIterations){e+=n.ellipsis;break}var l=0===u?function(t,e,n,r){for(var i=0,o=0,a=t.length;o<a&&i<e;o++){var s=t.charCodeAt(o);i+=0<=s&&s<=127?n:r}return o}(e,a,n.ascCharWidth,n.cnCharWidth):s>0?Math.floor(e.length*a/s):0;e=e.substr(0,l),s=(0,o.RG)(e,i)}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}function h(t,e){null!=t&&(t+="");var n,r=e.overflow,a=e.padding,s=e.font,u=(0,o.ks)(s),h=(0,i.retrieve2)(e.lineHeight,u),f=!!e.backgroundColor,p="truncate"===e.lineOverflow,d=!1,g=e.width,y=(n=null!=g&&("break"===r||"breakAll"===r)?t?m(t,e.font,g,"breakAll"===r,0).lines:[]:t?t.split("\n"):[]).length*h,v=(0,i.retrieve2)(e.height,y);if(y>v&&p){var _=Math.floor(v/h);d=d||n.length>_,n=n.slice(0,_)}if(t&&"truncate"===r&&null!=g)for(var x=l(g,s,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),b={},w=0;w<n.length;w++)c(b,n[w],x),n[w]=b.textLine,d=d||b.isTruncated;for(var S=v,M=0,w=0;w<n.length;w++)M=Math.max((0,o.RG)(n[w],s),M);null==g&&(g=M);var A=M;return a&&(S+=a[0]+a[2],A+=a[1]+a[3],g+=a[1]+a[3]),f&&(A=g),{lines:n,height:v,outerWidth:A,outerHeight:S,lineHeight:h,calculatedLineHeight:u,contentWidth:M,contentHeight:y,width:g,isTruncated:d}}var f=function(){},p=function(t){this.tokens=[],t&&(this.tokens=t)},d=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function g(t,e){var n,s=new d;if(null!=t&&(t+=""),!t)return s;for(var l=e.width,c=e.height,h=e.overflow,f=("break"===h||"breakAll"===h)&&null!=l?{width:l,accumWidth:0,breakAll:"breakAll"===h}:null,p=a.lastIndex=0;null!=(n=a.exec(t));){var g=n.index;g>p&&y(s,t.substring(p,g),e,f),y(s,n[2],e,f,n[1]),p=a.lastIndex}p<t.length&&y(s,t.substring(p,t.length),e,f);var v=[],m=0,_=0,x=e.padding,b="truncate"===h,w="truncate"===e.lineOverflow,S={};function M(t,e,n){t.width=e,t.lineHeight=n,m+=n,_=Math.max(_,e)}e:for(var A=0;A<s.lines.length;A++){for(var T=s.lines[A],k=0,C=0,I=0;I<T.tokens.length;I++){var D=T.tokens[I],O=D.styleName&&e.rich[D.styleName]||{},P=D.textPadding=O.padding,L=P?P[1]+P[3]:0,R=D.font=O.font||e.font;D.contentHeight=(0,o.ks)(R);var E=(0,i.retrieve2)(O.height,D.contentHeight);if(D.innerHeight=E,P&&(E+=P[0]+P[2]),D.height=E,D.lineHeight=(0,i.retrieve3)(O.lineHeight,e.lineHeight,E),D.align=O&&O.align||e.align,D.verticalAlign=O&&O.verticalAlign||"middle",w&&null!=c&&m+D.lineHeight>c){var N=s.lines.length;I>0?(T.tokens=T.tokens.slice(0,I),M(T,C,k),s.lines=s.lines.slice(0,A+1)):s.lines=s.lines.slice(0,A),s.isTruncated=s.isTruncated||s.lines.length<N;break e}var B=O.width,F=null==B||"auto"===B;if("string"==typeof B&&"%"===B.charAt(B.length-1))D.percentWidth=B,v.push(D),D.contentWidth=(0,o.RG)(D.text,R);else{if(F){var z=O.backgroundColor,V=z&&z.image;V&&(V=r.SR(V),r.xA(V)&&(D.width=Math.max(D.width,V.width*E/V.height)))}var H=b&&null!=l?l-C:null;null!=H&&H<D.width?!F||H<L?(D.text="",D.width=D.contentWidth=0):(u(S,D.text,H-L,R,e.ellipsis,{minChar:e.truncateMinChar}),D.text=S.text,s.isTruncated=s.isTruncated||S.isTruncated,D.width=D.contentWidth=(0,o.RG)(D.text,R)):D.contentWidth=(0,o.RG)(D.text,R)}D.width+=L,C+=D.width,O&&(k=Math.max(k,D.lineHeight))}M(T,C,k)}s.outerWidth=s.width=(0,i.retrieve2)(l,_),s.outerHeight=s.height=(0,i.retrieve2)(c,m),s.contentHeight=m,s.contentWidth=_,x&&(s.outerWidth+=x[1]+x[3],s.outerHeight+=x[0]+x[2]);for(var A=0;A<v.length;A++){var D=v[A],j=D.percentWidth;D.width=parseInt(j,10)/100*s.width}return s}function y(t,e,n,r,i){var a,s,u=""===e,l=i&&n.rich[i]||{},c=t.lines,h=l.font||n.font,d=!1;if(r){var g=l.padding,y=g?g[1]+g[3]:0;if(null!=l.width&&"auto"!==l.width){var v=(0,o.lo)(l.width,r.width)+y;c.length>0&&v+r.accumWidth>r.width&&(a=e.split("\n"),d=!0),r.accumWidth=v}else{var _=m(e,h,r.width,r.breakAll,r.accumWidth);r.accumWidth=_.accumWidth+y,s=_.linesWidths,a=_.lines}}else a=e.split("\n");for(var x=0;x<a.length;x++){var b=a[x],w=new f;if(w.styleName=i,w.text=b,w.isLineHolder=!b&&!u,"number"==typeof l.width?w.width=l.width:w.width=s?s[x]:(0,o.RG)(b,h),x||d)c.push(new p([w]));else{var S=(c[c.length-1]||(c[0]=new p)).tokens,M=S.length;1===M&&S[0].isLineHolder?S[0]=w:(b||!M||u)&&S.push(w)}}}var v=(0,i.reduce)(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function m(t,e,n,r,i){for(var a=[],s=[],u="",l="",c=0,h=0,f=0;f<t.length;f++){var p=t.charAt(f);if("\n"===p){l&&(u+=l,h+=c),a.push(u),s.push(h),u="",l="",c=0,h=0;continue}var d=(0,o.RG)(p,e),g=!r&&!function(t){var e;return(!((e=t.charCodeAt(0))>=32)||!(e<=591))&&(!(e>=880)||!(e<=4351))&&(!(e>=4608)||!(e<=5119))&&(!(e>=7680)||!(e<=8303))||!!v[t]}(p);if(a.length?h+d>n:i+h+d>n){h?(u||l)&&(g?(u||(u=l,l="",h=c=0),a.push(u),s.push(h-c),l+=p,c+=d,u="",h=c):(l&&(u+=l,l="",c=0),a.push(u),s.push(h),u=p,h=d)):g?(a.push(l),s.push(c),l=p,c=d):(a.push(p),s.push(d));continue}h+=d,g?(l+=p,c+=d):(l&&(u+=l,l="",c=0),u+=p)}return a.length||u||(u=t,l="",c=0),l&&(u+=l),u&&(a.push(u),s.push(h)),1===a.length&&(h+=i),{accumWidth:h,lines:a,linesWidths:s}}},1834:(t,e,n)=>{"use strict";n.d(e,{Xi:()=>C});var r=n(5921),i=n(8248),o=n(9662),a=n(3030),s=n(4271),u=n(3493),l=n(6436),c=n(4123),h=n(3219),f=n(3273),p=n(2316),d=new i.A(!0);function g(t){var e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0}function y(t){return"string"==typeof t&&"none"!==t}function v(t){var e=t.fill;return null!=e&&"none"!==e}function m(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function _(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function x(t,e,n){var r=(0,o.OD)(e.image,e.__image,n);if((0,o.xA)(r)){var i=t.createPattern(r,e.repeat||"repeat");if("function"==typeof DOMMatrix&&i&&i.setTransform){var a=new DOMMatrix;a.translateSelf(e.x||0,e.y||0),a.rotateSelf(0,0,(e.rotation||0)*c.RADIAN_TO_DEGREE),a.scaleSelf(e.scaleX||1,e.scaleY||1),i.setTransform(a)}return i}}var b=["shadowBlur","shadowOffsetX","shadowOffsetY"],w=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function S(t,e,n,i,o){var a=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){T(t,o),a=!0;var s=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(s)?r.oN.opacity:s}(i||e.blend!==n.blend)&&(a||(T(t,o),a=!0),t.globalCompositeOperation=e.blend||r.oN.blend);for(var u=0;u<b.length;u++){var l=b[u];(i||e[l]!==n[l])&&(a||(T(t,o),a=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(a||(T(t,o),a=!0),t.shadowColor=e.shadowColor||r.oN.shadowColor),a}function M(t,e,n,r,i){var o=k(e,i.inHover),a=r?null:n&&k(n,i.inHover)||{};if(o===a)return!1;var s=S(t,o,a,r,i);if((r||o.fill!==a.fill)&&(s||(T(t,i),s=!0),y(o.fill)&&(t.fillStyle=o.fill)),(r||o.stroke!==a.stroke)&&(s||(T(t,i),s=!0),y(o.stroke)&&(t.strokeStyle=o.stroke)),(r||o.opacity!==a.opacity)&&(s||(T(t,i),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var u=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==u&&(s||(T(t,i),s=!0),t.lineWidth=u)}for(var l=0;l<w.length;l++){var c=w[l],h=c[0];(r||o[h]!==a[h])&&(s||(T(t,i),s=!0),t[h]=o[h]||c[1])}return s}function A(t,e){var n=e.transform,r=t.dpr||1;n?t.setTransform(r*n[0],r*n[1],r*n[2],r*n[3],r*n[4],r*n[5]):t.setTransform(r,0,0,r,0,0)}function T(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function k(t,e){return e&&t.__hoverStyle||t.style}function C(t,e){(function t(e,n,r,i){var c,y=n.transform;if(!n.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1)){n.__dirty&=~f.M,n.__isRendered=!1;return}var b=n.__clipPaths,w=r.prevElClipPaths,C=!1,I=!1;if(!w||(0,a.KU)(b,w)){if(w&&w.length&&(T(e,r),e.restore(),I=C=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),b&&b.length){T(e,r),e.save();for(var D=!1,O=0;O<b.length;O++){var P=b[O];D=D||P.isZeroArea(),A(e,P),e.beginPath(),P.buildPath(e,P.shape),e.clip()}r.allClipped=D,C=!0}r.prevElClipPaths=b}if(r.allClipped){n.__isRendered=!1;return}n.beforeBrush&&n.beforeBrush(),n.innerBeforeBrush();var L=r.prevEl;L||(I=C=!0);var R=n instanceof s.Ay&&n.autoBatch&&(B=v(N=n.style),F=g(N),!(N.lineDash||!(B^F)||B&&"string"!=typeof N.fill||F&&"string"!=typeof N.stroke||N.strokePercent<1||N.strokeOpacity<1||N.fillOpacity<1));!C&&(z=L.transform,y&&z?y[0]===z[0]&&y[1]===z[1]&&y[2]===z[2]&&y[3]===z[3]&&y[4]===z[4]&&y[5]===z[5]:+(!y&&!z))?R||T(e,r):(T(e,r),A(e,n));var E=k(n,r.inHover);if(n instanceof s.Ay)1!==r.lastDrawType&&(I=!0,r.lastDrawType=1),M(e,n,L,I,r),R&&(r.batchFill||r.batchStroke)||e.beginPath(),function(t,e,n,r){var i,o,s,u=g(n),l=v(n),c=n.strokePercent,p=c<1,y=!e.path;(!e.silent||p)&&y&&e.createPathProxy();var b=e.path||d,w=e.__dirty;if(!r){var S=n.fill,M=n.stroke,A=l&&!!S.colorStops,T=u&&!!M.colorStops,k=l&&!!S.image,C=u&&!!M.image,I=void 0,D=void 0,O=void 0,P=void 0,L=void 0;(A||T)&&(L=e.getBoundingRect()),A&&(I=w?(0,a.Ff)(t,S,L):e.__canvasFillGradient,e.__canvasFillGradient=I),T&&(D=w?(0,a.Ff)(t,M,L):e.__canvasStrokeGradient,e.__canvasStrokeGradient=D),k&&(O=w||!e.__canvasFillPattern?x(t,S,e):e.__canvasFillPattern,e.__canvasFillPattern=O),C&&(P=w||!e.__canvasStrokePattern?x(t,M,e):e.__canvasStrokePattern,e.__canvasStrokePattern=O),A?t.fillStyle=I:k&&(O?t.fillStyle=O:l=!1),T?t.strokeStyle=D:C&&(P?t.strokeStyle=P:u=!1)}var R=e.getGlobalScale();b.setScale(R[0],R[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(o=(i=(0,h.V)(e))[0],s=i[1]);var E=!0;(y||w&f.Dl)&&(b.setDPR(t.dpr),p?b.setContext(null):(b.setContext(t),E=!1),b.reset(),e.buildPath(b,e.shape,r),b.toStatic(),e.pathUpdated()),E&&b.rebuildPath(t,p?c:1),o&&(t.setLineDash(o),t.lineDashOffset=s),!r&&(n.strokeFirst?(u&&_(t,n),l&&m(t,n)):(l&&m(t,n),u&&_(t,n))),o&&t.setLineDash([])}(e,n,E,R),R&&(r.batchFill=E.fill||"",r.batchStroke=E.stroke||"");else if(n instanceof l.A){3!==r.lastDrawType&&(I=!0,r.lastDrawType=3),M(e,n,L,I,r);var N,B,F,z,V,H=E.text;if(null!=H&&(H+=""),H){e.font=E.font||p.OH,e.textAlign=E.textAlign,e.textBaseline=E.textBaseline;var j=void 0,U=void 0;e.setLineDash&&E.lineDash&&(j=(V=(0,h.V)(n))[0],U=V[1]),j&&(e.setLineDash(j),e.lineDashOffset=U),E.strokeFirst?(g(E)&&e.strokeText(H,E.x,E.y),v(E)&&e.fillText(H,E.x,E.y)):(v(E)&&e.fillText(H,E.x,E.y),g(E)&&e.strokeText(H,E.x,E.y)),j&&e.setLineDash([])}}else n instanceof u.Ay?(2!==r.lastDrawType&&(I=!0,r.lastDrawType=2),c=I,S(e,k(n,r.inHover),L&&k(L,r.inHover),c,r),function(t,e,n){var r=e.__image=(0,o.OD)(n.image,e.__image,e,e.onload);if(r&&(0,o.xA)(r)){var i=n.x||0,a=n.y||0,s=e.getWidth(),u=e.getHeight(),l=r.width/r.height;if(null==s&&null!=u?s=u*l:null==u&&null!=s?u=s/l:null==s&&null==u&&(s=r.width,u=r.height),n.sWidth&&n.sHeight){var c=n.sx||0,h=n.sy||0;t.drawImage(r,c,h,n.sWidth,n.sHeight,i,a,s,u)}else if(n.sx&&n.sy){var c=n.sx,h=n.sy,f=s-c,p=u-h;t.drawImage(r,c,h,f,p,i,a,s,u)}else t.drawImage(r,i,a,s,u)}}(e,n,E)):n.getTemporalDisplayables&&(4!==r.lastDrawType&&(I=!0,r.lastDrawType=4),function(e,n,r){var i,o,a=n.getDisplayables(),s=n.getTemporalDisplayables();e.save();var u={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(i=n.getCursor(),o=a.length;i<o;i++){var l=a[i];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),t(e,l,u,i===o-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),u.prevEl=l}for(var c=0,h=s.length;c<h;c++){var l=s[c];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),t(e,l,u,c===h-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),u.prevEl=l}n.clearTemporalDisplayables(),n.notClear=!0,e.restore()}(e,n,r));R&&i&&T(e,r),n.innerAfterBrush(),n.afterBrush&&n.afterBrush(),r.prevEl=n,n.__dirty=0,n.__isRendered=!0})(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}},1929:(t,e,n)=>{"use strict";n.d(e,{$Q:()=>a,A6:()=>s,vf:()=>u});var r=n(4123),i=n(2660),o=Math.round(10*Math.random());function a(t){return[t||"",o++].join("_")}function s(t){var e={};t.registerSubTypeDefaulter=function(t,n){e[(0,i.CC)(t).main]=n},t.determineSubType=function(n,r){var o=r.type;if(!o){var a=(0,i.CC)(n).main;t.hasSubTypes(n)&&e[a]&&(o=e[a](r))}return o}}function u(t,e){t.topologicalTravel=function(t,i,o,a){if(t.length){var s,u,l,c=(s=i,u={},l=[],r.each(s,function(t){var i,o,a,c=n(u,t),h=(i=c.originalDeps=e(t),o=s,a=[],r.each(i,function(t){r.indexOf(o,t)>=0&&a.push(t)}),a);c.entryCount=h.length,0===c.entryCount&&l.push(t),r.each(h,function(e){0>r.indexOf(c.predecessor,e)&&c.predecessor.push(e);var i=n(u,e);0>r.indexOf(i.successor,e)&&i.successor.push(t)})}),{graph:u,noEntryList:l}),h=c.graph,f=c.noEntryList,p={};for(r.each(t,function(t){p[t]=!0});f.length;){var d=f.pop(),g=h[d],y=!!p[d];y&&(o.call(a,d,g.originalDeps.slice()),delete p[d]),r.each(g.successor,y?m:v)}r.each(p,function(){throw Error("")})}function v(t){h[t].entryCount--,0===h[t].entryCount&&f.push(t)}function m(t){p[t]=!0,v(t)}};function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}}},2045:(t,e,n)=>{"use strict";n.d(e,{Lv:()=>u,A$:()=>c,pr:()=>f,A4:()=>h,E:()=>l});var r=n(1613),i=n(7170),o=n(4123),a={},s={},u=i.A.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage||"EN").toUpperCase().indexOf("ZH")>-1?"ZH":"EN";function l(t,e){s[t=t.toUpperCase()]=new r.A(e),a[t]=e}function c(t){if(!(0,o.isString)(t))return(0,o.merge)((0,o.clone)(t),(0,o.clone)(a.EN),!1);var e=a[t.toUpperCase()]||{};return"ZH"===t||"EN"===t?(0,o.clone)(e):(0,o.merge)((0,o.clone)(e),(0,o.clone)(a.EN),!1)}function h(t){return s[t]}function f(){return s.EN}l("EN",{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),l("ZH",{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}})},2170:(t,e,n)=>{"use strict";n.d(e,{Kd:()=>m,QE:()=>S,d1:()=>f,sC:()=>g,uQ:()=>b});var r,i,o,a,s,u=n(4123),l=n(6187),c=n(8535),h=n(7439),f=function(){var t;function e(t,e){var n=(0,c.tP)(t)?t:(0,c.AF)(t);this._source=n;var r=this._data=n.data;n.sourceFormat===h.XO&&(this._offset=0,this._dimSize=e,this._data=r),s(this,r,n)}return e.prototype.getSource=function(){return this._source},e.prototype.count=function(){return 0},e.prototype.getItem=function(t,e){},e.prototype.appendData=function(t){},e.prototype.clean=function(){},e.protoInitialize=void((t=e.prototype).pure=!1,t.persistent=!0),e.internalField=function(){s=function(t,i,o){var s=o.sourceFormat,l=o.seriesLayoutBy,c=o.startIndex,f=o.dimensionsDefine,p=a[w(s,l)];if((0,u.extend)(t,p),s===h.XO)t.getItem=e,t.count=r,t.fillStorage=n;else{var d=g(s,l);t.getItem=(0,u.bind)(d,null,i,c,f);var y=m(s,l);t.count=(0,u.bind)(y,null,i,c,f)}};var t,e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,r=this._dimSize,i=r*t,o=0;o<r;o++)e[o]=n[i+o];return e},n=function(t,e,n,r){for(var i=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=r[a],u=null==s[0]?1/0:s[0],l=null==s[1]?-1/0:s[1],c=e-t,h=n[a],f=0;f<c;f++){var p=i[f*o+a];h[t+f]=p,p<u&&(u=p),p>l&&(l=p)}s[0]=u,s[1]=l}},r=function(){return this._data?this._data.length/this._dimSize:0};function i(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={})[h.Km+"_"+h.i_]={pure:!0,appendData:i},t[h.Km+"_"+h.oC]={pure:!0,appendData:function(){throw Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[h.Wk]={pure:!0,appendData:i},t[h.t1]={pure:!0,appendData:function(t){var e=this._data;(0,u.each)(t,function(t,n){for(var r=e[n]||(e[n]=[]),i=0;i<(t||[]).length;i++)r.push(t[i])})}},t[h.mK]={appendData:i},t[h.XO]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},a=t}(),e}(),p=function(t,e,n,r){return t[r]},d=((r={})[h.Km+"_"+h.i_]=function(t,e,n,r){return t[r+e]},r[h.Km+"_"+h.oC]=function(t,e,n,r,i){r+=e;for(var o=i||[],a=0;a<t.length;a++){var s=t[a];o[a]=s?s[r]:null}return o},r[h.Wk]=p,r[h.t1]=function(t,e,n,r,i){for(var o=i||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[r]:null}return o},r[h.mK]=p,r);function g(t,e){return d[w(t,e)]}var y=function(t,e,n){return t.length},v=((i={})[h.Km+"_"+h.i_]=function(t,e,n){return Math.max(0,t.length-e)},i[h.Km+"_"+h.oC]=function(t,e,n){var r=t[0];return r?Math.max(0,r.length-e):0},i[h.Wk]=y,i[h.t1]=function(t,e,n){var r=t[n[0].name];return r?r.length:0},i[h.mK]=y,i);function m(t,e){return v[w(t,e)]}var _=function(t,e,n){return t[e]},x=((o={})[h.Km]=_,o[h.Wk]=function(t,e,n){return t[n]},o[h.t1]=_,o[h.mK]=function(t,e,n){var r=(0,l.vj)(t);return r instanceof Array?r[e]:r},o[h.XO]=_,o);function b(t){return x[t]}function w(t,e){return t===h.Km?t+"_"+e:t}function S(t,e,n){if(t){var r=t.getRawDataItem(e);if(null!=r){var i=t.getStore(),o=i.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=i.getDimensionProperty(a);return x[o](r,a,s)}var u=r;return o===h.mK&&(u=(0,l.vj)(r)),u}}}},2227:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,r){t.x=e.x+n.x*r,t.y=e.y+n.y*r},t.lerp=function(t,e,n,r){var i=1-r;t.x=i*e.x+r*n.x,t.y=i*e.y+r*n.y},t}()},2261:(t,e,n)=>{"use strict";n.d(e,{Cb:()=>u,JW:()=>v,QX:()=>l,YK:()=>f,fU:()=>d,he:()=>y,ob:()=>s,qg:()=>p,x5:()=>g});var r=n(4123),i=n(4958),o=n(3607),a=n(9799);function s(t){if(!(0,o.kf)(t))return r.isString(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}function u(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var l=r.normalizeCssArray,c=["a","b","c","d","e","f","g"],h=function(t,e){return"{"+t+(null==e?"":e)+"}"};function f(t,e,n){r.isArray(e)||(e=[e]);var o=e.length;if(!o)return"";for(var a=e[0].$vars||[],s=0;s<a.length;s++){var u=c[s];t=t.replace(h(u),h(u,0))}for(var l=0;l<o;l++)for(var f=0;f<a.length;f++){var p=e[l][a[f]];t=t.replace(h(c[f],l),n?(0,i.Me)(p):p)}return t}function p(t,e){var n=r.isString(t)?{color:t,extraCssText:e}:t||{},o=n.color,a=n.type;e=n.extraCssText;var s=n.renderMode||"html";return o?"html"===s?"subItem"===a?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+(0,i.Me)(o)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+(0,i.Me)(o)+";"+(e||"")+'"></span>':{renderMode:s,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===a?{width:4,height:4,borderRadius:2,backgroundColor:o}:{width:10,height:10,borderRadius:5,backgroundColor:o}}:""}function d(t,e,n){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var r=(0,o._U)(e),i=n?"getUTC":"get",s=r[i+"FullYear"](),u=r[i+"Month"]()+1,l=r[i+"Date"](),c=r[i+"Hours"](),h=r[i+"Minutes"](),f=r[i+"Seconds"](),p=r[i+"Milliseconds"]();return t.replace("MM",(0,a.eV)(u,2)).replace("M",u).replace("yyyy",s).replace("yy",(0,a.eV)(s%100+"",2)).replace("dd",(0,a.eV)(l,2)).replace("d",l).replace("hh",(0,a.eV)(c,2)).replace("h",c).replace("mm",(0,a.eV)(h,2)).replace("m",h).replace("ss",(0,a.eV)(f,2)).replace("s",f).replace("SSS",(0,a.eV)(p,3))}function g(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function y(t,e){return e=e||"transparent",r.isString(t)?t:r.isObject(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function v(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location.href=t}else window.open(t,e)}},2276:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(2115);let i=r.forwardRef(function({title:t,titleId:e,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},n),t?r.createElement("title",{id:e},t):null,r.createElement("path",{fillRule:"evenodd",d:"M14 8a.75.75 0 0 1-.75.75H4.56l3.22 3.22a.75.75 0 1 1-1.06 1.06l-4.5-4.5a.75.75 0 0 1 0-1.06l4.5-4.5a.75.75 0 0 1 1.06 1.06L4.56 7.25h8.69A.75.75 0 0 1 14 8Z",clipRule:"evenodd"}))})},2316:(t,e,n)=>{"use strict";n.d(e,{Gs:()=>u,OH:()=>o,gI:()=>r,yh:()=>s,zs:()=>i});var r=12,i="sans-serif",o="12px "+i,a=function(t){var e={};if("undefined"==typeof JSON)return e;for(var n=0;n<t.length;n++){var r=String.fromCharCode(n+32),i=(t.charCodeAt(n)-20)/100;e[r]=i}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),s={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(){var t,e;return function(n,i){if(!t){var u=s.createCanvas();t=u&&u.getContext("2d")}if(t)return e!==i&&(e=t.font=i||o),t.measureText(n);n=n||"",i=i||o;var l=/((?:\d+)?\.?\d*)px/.exec(i),c=l&&+l[1]||r,h=0;if(i.indexOf("mono")>=0)h=c*n.length;else for(var f=0;f<n.length;f++){var p=a[n[f]];h+=null==p?c:p*c}return{width:h}}}(),loadImage:function(t,e,n){var r=new Image;return r.onload=e,r.onerror=n,r.src=t,r}};function u(t){for(var e in s)t[e]&&(s[e]=t[e])}},2392:(t,e,n)=>{"use strict";n.d(e,{BV:()=>d,Du:()=>U,HY:()=>R,Iz:()=>X,JC:()=>B,JI:()=>F,Lm:()=>Q,Lv:()=>m,Lx:()=>tn,PW:()=>v,Q6:()=>x,QX:()=>N,SD:()=>E,T$:()=>tr,Tl:()=>G,U2:()=>_,_m:()=>q,_n:()=>te,e3:()=>p,fz:()=>ti,gd:()=>z,h5:()=>y,iJ:()=>K,jA:()=>Z,lV:()=>W,mc:()=>$,qR:()=>H,t6:()=>Y,u6:()=>tt,wV:()=>g,zX:()=>f});var r=n(4123),i=n(2663),o=n(411),a=n(6187),s=n(4271),u=1,l={},c=(0,a.$r)(),h=(0,a.$r)(),f=1,p=2,d=["emphasis","blur","select"],g=["normal","emphasis","blur","select"],y="highlight",v="downplay",m="select",_="unselect",x="toggleSelect";function b(t){return null!=t&&"none"!==t}function w(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function S(t){w(t,"emphasis",p)}function M(t){t.hoverState===p&&w(t,"normal",0)}function A(t){w(t,"blur",f)}function T(t){t.hoverState===f&&w(t,"normal",0)}function k(t){t.selected=!0}function C(t){t.selected=!1}function I(t,e,n){e(t,n),t.isGroup&&t.traverse(function(t){e(t,n)})}function D(t,e){var n,i,a,u,l,h=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var a=n&&(0,r.indexOf)(n,"select")>=0,u=!1;if(t instanceof s.Ay){var l=c(t),h=a&&l.selectFill||l.normalFill,f=a&&l.selectStroke||l.normalStroke;if(b(h)||b(f)){var p=(i=i||{}).style||{};"inherit"===p.fill?(u=!0,i=(0,r.extend)({},i),(p=(0,r.extend)({},p)).fill=h):!b(p.fill)&&b(h)?(u=!0,i=(0,r.extend)({},i),(p=(0,r.extend)({},p)).fill=(0,o.liftColor)(h)):!b(p.stroke)&&b(f)&&(u||(i=(0,r.extend)({},i),p=(0,r.extend)({},p)),p.stroke=(0,o.liftColor)(f)),i.style=p}}if(i&&null==i.z2){u||(i=(0,r.extend)({},i));var d=t.z2EmphasisLift;i.z2=t.z2+(null!=d?d:10)}return i}(this,0,e,h);else if("blur"===t)return n=h,i=(0,r.indexOf)(this.currentStates,t)>=0,a=this.style.opacity,u=i?null:function(t,e,n,r){for(var i=t.style,o={},a=0;a<e.length;a++){var s=e[a],u=i[s];o[s]=null==u?r&&r[s]:u}for(var a=0;a<t.animators.length;a++){var l=t.animators[a];l.__fromStateTransition&&0>l.__fromStateTransition.indexOf(n)&&"style"===l.targetName&&l.saveTo(o,e)}return o}(this,["opacity"],t,{opacity:1}),null==(l=(n=n||{}).style||{}).opacity&&(n=(0,r.extend)({},n),l=(0,r.extend)({opacity:i?a:.1*u.opacity},l),n.style=l),n;else if("select"===t){var f=h;if(f&&null==f.z2){f=(0,r.extend)({},f);var p=this.z2SelectLift;f.z2=this.z2+(null!=p?p:9)}return f}}return h}function O(t){t.stateProxy=D;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=D),n&&(n.stateProxy=D)}function P(t,e){V(t,e)||t.__highByOuter||I(t,S)}function L(t,e){V(t,e)||t.__highByOuter||I(t,M)}function R(t,e){t.__highByOuter|=1<<(e||0),I(t,S)}function E(t,e){(t.__highByOuter&=~(1<<(e||0)))||I(t,M)}function N(t){I(t,A)}function B(t){I(t,T)}function F(t){I(t,k)}function z(t){I(t,C)}function V(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function H(t){var e=t.getModel(),n=[],i=[];e.eachComponent(function(e,r){var o=h(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);a||i.push(s),o.isBlured&&(s.group.traverse(function(t){T(t)}),a&&n.push(r)),o.isBlured=!1}),(0,r.each)(i,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)})}function j(t,e,n,i){var o=i.getModel();function a(t,e){for(var n=0;n<e.length;n++){var r=t.getItemGraphicEl(e[n]);r&&B(r)}}if((n=n||"coordinateSystem",null!=t)&&e&&"none"!==e){var s=o.getSeriesByIndex(t),u=s.coordinateSystem;u&&u.master&&(u=u.master);var l=[];o.eachSeries(function(t){var o=s===t,c=t.coordinateSystem;c&&c.master&&(c=c.master);var f=c&&u?c===u:o;if(!("series"===n&&!o||"coordinateSystem"===n&&!f||"series"===e&&o)){if(i.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&o&&"self"===e||A(t)}),(0,r.isArrayLike)(e))a(t.getData(),e);else if((0,r.isObject)(e))for(var p=(0,r.keys)(e),d=0;d<p.length;d++)a(t.getData(p[d]),e[p[d]]);l.push(t),h(t).isBlured=!0}}),o.eachComponent(function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,o)}})}}function U(t,e,n){if(null!=t&&null!=e){var r=n.getModel().getComponent(t,e);if(r){h(r).isBlured=!0;var i=n.getViewOfComponentModel(r);i&&i.focusBlurEnabled&&i.group.traverse(function(t){A(t)})}}}function W(t,e,n){var o=t.seriesIndex,s=t.getData(e.dataType);if(s){var u=(0,a.le)(s,e);u=((0,r.isArray)(u)?u[0]:u)||0;var l=s.getItemGraphicEl(u);if(!l)for(var c=s.count(),h=0;!l&&h<c;)l=s.getItemGraphicEl(h++);if(l){var f=(0,i.z)(l);j(o,f.focus,f.blurScope,n)}else{var p=t.get(["emphasis","focus"]),d=t.get(["emphasis","blurScope"]);null!=p&&j(o,p,d,n)}}}function G(t,e,n,r){var o,a={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return a;var s=r.getModel().getComponent(t,e);if(!s)return a;var u=r.getViewOfComponentModel(s);if(!u||!u.findHighDownDispatchers)return a;for(var l=u.findHighDownDispatchers(n),c=0;c<l.length;c++)if("self"===(0,i.z)(l[c]).focus){o=!0;break}return{focusSelf:o,dispatchers:l}}function q(t,e,n){var o=(0,i.z)(t),a=G(o.componentMainType,o.componentIndex,o.componentHighDownName,n),s=a.dispatchers,u=a.focusSelf;s?(u&&U(o.componentMainType,o.componentIndex,n),(0,r.each)(s,function(t){return P(t,e)})):(j(o.seriesIndex,o.focus,o.blurScope,n),"self"===o.focus&&U(o.componentMainType,o.componentIndex,n),P(t,e))}function X(t,e,n){H(n);var o=(0,i.z)(t),a=G(o.componentMainType,o.componentIndex,o.componentHighDownName,n).dispatchers;a?(0,r.each)(a,function(t){return L(t,e)}):L(t,e)}function Y(t,e,n){if(tn(e)){var i=e.dataType,o=t.getData(i),s=(0,a.le)(o,e);(0,r.isArray)(s)||(s=[s]),t[e.type===x?"toggleSelect":e.type===m?"select":"unselect"](s,i)}}function Z(t){var e=t.getAllData();(0,r.each)(e,function(e){var n=e.data,r=e.type;n.eachItemGraphicEl(function(e,n){t.isSelected(n,r)?F(e):z(e)})})}function $(t){var e=[];return t.eachSeries(function(t){var n=t.getAllData();(0,r.each)(n,function(n){n.data;var r=n.type,i=t.getSelectedDataIndices();if(i.length>0){var o={dataIndex:i,seriesIndex:t.seriesIndex};null!=r&&(o.dataType=r),e.push(o)}})}),e}function K(t,e,n){var r,o,a,s;J(t,!0),I(t,O),r=t,o=e,a=n,s=(0,i.z)(r),null!=o?(s.focus=o,s.blurScope=a):s.focus&&(s.focus=null)}function Q(t,e,n,r){r?J(t,!1):K(t,e,n)}function J(t,e){var n=!1===e;t.highDownSilentOnTouch&&(t.__highDownSilentOnTouch=t.highDownSilentOnTouch),(!n||t.__highDownDispatcher)&&(t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!n)}function tt(t){return!!(t&&t.__highDownDispatcher)}function te(t){var e=l[t];return null==e&&u<=32&&(e=l[t]=u++),e}function tn(t){var e=t.type;return e===m||e===_||e===x}function tr(t){var e=t.type;return e===y||e===v}function ti(t){var e=c(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}},2403:(t,e,n)=>{"use strict";n.d(e,{A:()=>E});var r=n(643),i=n(4123),o=n(7170),a=n(6187),s=n(706),u=n(7156),l=n(2170),c=n(2261),h=/\{@(.+?)\}/g,f=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),r=this.getRawValue(t,e),i=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),u=s&&s[n.getItemVisual(t,"drawType")||"fill"],l=s&&s.stroke,c=this.mainType,h="series"===c,f=n.userOutput&&n.userOutput.get();return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:h?this.subType:null,seriesIndex:this.seriesIndex,seriesId:h?this.id:null,seriesName:h?this.name:null,name:o,dataIndex:i,data:a,dataType:e,value:r,color:u,borderColor:l,dimensionNames:f?f.fullDimensions:null,encode:f?f.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,r,o,a){e=e||"normal";var s=this.getData(n),u=this.getDataParams(t,n);return(a&&(u.value=a.interpolatedValue),null!=r&&i.isArray(u.value)&&(u.value=u.value[r]),o||(o=s.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"])),i.isFunction(o))?(u.status=e,u.dimensionIndex=r,o(u)):i.isString(o)?(0,c.YK)(o,u).replace(h,function(e,n){var r=n.length,o=n;"["===o.charAt(0)&&"]"===o.charAt(r-1)&&(o=+o.slice(1,r-1));var u=(0,l.QE)(s,t,o);if(a&&i.isArray(a.interpolatedValue)){var c=s.getDimensionIndex(o);c>=0&&(u=a.interpolatedValue[c])}return null!=u?u+"":""}):void 0},t.prototype.getRawValue=function(t,e){return(0,l.QE)(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}(),p=n(4524),d=n(9942),g=n(2660),y=n(8535),v=n(7439),m=n(8142),_=n(1798),x=n(2899),b=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,r=this._getUpstreamSourceManagers(),o=!!r.length;if(w(n)){var a=void 0,s=void 0,u=void 0;if(o){var l=r[0];l.prepareSource(),a=(u=l.getSource()).data,s=u.sourceFormat,e=[l._getVersionSign()]}else a=n.get("data",!0),s=(0,i.isTypedArray)(a)?v.XO:v.mK,e=[];var c=this._getSourceMetaRawOption()||{},h=u&&u.metaRawOption||{},f=(0,i.retrieve2)(c.seriesLayoutBy,h.seriesLayoutBy)||null,p=(0,i.retrieve2)(c.sourceHeader,h.sourceHeader),d=(0,i.retrieve2)(c.dimensions,h.dimensions);t=f!==h.seriesLayoutBy||!!p!=!!h.sourceHeader||d?[(0,y.gV)(a,{seriesLayoutBy:f,sourceHeader:p,dimensions:d},s)]:[]}else if(o){var g=this._applyTransform(r);t=g.sourceList,e=g.upstreamSignList}else{var m=n.get("source",!0);t=[(0,y.gV)(m,this._getSourceMetaRawOption(),null)],e=[]}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,r=n.get("transform",!0),o=n.get("fromTransformResult",!0);null!=o&&1!==t.length&&S("");var a=[],s=[];return(0,i.each)(t,function(t){t.prepareSource();var e=t.getSource(o||0);null==o||e||S(""),a.push(e),s.push(t._getVersionSign())}),r?e=(0,_.py)(r,a,{datasetIndex:n.componentIndex}):null!=o&&(e=[(0,y.BE)(a[0])]),{sourceList:e,upstreamSignList:s}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var r=this._storeList,i=r[0];i||(i=r[0]={});var o=i[n];if(!o){var a=this._getUpstreamSourceManagers()[0];w(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new x.Ay).initData(new l.d1(e,t.length),t),i[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(!w(t))return(0,i.map)((0,m.Gt)(t),function(t){return t.getSourceManager()});var e=(0,m.G9)(t);return e?[e.getSourceManager()]:[]},t.prototype._getSourceMetaRawOption=function(){var t,e,n,r=this._sourceHost;return w(r)?(t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function w(t){return"series"===t.mainType}function S(t){throw Error(t)}var M=n(3607);!function(){function t(){this.richTextStyles={},this._nextStyleNameId=(0,M.IH)()}t.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},t.prototype.makeTooltipMarker=function(t,e,n){var r="richText"===n?this._generateStyleName():null,o=(0,c.qg)({color:e,type:t,renderMode:n,markerId:r});return(0,i.isString)(o)?o:(this.richTextStyles[r]=o.style,o.content)},t.prototype.wrapRichTextStyle=function(t,e){var n={};(0,i.isArray)(e)?(0,i.each)(e,function(t){return(0,i.extend)(n,t)}):(0,i.extend)(n,e);var r=this._generateStyleName();return this.richTextStyles[r]=n,"{"+r+"|"+t+"}"}}();var A=a.$r();function T(t,e){return t.getName(e)||t.getId(e)}var k=function(t){var e;function n(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return(0,r.__extends)(n,t),n.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=(0,d.U)({count:I,reset:D}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(A(this).sourceManager=new b(this)).prepareSource();var r=this.getInitialData(t,n);P(r,this),this.dataTask.context.data=r,A(this).dataBeforeProcessed=r,C(this),this._initSelectedMapFromData(r)},n.prototype.mergeDefaultAndTheme=function(t,e){var n=(0,p.ad)(this),r=n?(0,p.vs)(t):{},o=this.subType;s.A.hasClass(o)&&(o+="Series"),i.merge(t,e.getTheme().get(this.subType)),i.merge(t,this.getDefaultOption()),a.M5(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&(0,p.YA)(t,r,n)},n.prototype.mergeOption=function(t,e){t=i.merge(this.option,t,!0),this.fillDataTextStyle(t.data);var n=(0,p.ad)(this);n&&(0,p.YA)(this.option,t,n);var r=A(this).sourceManager;r.dirty(),r.prepareSource();var o=this.getInitialData(t,e);P(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,A(this).dataBeforeProcessed=o,C(this),this._initSelectedMapFromData(o)},n.prototype.fillDataTextStyle=function(t){if(t&&!i.isTypedArray(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&a.M5(t[n],"label",e)},n.prototype.getInitialData=function(t,e){},n.prototype.appendData=function(t){this.getRawData().appendData(t.data)},n.prototype.getData=function(t){var e=R(this);if(!e)return A(this).data;var n=e.context.data;return null!=t&&n.getLinkedData?n.getLinkedData(t):n},n.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},n.prototype.setData=function(t){var e=R(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}A(this).data=t},n.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return i.createHashMap(t)},n.prototype.getSourceManager=function(){return A(this).sourceManager},n.prototype.getSource=function(){return this.getSourceManager().getSource()},n.prototype.getRawData=function(){return A(this).dataBeforeProcessed},n.prototype.getColorBy=function(){return this.get("colorBy")||"series"},n.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},n.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},n.prototype.formatTooltip=function(t,e,n){return function(t){var e,n,r,o,s,u,h,f=t.series,p=t.dataIndex,d=t.multipleSeries,g=f.getData(),y=g.mapDimensionsAll("defaultedTooltip"),v=y.length,m=f.getRawValue(p),_=(0,i.isArray)(m),x=(e=f.getData().getItemVisual(p,"style")[f.visualDrawType],(0,c.he)(e));if(v>1||_&&!v){var b=function(t,e,n,r,o){var a=e.getData(),s=(0,i.reduce)(t,function(t,e,n){var r=a.getDimensionInfo(n);return t||r&&!1!==r.tooltip&&null!=r.displayName},!1),u=[],c=[],h=[];function f(t,e){var n,r=a.getDimensionInfo(e);r&&!1!==r.otherDims.tooltip&&(s?h.push(((n={markerType:"subItem",markerColor:o,name:r.displayName,value:t,valueType:r.type}).type="nameValue",n)):(u.push(t),c.push(r.type)))}return r.length?(0,i.each)(r,function(t){f((0,l.QE)(a,n,t),t)}):(0,i.each)(t,f),{inlineValues:u,inlineValueTypes:c,blocks:h}}(m,f,p,y,x);o=b.inlineValues,s=b.inlineValueTypes,u=b.blocks,h=b.inlineValues[0]}else if(v){var w=g.getDimensionInfo(y[0]);h=o=(0,l.QE)(g,p,y[0]),s=w.type}else h=o=_?m[0]:m;var S=(0,a.O0)(f),M=S&&f.name||"",A=g.getName(p),T=d?M:A;return(r={header:M,noHeader:d||!S,sortParam:h,blocks:[((n={markerType:"item",markerColor:x,name:T,noName:!(0,i.trim)(T),value:o,valueType:s,dataIndex:p}).type="nameValue",n)].concat(u||[])}).type="section",r}({series:this,dataIndex:t,multipleSeries:e})},n.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(o.A.node&&!(t&&t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},n.prototype.restoreData=function(){this.dataTask.dirty()},n.prototype.getColorFromPalette=function(t,e,n){var r=this.ecModel,i=u.X.prototype.getColorFromPalette.call(this,t,e,n);return i||(i=r.getColorFromPalette(t,e,n)),i},n.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},n.prototype.getProgressive=function(){return this.get("progressive")},n.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},n.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},n.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var r=this.option.selectedMode,i=this.getData(e);if("series"===r||"all"===n){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var o=0;o<t.length;o++){var a=T(i,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},n.prototype.toggleSelect=function(t,e){for(var n=[],r=0;r<t.length;r++)n[0]=t[r],this.isSelected(t[r],e)?this.unselect(n,e):this.select(n,e)},n.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=i.keys(t),n=[],r=0;r<e.length;r++){var o=t[e[r]];o>=0&&n.push(o)}return n},n.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var r=this.getData(e);return("all"===n||n[T(r,t)])&&!r.getItemModel(t).get(["select","disabled"])},n.prototype.isUniversalTransitionEnabled=function(){if(this.__universalTransitionEnabled)return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},n.prototype._innerSelect=function(t,e){var n,r,o=this.option,a=o.selectedMode,s=e.length;if(a&&s){if("series"===a)o.selectedMap="all";else if("multiple"===a){i.isObject(o.selectedMap)||(o.selectedMap={});for(var u=o.selectedMap,l=0;l<s;l++){var c=e[l],h=T(t,c);u[h]=!0,this._selectedDataIndicesMap[h]=t.getRawIndex(c)}}else if("single"===a||!0===a){var f=e[s-1],h=T(t,f);(n={})[h]=!0,o.selectedMap=n,this._selectedDataIndicesMap=((r={})[h]=t.getRawIndex(f),r)}}},n.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each(function(n){var r=t.getRawDataItem(n);r&&r.selected&&e.push(n)}),e.length>0&&this._innerSelect(t,e)}},n.registerClass=function(t){return s.A.registerClass(t)},n.protoInitialize=void((e=n.prototype).type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"),n}(s.A);function C(t){var e,n,r,o=t.name;a.O0(t)||(t.name=(n=(e=t.getRawData()).mapDimensionsAll("seriesName"),r=[],i.each(n,function(t){var n=e.getDimensionInfo(t);n.displayName&&r.push(n.displayName)}),r.join(" ")||o))}function I(t){return t.model.getRawData().count()}function D(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),O}function O(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function P(t,e){i.each(i.concatArray(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),function(n){t.wrapMethod(n,i.curry(L,e))})}function L(t,e){var n=R(t);return n&&n.setOutputEnd((e||this).count()),e}function R(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var r=n.currentTask;if(r){var i=r.agentStubMap;i&&(r=i.get(t.uid))}return r}}i.mixin(k,f),i.mixin(k,u.X),(0,g.q7)(k,s.A);let E=k},2456:(t,e,n)=>{"use strict";var r=n(643),i=(0,r.__importStar)(n(2115)),o=n(8118),a=n(5733),s=n(8947),u=n(9254),l=n(3949);e.A=function(t){function e(e){var n=t.call(this,e)||this;return n.echarts=e.echarts,n.ele=null,n.isInitialResize=!0,n}return(0,r.__extends)(e,t),e.prototype.componentDidMount=function(){this.renderNewEcharts()},e.prototype.componentDidUpdate=function(t){var e=this.props.shouldSetOption;if(!(0,s.isFunction)(e)||e(t,this.props)){if(!(0,l.isEqual)(t.theme,this.props.theme)||!(0,l.isEqual)(t.opts,this.props.opts)||!(0,l.isEqual)(t.onEvents,this.props.onEvents)){this.dispose(),this.renderNewEcharts();return}var n=["option","notMerge","lazyUpdate","showLoading","loadingOption"];(0,l.isEqual)((0,a.pick)(this.props,n),(0,a.pick)(t,n))||this.updateEChartsOption(),(0,l.isEqual)(t.style,this.props.style)&&(0,l.isEqual)(t.className,this.props.className)||this.resize()}},e.prototype.componentWillUnmount=function(){this.dispose()},e.prototype.getEchartsInstance=function(){return this.echarts.getInstanceByDom(this.ele)||this.echarts.init(this.ele,this.props.theme,this.props.opts)},e.prototype.dispose=function(){if(this.ele){try{(0,o.clear)(this.ele)}catch(t){console.warn(t)}this.echarts.dispose(this.ele)}},e.prototype.renderNewEcharts=function(){var t=this,e=this.props,n=e.onEvents,r=e.onChartReady,i=this.updateEChartsOption();this.bindEvents(i,n||{}),(0,s.isFunction)(r)&&r(i),this.ele&&(0,o.bind)(this.ele,function(){t.resize()})},e.prototype.bindEvents=function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&function(e,n){(0,u.isString)(e)&&(0,s.isFunction)(n)&&t.on(e,function(e){n(e,t)})}(n,e[n])},e.prototype.updateEChartsOption=function(){var t=this.props,e=t.option,n=t.notMerge,r=t.lazyUpdate,i=t.showLoading,o=t.loadingOption,a=this.getEchartsInstance();return a.setOption(e,void 0!==n&&n,void 0!==r&&r),i?a.showLoading(void 0===o?null:o):a.hideLoading(),a},e.prototype.resize=function(){var t=this.getEchartsInstance();if(!this.isInitialResize)try{t.resize()}catch(t){console.warn(t)}this.isInitialResize=!1},e.prototype.render=function(){var t=this,e=this.props,n=e.style,o=e.className,a=(0,r.__assign)({height:300},n);return i.default.createElement("div",{ref:function(e){t.ele=e},style:a,className:"echarts-for-react "+(void 0===o?"":o)})},e}(i.PureComponent)},2660:(t,e,n)=>{"use strict";n.d(e,{CC:()=>s,Od:()=>f,_E:()=>u,gq:()=>l,q7:()=>c,tQ:()=>g});var r=n(643),i=n(4123),o="___EC__COMPONENT__CONTAINER___",a="___EC__EXTENDED_CLASS___";function s(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function u(t){return!!(t&&t[a])}function l(t,e){t.$constructor=t,t.extend=function(t){var e,n,o=this;return(e=o,i.isFunction(e)&&/^class\s/.test(Function.prototype.toString.call(e)))?n=function(t){function e(){return t.apply(this,arguments)||this}return(0,r.__extends)(e,t),e}(o):(n=function(){(t.$constructor||o).apply(this,arguments)},i.inherits(n,this)),i.extend(n.prototype,t),n[a]=!0,n.extend=this.extend,n.superCall=p,n.superApply=d,n.superClass=o,n}}function c(t,e){t.extend=e.extend}var h=Math.round(10*Math.random());function f(t){var e=["__\0is_clz",h++].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!!(t&&t[e])}}function p(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return this.superClass.prototype[e].apply(t,n)}function d(t,e,n){return this.superClass.prototype[e].apply(t,n)}function g(t){var e={};t.registerClass=function(t){var n=t.type||t.prototype.type;if(n){i.assert(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n),'componentType "'+n+'" illegal'),t.prototype.type=n;var r,a,u=s(n);u.sub?u.sub!==o&&(((a=e[(r=u).main])&&a[o]||((a=e[r.main]={})[o]=!0),a)[u.sub]=t):e[u.main]=t}return t},t.getClass=function(t,n,r){var i=e[t];if(i&&i[o]&&(i=n?i[n]:null),r&&!i)throw Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var n=s(t),r=[],a=e[n.main];return a&&a[o]?i.each(a,function(t,e){e!==o&&r.push(t)}):r.push(a),r},t.hasClass=function(t){return!!e[s(t).main]},t.getAllClassMainTypes=function(){var t=[];return i.each(e,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){var n=e[s(t).main];return n&&n[o]}}},2663:(t,e,n)=>{"use strict";n.d(e,{a:()=>i,z:()=>r});var r=(0,n(6187).$r)(),i=function(t,e,n,i){if(i){var o=r(i);o.dataIndex=n,o.dataType=e,o.seriesIndex=t,o.ssrType="chart","group"===i.type&&i.traverse(function(i){var o=r(i);o.seriesIndex=t,o.dataIndex=n,o.dataType=e,o.ssrType="chart"})}}},2858:(t,e,n)=>{"use strict";n.d(e,{a:()=>tA});var r=n(4566),i=n(4271),o=n(3493),a=n(6847),s=n(6436),u=Math.sin,l=Math.cos,c=Math.PI,h=2*Math.PI,f=180/c,p=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add("M",t,e)},t.prototype.lineTo=function(t,e){this._add("L",t,e)},t.prototype.bezierCurveTo=function(t,e,n,r,i,o){this._add("C",t,e,n,r,i,o)},t.prototype.quadraticCurveTo=function(t,e,n,r){this._add("Q",t,e,n,r)},t.prototype.arc=function(t,e,n,r,i,o){this.ellipse(t,e,n,n,0,r,i,o)},t.prototype.ellipse=function(t,e,n,i,o,a,s,p){var d=s-a,g=!p,y=Math.abs(d),v=(0,r.Cv)(y-h)||(g?d>=h:-d>=h),m=d>0?d%h:d%h+h,_=!1;_=!!v||!(0,r.Cv)(y)&&m>=c==!!g;var x=t+n*l(a),b=e+i*u(a);this._start&&this._add("M",x,b);var w=Math.round(o*f);if(v){var S=1/this._p,M=(g?1:-1)*(h-S);this._add("A",n,i,w,1,+g,t+n*l(a+M),e+i*u(a+M)),S>.01&&this._add("A",n,i,w,0,+g,x,b)}else{var A=t+n*l(s),T=e+i*u(s);this._add("A",n,i,w,+_,+g,A,T)}},t.prototype.rect=function(t,e,n,r){this._add("M",t,e),this._add("l",n,0),this._add("l",0,r),this._add("l",-n,0),this._add("Z")},t.prototype.closePath=function(){this._d.length>0&&this._add("Z")},t.prototype._add=function(t,e,n,r,i,o,a,s,u){for(var l=[],c=this._p,h=1;h<arguments.length;h++){var f=arguments[h];if(isNaN(f)){this._invalid=!0;return}l.push(Math.round(f*c)/c)}this._d.push(t+l.join(" ")),this._start="Z"===t},t.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},t.prototype.getStr=function(){return this._str},t}(),d=n(3219),g=n(4123),y="none",v=Math.round,m=["lineCap","miterLimit","lineJoin"],_=(0,g.map)(m,function(t){return"stroke-"+t.toLowerCase()}),x=n(4958),b="http://www.w3.org/2000/svg",w="http://www.w3.org/1999/xlink",S="ecmeta_";function M(t){return document.createElementNS(b,t)}function A(t,e,n,r,i){return{tag:t,attrs:n||{},children:r,text:i,key:e}}function T(t,e){var n=(e=e||{}).newline?"\n":"";return function t(e){var r=e.children,i=e.tag,o=e.attrs,a=e.text;return function(t,e){var n=[];if(e)for(var r in e){var i=e[r],o=r;!1!==i&&(!0!==i&&null!=i&&(o+='="'+i+'"'),n.push(o))}return"<"+t+" "+n.join(" ")+">"}(i,o)+("style"!==i?(0,x.Me)(a):a||"")+(r?""+n+(0,g.map)(r,function(e){return t(e)}).join(n)+n:"")+"</"+i+">"}(t)}function k(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function C(t,e,n,r){return A("svg","root",{width:t,height:e,xmlns:b,"xmlns:xlink":w,version:"1.1",baseProfile:"full",viewBox:!!r&&"0 0 "+t+" "+e},n)}var I=n(9662),D=n(4621),O=n(8248),P=n(1294),L=n(65),R=0,E={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},N="transform-origin",B={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function F(t,e){var n=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[n]=t,n}function z(t){return(0,g.isString)(t)?E[t]?"cubic-bezier("+E[t]+")":(0,L.w)(t)?t:"":""}function V(t,e,n,i){var o=t.animators,a=o.length,s=[];if(t instanceof P.A){var u=function(t,e,n){var r,i,o=t.shape.paths,a={};if((0,g.each)(o,function(t){var e=k(n.zrId);e.animation=!0,V(t,{},e,!0);var o=e.cssAnims,s=e.cssNodes,u=(0,g.keys)(o),l=u.length;if(l){var c=o[i=u[l-1]];for(var h in c){var f=c[h];a[h]=a[h]||{d:""},a[h].d+=f.d||""}for(var p in s){var d=s[p].animation;d.indexOf(i)>=0&&(r=d)}}}),r){e.d=!1;var s=F(a,n);return r.replace(i,s)}}(t,e,n);if(u)s.push(u);else if(!a)return}else if(!a)return;for(var l={},c=0;c<a;c++){var h=o[c],f=[h.getMaxTime()/1e3+"s"],d=z(h.getClip().easing),y=h.getDelay();d?f.push(d):f.push("linear"),y&&f.push(y/1e3+"s"),h.getLoop()&&f.push("infinite");var v=f.join(" ");l[v]=l[v]||[v,[]],l[v][1].push(h)}for(var m in l){var u=function(o){var a,s,u=o[1],l=u.length,c={},h={},f={},d="animation-timing-function";function y(t,e,n){for(var r=t.getTracks(),i=t.getMaxTime(),o=0;o<r.length;o++){var a=r[o];if(a.needsAnimate()){var s=a.keyframes,u=a.propName;if(n&&(u=n(u)),u)for(var l=0;l<s.length;l++){var c=s[l],h=Math.round(c.time/i*100)+"%",f=z(c.easing),p=c.rawValue;((0,g.isString)(p)||(0,g.isNumber)(p))&&(e[h]=e[h]||{},e[h][u]=c.rawValue,f&&(e[h][d]=f))}}}}for(var v=0;v<l;v++){var m=u[v],_=m.targetName;_?"shape"===_&&y(m,h):i||y(m,c)}for(var x in c){var b={};(0,D.IT)(b,t),(0,g.extend)(b,c[x]);var w=(0,r.Z1)(b),S=c[x][d];f[x]=w?{transform:w}:{},function(t,e){var n=e.originX,r=e.originY;(n||r)&&(t[N]=n+"px "+r+"px")}(f[x],b),S&&(f[x][d]=S)}var M=!0;for(var x in h){f[x]=f[x]||{};var A=!a,S=h[x][d];A&&(a=new O.A);var T=a.len();a.reset(),f[x].d=function(t,e,n){var i=(0,g.extend)({},t.shape);(0,g.extend)(i,e),t.buildPath(n,i);var o=new p;return o.reset((0,r.MD)(t)),n.rebuildPath(o,1),o.generateStr(),o.getStr()}(t,h[x],a);var k=a.len();if(!A&&T!==k){M=!1;break}S&&(f[x][d]=S)}if(!M)for(var x in f)delete f[x].d;if(!i)for(var v=0;v<l;v++){var m=u[v],_=m.targetName;"style"===_&&y(m,f,function(t){return B[t]})}for(var C=(0,g.keys)(f),I=!0,v=1;v<C.length;v++){var P=C[v-1],L=C[v];if(f[P][N]!==f[L][N]){I=!1;break}s=f[P][N]}if(I&&s){for(var x in f)f[x][N]&&delete f[x][N];e[N]=s}if((0,g.filter)(C,function(t){return(0,g.keys)(f[t]).length>0}).length)return F(f,n)+" "+o[0]+" both"}(l[m]);u&&s.push(u)}if(s.length){var _=n.zrId+"-cls-"+R++;n.cssNodes["."+_]={animation:s.join(",")},e.class=_}}var H=n(3875),j=n(2316),U=n(411);function W(t,e,n,r){var i=JSON.stringify(t),o=n.cssStyleCache[i];o||(o=n.zrId+"-cls-"+R++,n.cssStyleCache[i]=o,n.cssNodes["."+o+(r?":hover":"")]=t),e.class=e.class?e.class+" "+o:o}var G=n(7172),q=Math.round;function X(t){return t&&(0,g.isString)(t.src)}function Y(t){return t&&(0,g.isFunction)(t.toDataURL)}function Z(t,e,n,a){!function(t,e,n,a){var s,u,l=null==e.opacity?1:e.opacity;if(n instanceof o.Ay)return t("opacity",l);if(null!=(s=e.fill)&&s!==y){var c=(0,r.$2)(e.fill);t("fill",c.color);var h=null!=e.fillOpacity?e.fillOpacity*c.opacity*l:c.opacity*l;(a||h<1)&&t("fill-opacity",h)}else t("fill",y);if(null!=(u=e.stroke)&&u!==y){var f=(0,r.$2)(e.stroke);t("stroke",f.color);var p=e.strokeNoScale?n.getLineScale():1,g=p?(e.lineWidth||0)/p:0,x=null!=e.strokeOpacity?e.strokeOpacity*f.opacity*l:f.opacity*l,b=e.strokeFirst;if((a||1!==g)&&t("stroke-width",g),(a||b)&&t("paint-order",b?"stroke":"fill"),(a||x<1)&&t("stroke-opacity",x),e.lineDash){var w=(0,d.V)(n),S=w[0],M=w[1];S&&(M=v(M||0),t("stroke-dasharray",S.join(",")),(M||a)&&t("stroke-dashoffset",M))}else a&&t("stroke-dasharray",y);for(var A=0;A<m.length;A++){var T=m[A];if(a||e[T]!==i.MW[T]){var k=e[T]||i.MW[T];k&&t(_[A],k)}}}else a&&t("stroke",y)}(function(i,o){var s="fill"===i||"stroke"===i;s&&(0,r.bn)(o)?ti(e,t,i,a):s&&(0,r.Pt)(o)?to(n,t,i,a):t[i]=o,s&&a.ssr&&"none"===o&&(t["pointer-events"]="visible")},e,n,!1),function(t,e,n){var i=t.style;if((0,r.dX)(i)){var o=(0,r.si)(t),a=n.shadowCache,s=a[o];if(!s){var u=t.getGlobalScale(),l=u[0],c=u[1];if(!l||!c)return;var h=i.shadowOffsetX||0,f=i.shadowOffsetY||0,p=i.shadowBlur,d=(0,r.$2)(i.shadowColor),g=d.opacity,y=d.color;s=n.zrId+"-s"+n.shadowIdx++,n.defs[s]=A("filter",s,{id:s,x:"-100%",y:"-100%",width:"300%",height:"300%"},[A("feDropShadow","",{dx:h/l,dy:f/c,stdDeviation:p/2/l+" "+p/2/c,"flood-color":y,"flood-opacity":g})]),a[o]=s}e.filter=(0,r.Xu)(s)}}(n,t,a)}function $(t,e){var n=(0,G.getElementSSRData)(e);n&&(n.each(function(e,n){null!=e&&(t[(S+n).toLowerCase()]=e+"")}),e.isSilent()&&(t[S+"silent"]="true"))}function K(t){return(0,r.Cv)(t[0]-1)&&(0,r.Cv)(t[1])&&(0,r.Cv)(t[2])&&(0,r.Cv)(t[3]-1)}function Q(t,e,n){if(e&&!((0,r.Cv)(e[4])&&(0,r.Cv)(e[5])&&K(e))){var i=n?10:1e4;t.transform=K(e)?"translate("+q(e[4]*i)/i+" "+q(e[5]*i)/i+")":(0,r.nV)(e)}}function J(t,e,n){for(var r=t.points,i=[],o=0;o<r.length;o++)i.push(q(r[o][0]*n)/n),i.push(q(r[o][1]*n)/n);e.points=i.join(" ")}function tt(t){return!t.smooth}var te={circle:[function(t){var e=(0,g.map)(t,function(t){return"string"==typeof t?[t,t]:t});return function(t,n,r){for(var i=0;i<e.length;i++){var o=e[i],a=t[o[0]];null!=a&&(n[o[1]]=q(a*r)/r)}}}(["cx","cy","r"])],polyline:[J,tt],polygon:[J,tt]};function tn(t,e){var n=t.style,i=t.shape,o=te[t.type],a={},s=e.animation,u="path",l=t.style.strokePercent,c=e.compress&&(0,r.MD)(t)||4;if(!o||e.willUpdate||o[1]&&!o[1](i)||s&&function(t){for(var e=t.animators,n=0;n<e.length;n++)if("shape"===e[n].targetName)return!0;return!1}(t)||l<1){var h=!t.path||t.shapeChanged();t.path||t.createPathProxy();var f=t.path;h&&(f.beginPath(),t.buildPath(f,t.shape),t.pathUpdated());var d=f.getVersion(),g=t.__svgPathBuilder;t.__svgPathVersion===d&&g&&l===t.__svgPathStrokePercent||(g||(g=t.__svgPathBuilder=new p),g.reset(c),f.rebuildPath(g,l),g.generateStr(),t.__svgPathVersion=d,t.__svgPathStrokePercent=l),a.d=g.getStr()}else{u=t.type;var y=Math.pow(10,c);o[0](i,a,y)}return Q(a,t.transform),Z(a,n,t,e),$(a,t),e.animation&&V(t,a,e),e.emphasis&&function(t,e,n){if(!t.ignore)if(t.isSilent()){var r={"pointer-events":"none"};W(r,e,n,!0)}else{var i=t.states.emphasis&&t.states.emphasis.style?t.states.emphasis.style:{},o=i.fill;if(!o){var a=t.style&&t.style.fill,s=t.states.select&&t.states.select.style&&t.states.select.style.fill,u=t.currentStates.indexOf("select")>=0&&s||a;u&&(o=(0,U.liftColor)(u))}var l=i.lineWidth;l&&(l/=!i.strokeNoScale&&t.transform?t.transform[0]:1);var r={cursor:"pointer"};o&&(r.fill=o),i.stroke&&(r.stroke=i.stroke),l&&(r["stroke-width"]=l),W(r,e,n,!0)}}(t,a,e),A(u,t.id+"",a)}function tr(t,e){return t instanceof i.Ay?tn(t,e):t instanceof o.Ay?function(t,e){var n=t.style,r=n.image;if(r&&!(0,g.isString)(r)&&(X(r)?r=r.src:Y(r)&&(r=r.toDataURL())),r){var i=n.x||0,o=n.y||0,a={href:r,width:n.width,height:n.height};return i&&(a.x=i),o&&(a.y=o),Q(a,t.transform),Z(a,n,t,e),$(a,t),e.animation&&V(t,a,e),A("image",t.id+"",a)}}(t,e):t instanceof s.A?function(t,e){var n=t.style,i=n.text;if(null!=i&&(i+=""),!(!i||isNaN(n.x)||isNaN(n.y))){var o=n.font||j.OH,s=n.x||0,u=(0,r.sZ)(n.y||0,(0,a.ks)(o),n.textBaseline),l={"dominant-baseline":"central","text-anchor":r.eQ[n.textAlign]||n.textAlign};if((0,H.XE)(n)){var c="",h=n.fontStyle,f=(0,H.I5)(n.fontSize);if(!parseFloat(f))return;var p=n.fontFamily||j.zs,d=n.fontWeight;c+="font-size:"+f+";font-family:"+p+";",h&&"normal"!==h&&(c+="font-style:"+h+";"),d&&"normal"!==d&&(c+="font-weight:"+d+";"),l.style=c}else l.style="font: "+o;return i.match(/\s/)&&(l["xml:space"]="preserve"),s&&(l.x=s),u&&(l.y=u),Q(l,t.transform),Z(l,n,t,e),$(l,t),e.animation&&V(t,l,e),A("text",t.id+"",l,void 0,i)}}(t,e):void 0}function ti(t,e,n,i){var o,a=t[n],s={gradientUnits:a.global?"userSpaceOnUse":"objectBoundingBox"};if((0,r.OS)(a))o="linearGradient",s.x1=a.x,s.y1=a.y,s.x2=a.x2,s.y2=a.y2;else{if(!(0,r.OH)(a))return;o="radialGradient",s.cx=(0,g.retrieve2)(a.x,.5),s.cy=(0,g.retrieve2)(a.y,.5),s.r=(0,g.retrieve2)(a.r,.5)}for(var u=a.colorStops,l=[],c=0,h=u.length;c<h;++c){var f=100*(0,r.XP)(u[c].offset)+"%",p=u[c].color,d=(0,r.$2)(p),y=d.color,v=d.opacity,m={offset:f};m["stop-color"]=y,v<1&&(m["stop-opacity"]=v),l.push(A("stop",c+"",m))}var _=T(A(o,"",s,l)),x=i.gradientCache,b=x[_];b||(b=i.zrId+"-g"+i.gradientIdx++,x[_]=b,s.id=b,i.defs[b]=A(o,b,s,l)),e[n]=(0,r.Xu)(b)}function to(t,e,n,i){var o,a,s,u=t.style[n],l=t.getBoundingRect(),c={},h=u.repeat,f="no-repeat"===h,p="repeat-x"===h,d="repeat-y"===h;if((0,r.sL)(u)){var y=u.imageWidth,v=u.imageHeight,m=void 0,_=u.image;if((0,g.isString)(_)?m=_:X(_)?m=_.src:Y(_)&&(m=_.toDataURL()),"undefined"==typeof Image){var x="Image width/height must been given explictly in svg-ssr renderer.";(0,g.assert)(y,x),(0,g.assert)(v,x)}else if(null==y||null==v){var b=function(t,e){if(t){var n=t.elm,r=y||e.width,i=v||e.height;"pattern"===t.tag&&(p?(i=1,r/=l.width):d&&(r=1,i/=l.height)),t.attrs.width=r,t.attrs.height=i,n&&(n.setAttribute("width",r),n.setAttribute("height",i))}},w=(0,I.OD)(m,null,t,function(t){f||b(M,t),b(o,t)});w&&w.width&&w.height&&(y=y||w.width,v=v||w.height)}o=A("image","img",{href:m,width:y,height:v}),c.width=y,c.height=v}else u.svgElement&&(o=(0,g.clone)(u.svgElement),c.width=u.svgWidth,c.height=u.svgHeight);if(o){f?a=s=1:p?(s=1,a=c.width/l.width):d?(a=1,s=c.height/l.height):c.patternUnits="userSpaceOnUse",null==a||isNaN(a)||(c.width=a),null==s||isNaN(s)||(c.height=s);var S=(0,r.Z1)(u);S&&(c.patternTransform=S);var M=A("pattern","",c,[o]),k=T(M),C=i.patternCache,D=C[k];D||(D=i.zrId+"-p"+i.patternIdx++,C[k]=D,c.id=D,M=i.defs[D]=A("pattern",D,c,[o])),e[n]=(0,r.Xu)(D)}}function ta(t){return document.createTextNode(t)}function ts(t,e,n){t.insertBefore(e,n)}function tu(t,e){t.removeChild(e)}function tl(t,e){t.appendChild(e)}function tc(t){return t.parentNode}function th(t){return t.nextSibling}function tf(t,e){t.textContent=e}var tp=A("","");function td(t){return void 0===t}function tg(t){return void 0!==t}function ty(t,e){var n=t.key===e.key;return t.tag===e.tag&&n}function tv(t){var e,n=t.children,r=t.tag;if(tg(r)){var i=t.elm=M(r);if(tx(tp,t),(0,g.isArray)(n))for(e=0;e<n.length;++e){var o=n[e];null!=o&&tl(i,tv(o))}else tg(t.text)&&!(0,g.isObject)(t.text)&&tl(i,ta(t.text))}else t.elm=ta(t.text);return t.elm}function tm(t,e,n,r,i){for(;r<=i;++r){var o=n[r];null!=o&&ts(t,tv(o),e)}}function t_(t,e,n,r){for(;n<=r;++n){var i=e[n];null!=i&&(tg(i.tag)?tu(tc(i.elm),i.elm):tu(t,i.elm))}}function tx(t,e){var n,r=e.elm,i=t&&t.attrs||{},o=e.attrs||{};if(i!==o){for(n in o){var a=o[n];i[n]!==a&&(!0===a?r.setAttribute(n,""):!1===a?r.removeAttribute(n):"style"===n?r.style.cssText=a:120!==n.charCodeAt(0)?r.setAttribute(n,a):"xmlns:xlink"===n||"xmlns"===n?r.setAttributeNS("http://www.w3.org/2000/xmlns/",n,a):58===n.charCodeAt(3)?r.setAttributeNS("http://www.w3.org/XML/1998/namespace",n,a):58===n.charCodeAt(5)?r.setAttributeNS(w,n,a):r.setAttribute(n,a))}for(n in i)n in o||r.removeAttribute(n)}}var tb=n(3030),tw=0,tS=function(){function t(t,e,n){if(this.type="svg",this.refreshHover=tM("refreshHover"),this.configLayer=tM("configLayer"),this.storage=e,this._opts=n=(0,g.extend)({},n),this.root=t,this._id="zr"+tw++,this._oldVNode=C(n.width,n.height),t&&!n.ssr){var r=this._viewport=document.createElement("div");r.style.cssText="position:relative;overflow:hidden";var i=this._svgDom=this._oldVNode.elm=M("svg");tx(null,this._oldVNode),r.appendChild(i),t.appendChild(r)}this.resize(n.width,n.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",function(t,e){if(ty(t,e))!function t(e,n){var r=n.elm=e.elm,i=e.children,o=n.children;e!==n&&(tx(e,n),td(n.text)?tg(i)&&tg(o)?i!==o&&function(e,n,r){for(var i,o,a,s=0,u=0,l=n.length-1,c=n[0],h=n[l],f=r.length-1,p=r[0],d=r[f];s<=l&&u<=f;)null==c?c=n[++s]:null==h?h=n[--l]:null==p?p=r[++u]:null==d?d=r[--f]:ty(c,p)?(t(c,p),c=n[++s],p=r[++u]):ty(h,d)?(t(h,d),h=n[--l],d=r[--f]):ty(c,d)?(t(c,d),ts(e,c.elm,th(h.elm)),c=n[++s],d=r[--f]):(ty(h,p)?(t(h,p),ts(e,h.elm,c.elm),h=n[--l]):(td(i)&&(i=function(t,e,n){for(var r={},i=e;i<=n;++i){var o=t[i].key;void 0!==o&&(r[o]=i)}return r}(n,s,l)),td(o=i[p.key])||(a=n[o]).tag!==p.tag?ts(e,tv(p),c.elm):(t(a,p),n[o]=void 0,ts(e,a.elm,c.elm))),p=r[++u]);(s<=l||u<=f)&&(s>l?tm(e,null==r[f+1]?null:r[f+1].elm,r,u,f):t_(e,n,s,l))}(r,i,o):tg(o)?(tg(e.text)&&tf(r,""),tm(r,null,o,0,o.length-1)):tg(i)?t_(r,i,0,i.length-1):tg(e.text)&&tf(r,""):e.text!==n.text&&(tg(i)&&t_(r,i,0,i.length-1),tf(r,n.text)))}(t,e);else{var n=t.elm,r=tc(n);tv(e),null!==r&&(ts(r,e.elm,th(n)),t_(r,[t],0,0))}}(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return tr(t,k(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),n=this._width,i=this._height,o=k(this._id);o.animation=t.animation,o.willUpdate=t.willUpdate,o.compress=t.compress,o.emphasis=t.emphasis,o.ssr=this._opts.ssr;var a=[],s=this._bgVNode=function(t,e,n,i){var o;if(n&&"none"!==n)if(o=A("rect","bg",{width:t,height:e,x:"0",y:"0"}),(0,r.bn)(n))ti({fill:n},o.attrs,"fill",i);else if((0,r.Pt)(n))to({style:{fill:n},dirty:g.noop,getBoundingRect:function(){return{width:t,height:e}}},o.attrs,"fill",i);else{var a=(0,r.$2)(n),s=a.color,u=a.opacity;o.attrs.fill=s,u<1&&(o.attrs["fill-opacity"]=u)}return o}(n,i,this._backgroundColor,o);s&&a.push(s);var u=t.compress?null:this._mainVNode=A("g","main",{},[]);this._paintList(e,o,u?u.children:a),u&&a.push(u);var l=(0,g.map)((0,g.keys)(o.defs),function(t){return o.defs[t]});if(l.length&&a.push(A("defs","defs",{},l)),t.animation){var c,h,f,p,d,y,v,m,_=(c=o.cssNodes,h=o.cssAnims,d=" {"+(p=(f={newline:!0}).newline?"\n":""),y=p+"}",v=(0,g.map)((0,g.keys)(c),function(t){return t+d+(0,g.map)((0,g.keys)(c[t]),function(e){return e+":"+c[t][e]+";"}).join(p)+y}).join(p),m=(0,g.map)((0,g.keys)(h),function(t){return"@keyframes "+t+d+(0,g.map)((0,g.keys)(h[t]),function(e){return e+d+(0,g.map)((0,g.keys)(h[t][e]),function(n){var r=h[t][e][n];return"d"===n&&(r='path("'+r+'")'),n+":"+r+";"}).join(p)+y}).join(p)+y}).join(p),v||m?["<![CDATA[",v,m,"]]>"].join(p):"");if(_){var x=A("style","stl",{},[],_);a.push(x)}}return C(n,i,a,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},T(this.renderToVNode({animation:(0,g.retrieve2)(t.cssAnimation,!0),emphasis:(0,g.retrieve2)(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:(0,g.retrieve2)(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,n){for(var i,o,a=t.length,s=[],u=0,l=0,c=0;c<a;c++){var h=t[c];if(!h.invisible){var f=h.__clipPaths,p=f&&f.length||0,d=o&&o.length||0,g=void 0;for(g=Math.max(p-1,d-1);g>=0&&(!f||!o||f[g]!==o[g]);g--);for(var y=d-1;y>g;y--)i=s[--u-1];for(var v=g+1;v<p;v++){var m={};!function(t,e,n){var i=n.clipPathCache,o=n.defs,a=i[t.id];if(!a){var s={id:a=n.zrId+"-c"+n.clipPathIdx++};i[t.id]=a,o[a]=A("clipPath",a,s,[tn(t,n)])}e["clip-path"]=(0,r.Xu)(a)}(f[v],m,e);var _=A("g","clip-g-"+l++,m,[]);(i?i.children:n).push(_),s[u++]=_,i=_}o=f;var x=tr(h,e);x&&(i?i.children:n).push(x)}}},t.prototype.resize=function(t,e){var n=this._opts,i=this.root,o=this._viewport;if(null!=t&&(n.width=t),null!=e&&(n.height=e),i&&o&&(o.style.display="none",t=(0,tb.YC)(i,0,n),e=(0,tb.YC)(i,1,n),o.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,o){var a=o.style;a.width=t+"px",a.height=e+"px"}if((0,r.Pt)(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",t),s.setAttribute("height",e));var u=this._bgVNode&&this._bgVNode.elm;u&&(u.setAttribute("width",t),u.setAttribute("height",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),n="data:image/svg+xml;";return t?(e=(0,r.WG)(e))&&n+"base64,"+e:n+"charset=UTF-8,"+encodeURIComponent(e)},t}();function tM(t){return function(){}}function tA(t){t.registerPainter("svg",tS)}},2899:(t,e,n)=>{"use strict";n.d(e,{A_:()=>c,Ay:()=>y});var r,i=n(4123),o=n(3424),a=n(8535),s="undefined",u=typeof Uint32Array===s?Array:Uint32Array,l=typeof Uint16Array===s?Array:Uint16Array,c=typeof Int32Array===s?Array:Int32Array,h=typeof Float64Array===s?Array:Float64Array,f={float:h,int:c,ordinal:Array,number:Array,time:h};function p(t){return t>65535?u:l}function d(){return[1/0,-1/0]}function g(t,e,n,r,i){var o=f[n||"float"];if(i){var a=t[e],s=a&&a.length;if(s!==r){for(var u=new o(r),l=0;l<s;l++)u[l]=a[l];t[e]=u}}else t[e]=new o(r)}let y=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=(0,i.createHashMap)()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var o=t.getSource(),s=this.defaultDimValueGetter=r[o.sourceFormat];this._dimValueGetter=n||s,this._rawExtent=[],(0,a.O0)(o),this._dimensions=(0,i.map)(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,r=this._dimensions,i=n.get(t);if(null!=i){if(r[i].type===e)return i}else i=r.length;return r[i]={type:e},n.set(t,i),this._chunks[i]=new f[e||"float"](this._rawCount),this._rawExtent[i]=d(),i},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],r=this._dimensions[t],i=this._rawExtent,o=r.ordinalOffset||0,a=n.length;0===o&&(i[t]=d());for(var s=i[t],u=o;u<a;u++){var l=n[u]=e.parseAndCollect(n[u]);isNaN(l)||(s[0]=Math.min(l,s[0]),s[1]=Math.max(l,s[1]))}r.ordinalMeta=e,r.ordinalOffset=a,r.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var r=e.count();return e.persistent||(r+=n),n<r&&this._initDataFromProvider(n,r,!0),[n,r]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,o=i.length,a=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<o;l++){var c=i[l];g(n,l,c.type,u,!0)}for(var h=[],f=s;f<u;f++)for(var p=f-s,d=0;d<o;d++){var c=i[d],y=r.arrayRows.call(this,t[p]||h,c.property,p,d);n[d][f]=y;var v=a[d];y<v[0]&&(v[0]=y),y>v[1]&&(v[1]=y)}return this._rawCount=this._count=u,{start:s,end:u}},t.prototype._initDataFromProvider=function(t,e,n){for(var r=this._provider,o=this._chunks,a=this._dimensions,s=a.length,u=this._rawExtent,l=(0,i.map)(a,function(t){return t.property}),c=0;c<s;c++){var h=a[c];u[c]||(u[c]=d()),g(o,c,h.type,e,n)}if(r.fillStorage)r.fillStorage(t,e,o,u);else for(var f=[],p=t;p<e;p++){f=r.getItem(p,f);for(var y=0;y<s;y++){var v=o[y],m=this._dimValueGetter(f,l[y],p,y);v[p]=m;var _=u[y];m<_[0]&&(_[0]=m),m>_[1]&&(_[1]=m)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],r=[];if(null==e){e=t,t=[];for(var i=0;i<this._dimensions.length;i++)r.push(i)}else r=t;for(var i=0,o=r.length;i<o;i++)n.push(this.get(r[i],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=this._chunks[t],n=0;if(e)for(var r=0,i=this.count();r<i;r++){var o=this.get(t,r);isNaN(o)||(n+=o)}return n},t.prototype.getMedian=function(t){var e=[];this.each([t],function(t){isNaN(t)||e.push(t)});var n=e.sort(function(t,e){return t-e}),r=this.count();return 0===r?0:r%2==1?n[(r-1)/2]:(n[r/2]+n[r/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return -1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var r=0,i=this._count-1;r<=i;){var o=(r+i)/2|0;if(e[o]<t)r=o+1;else{if(!(e[o]>t))return o;i=o-1}}return -1},t.prototype.indicesOfNearest=function(t,e,n){var r=this._chunks[t],i=[];if(!r)return i;null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,u=0,l=this.count();u<l;u++){var c=e-r[this.getRawIndex(u)],h=Math.abs(c);h<=n&&((h<o||h===o&&c>=0&&a<0)&&(o=h,a=c,s=0),c===a&&(i[s++]=u))}return i.length=s,i},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,r=this._count;if(n===Array){t=new n(r);for(var i=0;i<r;i++)t[i]=e[i]}else t=new n(e.buffer,0,r)}else{var n=p(this._rawCount);t=new n(this.count());for(var i=0;i<t.length;i++)t[i]=i}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),r=n.count(),i=new(p(n._rawCount))(r),o=[],a=t.length,s=0,u=t[0],l=n._chunks,c=0;c<r;c++){var h=void 0,f=n.getRawIndex(c);if(0===a)h=e(c);else if(1===a)h=e(l[u][f],c);else{for(var d=0;d<a;d++)o[d]=l[t[d]][f];o[d]=c,h=e.apply(null,o)}h&&(i[s++]=f)}return s<r&&(n._indices=i),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var r=(0,i.keys)(t),o=r.length;if(!o)return this;var a=e.count(),s=new(p(e._rawCount))(a),u=0,l=r[0],c=t[l][0],h=t[l][1],f=e._chunks,d=!1;if(!e._indices){var g=0;if(1===o){for(var y=f[r[0]],v=0;v<n;v++){var m=y[v];(m>=c&&m<=h||isNaN(m))&&(s[u++]=g),g++}d=!0}else if(2===o){for(var y=f[r[0]],_=f[r[1]],x=t[r[1]][0],b=t[r[1]][1],v=0;v<n;v++){var m=y[v],w=_[v];(m>=c&&m<=h||isNaN(m))&&(w>=x&&w<=b||isNaN(w))&&(s[u++]=g),g++}d=!0}}if(!d)if(1===o)for(var v=0;v<a;v++){var S=e.getRawIndex(v),m=f[r[0]][S];(m>=c&&m<=h||isNaN(m))&&(s[u++]=S)}else for(var v=0;v<a;v++){for(var M=!0,S=e.getRawIndex(v),A=0;A<o;A++){var T=r[A],m=f[T][S];(m<t[T][0]||m>t[T][1])&&(M=!1)}M&&(s[u++]=e.getRawIndex(v))}return u<a&&(e._indices=s),e._count=u,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var r=t._chunks,i=[],o=e.length,a=t.count(),s=[],u=t._rawExtent,l=0;l<e.length;l++)u[e[l]]=d();for(var c=0;c<a;c++){for(var h=t.getRawIndex(c),f=0;f<o;f++)s[f]=r[e[f]][h];s[o]=c;var p=n&&n.apply(null,s);if(null!=p){"object"!=typeof p&&(i[0]=p,p=i);for(var l=0;l<p.length;l++){var g=e[l],y=p[l],v=u[g],m=r[g];m&&(m[h]=y),y<v[0]&&(v[0]=y),y>v[1]&&(v[1]=y)}}}},t.prototype.lttbDownSample=function(t,e){var n,r,i,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),u=0,l=Math.floor(1/e),c=this.getRawIndex(0),h=new(p(this._rawCount))(Math.min((Math.ceil(s/l)+2)*2,s));h[u++]=c;for(var f=1;f<s-1;f+=l){for(var d=Math.min(f+l,s-1),g=Math.min(f+2*l,s),y=(g+d)/2,v=0,m=d;m<g;m++){var _=this.getRawIndex(m),x=a[_];isNaN(x)||(v+=x)}v/=g-d;var b=f,w=Math.min(f+l,s),S=f-1,M=a[c];n=-1,i=b;for(var A=-1,T=0,m=b;m<w;m++){var _=this.getRawIndex(m),x=a[_];if(isNaN(x)){T++,A<0&&(A=_);continue}(r=Math.abs((S-y)*(x-M)-(S-m)*(v-M)))>n&&(n=r,i=_)}T>0&&T<w-b&&(h[u++]=Math.min(A,i),i=Math.max(A,i)),h[u++]=i,c=i}return h[u++]=this.getRawIndex(s-1),o._count=u,o._indices=h,o.getRawIndex=this._getRawIdx,o},t.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),r=n._chunks,i=Math.floor(1/e),o=r[t],a=this.count(),s=new(p(this._rawCount))(2*Math.ceil(a/i)),u=0,l=0;l<a;l+=i){var c=l,h=o[this.getRawIndex(c)],f=l,d=o[this.getRawIndex(f)],g=i;l+i>a&&(g=a-l);for(var y=0;y<g;y++){var v=o[this.getRawIndex(l+y)];v<h&&(h=v,c=l+y),v>d&&(d=v,f=l+y)}var m=this.getRawIndex(c),_=this.getRawIndex(f);c<f?(s[u++]=m,s[u++]=_):(s[u++]=_,s[u++]=m)}return n._count=u,n._indices=s,n._updateGetRawIdx(),n},t.prototype.downSample=function(t,e,n,r){for(var i=this.clone([t],!0),o=i._chunks,a=[],s=Math.floor(1/e),u=o[t],l=this.count(),c=i._rawExtent[t]=d(),h=new(p(this._rawCount))(Math.ceil(l/s)),f=0,g=0;g<l;g+=s){s>l-g&&(a.length=s=l-g);for(var y=0;y<s;y++){var v=this.getRawIndex(g+y);a[y]=u[v]}var m=n(a),_=this.getRawIndex(Math.min(g+r(a,m)||0,l-1));u[_]=m,m<c[0]&&(c[0]=m),m>c[1]&&(c[1]=m),h[f++]=_}return i._count=f,i._indices=h,i._updateGetRawIdx(),i},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,r=this._chunks,i=0,o=this.count();i<o;i++){var a=this.getRawIndex(i);switch(n){case 0:e(i);break;case 1:e(r[t[0]][a],i);break;case 2:e(r[t[0]][a],r[t[1]][a],i);break;default:for(var s=0,u=[];s<n;s++)u[s]=r[t[s]][a];u[s]=i,e.apply(null,u)}}},t.prototype.getDataExtent=function(t){var e,n=this._chunks[t],r=d();if(!n)return r;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(e=this._extent[t])return e.slice();for(var o=(e=r)[0],a=e[1],s=0;s<i;s++){var u=n[this.getRawIndex(s)];u<o&&(o=u),u>a&&(a=u)}return e=[o,a],this._extent[t]=e,e},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],r=this._chunks,i=0;i<r.length;i++)n.push(r[i][e]);return n},t.prototype.clone=function(e,n){var r=new t,o=this._chunks,a=e&&(0,i.reduce)(e,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?function(t){var e=t.constructor;return e===Array?t.slice():new e(t)}(o[s]):o[s];else r._chunks=o;return this._copyCommonProps(r),n||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=(0,i.clone)(this._extent),t._rawExtent=(0,i.clone)(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var r=0;r<n;r++)e[r]=this._indices[r]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,r){return(0,o.Pn)(t[r],this._dimensions[r])}r={arrayRows:t,objectRows:function(t,e,n,r){return(0,o.Pn)(t[e],this._dimensions[r])},keyedColumns:t,original:function(t,e,n,r){var i=t&&(null==t.value?t:t.value);return(0,o.Pn)(i instanceof Array?i[r]:i,this._dimensions[r])},typedArray:function(t,e,n,r){return t[r]}}}(),t}()},3013:t=>{"use strict";t.exports=function t(e,n){if(e===n)return!0;if(e&&n&&"object"==typeof e&&"object"==typeof n){if(e.constructor!==n.constructor)return!1;if(Array.isArray(e)){if((r=e.length)!=n.length)return!1;for(i=r;0!=i--;)if(!t(e[i],n[i]))return!1;return!0}if(e.constructor===RegExp)return e.source===n.source&&e.flags===n.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===n.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===n.toString();if((r=(o=Object.keys(e)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,a=o[i];if(!t(e[a],n[a]))return!1}return!0}return e!=e&&n!=n}},3030:(t,e,n)=>{"use strict";function r(t){return isFinite(t)}function i(t,e,n){for(var i,o,a,s,u,l,c,h,f,p,d="radial"===e.type?(a=Math.min(i=n.width,o=n.height),s=null==e.x?.5:e.x,u=null==e.y?.5:e.y,l=null==e.r?.5:e.r,e.global||(s=s*i+n.x,u=u*o+n.y,l*=a),s=r(s)?s:.5,u=r(u)?u:.5,l=l>=0&&r(l)?l:.5,t.createRadialGradient(s,u,0,s,u,l)):(c=null==e.x?0:e.x,h=null==e.x2?1:e.x2,f=null==e.y?0:e.y,p=null==e.y2?0:e.y2,e.global||(c=c*n.width+n.x,h=h*n.width+n.x,f=f*n.height+n.y,p=p*n.height+n.y),c=r(c)?c:0,h=r(h)?h:1,f=r(f)?f:0,p=r(p)?p:0,t.createLinearGradient(c,f,h,p)),g=e.colorStops,y=0;y<g.length;y++)d.addColorStop(g[y].offset,g[y].color);return d}function o(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}function a(t){return parseInt(t,10)}function s(t,e,n){var r=["width","height"][e],i=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],s=["paddingRight","paddingBottom"][e];if(null!=n[r]&&"auto"!==n[r])return parseFloat(n[r]);var u=document.defaultView.getComputedStyle(t);return(t[i]||a(u[r])||a(t.style[r]))-(a(u[o])||0)-(a(u[s])||0)|0}n.d(e,{Ff:()=>i,KU:()=>o,YC:()=>s})},3213:(t,e,n)=>{"use strict";n.d(e,{B:()=>u,l:()=>a});var r=n(4123),i=n(7439),o=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function a(t,e){var n={},a=n.encode={},u=(0,r.createHashMap)(),l=[],c=[],h={};(0,r.each)(t.dimensions,function(e){var n=t.getDimensionInfo(e),r=n.coordDim;if(r){var o,f=n.coordDimIndex;s(a,r)[f]=e,n.isExtraCoord||(u.set(r,1),"ordinal"!==(o=n.type)&&"time"!==o&&(l[0]=e),s(h,r)[f]=t.getDimensionIndex(n.name)),n.defaultTooltip&&c.push(e)}i.Pe.each(function(t,e){var r=s(a,e),i=n.otherDims[e];null!=i&&!1!==i&&(r[i]=n.name)})});var f=[],p={};u.each(function(t,e){var n=a[e];p[e]=n[0],f=f.concat(n)}),n.dataDimsOnCoord=f,n.dataDimIndicesOnCoord=(0,r.map)(f,function(e){return t.getDimensionInfo(e).storeDimIndex}),n.encodeFirstDimNotExtra=p;var d=a.label;d&&d.length&&(l=d.slice());var g=a.tooltip;return g&&g.length?c=g.slice():c.length||(c=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=c,n.userOutput=new o(h,e),n}function s(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function u(t){return"category"===t?"ordinal":"time"===t?"time":"float"}},3219:(t,e,n)=>{"use strict";n.d(e,{V:()=>i});var r=n(4123);function i(t){var e,n,i=t.style,o=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,n=i.lineWidth,e&&"solid"!==e&&n>0?"dashed"===e?[4*n,2*n]:"dotted"===e?[n]:(0,r.isNumber)(e)?[e]:(0,r.isArray)(e)?e:null:null),a=i.lineDashOffset;if(o){var s=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;s&&1!==s&&(o=(0,r.map)(o,function(t){return t/s}),a/=s)}return[o,a]}},3273:(t,e,n)=>{"use strict";n.d(e,{Dl:()=>o,M:()=>r,pO:()=>i});var r=1,i=2,o=4},3312:(t,e,n)=>{"use strict";function r(t){}function i(t){throw Error(t)}n.d(e,{$8:()=>i,aT:()=>r}),"undefined"!=typeof console&&console.warn},3346:(t,e,n)=>{"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.UnmountClosed=void 0;var i=function(t){return t&&t.__esModule?t:{default:t}}(n(2115)),o=n(9445),a=["isOpened"],s=["isOpened"];function u(){return(u=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function l(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?l(Object(n),!0).forEach(function(e){g(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function h(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function f(t,e){return(f=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function p(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function d(t){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function g(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var y=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");y.prototype=Object.create(t&&t.prototype,{constructor:{value:y,writable:!0,configurable:!0}}),t&&f(y,t);var e,n,l=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=d(y);return t=e?Reflect.construct(n,arguments,d(this).constructor):n.apply(this,arguments),function(t,e){if(e&&("object"===r(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return p(t)}(this,t)});function y(t){var e;if(!(this instanceof y))throw TypeError("Cannot call a class as a function");return g(p(e=l.call(this,t)),"onWork",function(t){var n=t.isOpened,r=h(t,a);e.setState({isResting:!1,isOpened:n});var i=e.props.onWork;i&&i(c({isOpened:n},r))}),g(p(e),"onRest",function(t){var n=t.isOpened,r=h(t,s);e.setState({isResting:!0,isOpened:n,isInitialRender:!1});var i=e.props.onRest;i&&i(c({isOpened:n},r))}),g(p(e),"getInitialStyle",function(){var t=e.state,n=t.isOpened;return t.isInitialRender&&n?{height:"auto",overflow:"initial"}:{height:"0px",overflow:"hidden"}}),e.state={isResting:!0,isOpened:t.isOpened,isInitialRender:!0},e}return n=[{key:"componentDidUpdate",value:function(t){var e=this.props.isOpened;t.isOpened!==e&&this.setState({isResting:!1,isOpened:e,isInitialRender:!1})}},{key:"render",value:function(){var t=this.state,e=t.isResting,n=t.isOpened;return e&&!n?null:i.default.createElement(o.Collapse,u({},this.props,{initialStyle:this.getInitialStyle(),onWork:this.onWork,onRest:this.onRest}))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}(y.prototype,n),y}(i.default.PureComponent);e.UnmountClosed=y,g(y,"defaultProps",{onWork:void 0,onRest:void 0})},3424:(t,e,n)=>{"use strict";n.d(e,{Pn:()=>a});var r=n(3607),i=n(4123),o=n(3312);function a(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||(0,i.isNumber)(t)||null==t||"-"===t||(t=+(0,r._U)(t)),null==t||""===t?NaN:Number(t))}(0,i.createHashMap)({number:function(t){return parseFloat(t)},time:function(t){return+(0,r._U)(t)},trim:function(t){return(0,i.isString)(t)?(0,i.trim)(t):t}});var s={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return t>e},gte:function(t,e){return t>=e}};(function(t,e){(0,i.isNumber)(e)||(0,o.$8)(""),this._opFn=s[t],this._rvalFloat=(0,r.Sm)(e)}).prototype.evaluate=function(t){return(0,i.isNumber)(t)?this._opFn(t,this._rvalFloat):this._opFn((0,r.Sm)(t),this._rvalFloat)},(function(t,e){var n="desc"===t;this._resultLT=n?1:-1,null==e&&(e=n?"min":"max"),this._incomparable="min"===e?-1/0:1/0}).prototype.evaluate=function(t,e){var n=(0,i.isNumber)(t)?t:(0,r.Sm)(t),o=(0,i.isNumber)(e)?e:(0,r.Sm)(e),a=isNaN(n),s=isNaN(o);if(a&&(n=this._incomparable),s&&(o=this._incomparable),a&&s){var u=(0,i.isString)(t),l=(0,i.isString)(e);u&&(n=l?t:0),l&&(o=u?e:0)}return n<o?this._resultLT:n>o?-this._resultLT:0},(function(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=(0,r.Sm)(e)}).prototype.evaluate=function(t){var e=t===this._rval;if(!e){var n=typeof t;n!==this._rvalTypeof&&("number"===n||"number"===this._rvalTypeof)&&(e=(0,r.Sm)(t)===this._rvalFloat)}return this._isEQ?e:!e}},3493:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>c});var r=n(643),i=n(5921),o=n(7669),a=n(4123),s=(0,a.defaults)({x:0,y:0},i.oN),u={style:(0,a.defaults)({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},i.sW.style)},l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.__extends)(e,t),e.prototype.createStyle=function(t){return(0,a.createObject)(s,t)},e.prototype._getSize=function(t){var e,n=this.style,r=n[t];if(null!=r)return r;var i=(e=n.image)&&"string"!=typeof e&&e.width&&e.height?n.image:this.__image;if(!i)return 0;var o="width"===t?"height":"width",a=n[o];return null==a?i[t]:i[t]/i[o]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return u},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new o.A(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(i.Ay);l.prototype.type="image";let c=l},3607:(t,e,n)=>{"use strict";n.d(e,{Cb:()=>i,Cm:()=>x,IH:()=>A,Is:()=>p,LI:()=>a,NX:()=>_,Sm:()=>S,Tr:()=>f,XV:()=>u,Y6:()=>s,YV:()=>b,_7:()=>d,_U:()=>v,au:()=>m,dh:()=>g,hb:()=>c,kf:()=>M,lQ:()=>T,lo:()=>o,sL:()=>w,wp:()=>h,y6:()=>l});var r=n(4123);function i(t,e,n,r){var i=e[0],o=e[1],a=n[0],s=n[1],u=o-i,l=s-a;if(0===u)return 0===l?a:(a+s)/2;if(r){if(u>0){if(t<=i)return a;else if(t>=o)return s}else if(t>=i)return a;else if(t<=o)return s}else{if(t===i)return a;if(t===o)return s}return(t-i)/u*l+a}function o(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return r.isString(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function a(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function s(t){return t.sort(function(t,e){return t-e}),t}function u(t){if(isNaN(t*=1))return 0;if(t>1e-14){for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n}return l(t)}function l(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),r=n>0?+e.slice(n+1):0,i=n>0?n:e.length,o=e.indexOf(".");return Math.max(0,(o<0?0:i-1-o)-r)}function c(t,e){var n=Math.log,r=Math.LN10,i=Math.min(Math.max(-Math.floor(n(t[1]-t[0])/r)+Math.round(n(Math.abs(e[1]-e[0]))/r),0),20);return isFinite(i)?i:20}function h(t,e,n){return t[e]&&function(t,e){var n=r.reduce(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];for(var i=Math.pow(10,e),o=r.map(t,function(t){return(isNaN(t)?0:t)/n*i*100}),a=100*i,s=r.map(o,function(t){return Math.floor(t)}),u=r.reduce(s,function(t,e){return t+e},0),l=r.map(o,function(t,e){return t-s[e]});u<a;){for(var c=-1/0,h=null,f=0,p=l.length;f<p;++f)l[f]>c&&(c=l[f],h=f);++s[h],l[h]=0,++u}return r.map(s,function(t){return t/i})}(t,n)[e]||0}function f(t,e){var n=Math.max(u(t),u(e)),r=t+e;return n>20?r:a(r,n)}var p=0x1fffffffffffff;function d(t){var e=2*Math.PI;return(t%e+e)%e}function g(t){return t>-1e-4&&t<1e-4}var y=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function v(t){if(t instanceof Date)return t;if(r.isString(t)){var e=y.exec(t);if(!e)return new Date(NaN);if(!e[8])return new Date(+e[1],(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0);var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}return null==t?new Date(NaN):new Date(Math.round(t))}function m(t){return Math.pow(10,_(t))}function _(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function x(t,e){var n=_(t),r=Math.pow(10,n),i=t/r;return t=(e?i<1.5?1:i<2.5?2:i<4?3:i<7?5:10:i<1?1:i<2?2:i<3?3:i<5?5:10)*r,n>=-20?+t.toFixed(n<0?-n:0):t}function b(t,e){var n=(t.length-1)*e+1,r=Math.floor(n),i=+t[r-1],o=n-r;return o?i+o*(t[r]-i):i}function w(t){t.sort(function(t,e){return!function t(e,n,r){return e.interval[r]<n.interval[r]||e.interval[r]===n.interval[r]&&(e.close[r]-n.close[r]==(r?-1:1)||!r&&t(e,n,1))}(t,e,0)?1:-1});for(var e=-1/0,n=1,r=0;r<t.length;){for(var i=t[r].interval,o=t[r].close,a=0;a<2;a++)i[a]<=e&&(i[a]=e,o[a]=a?1:1-n),e=i[a],n=o[a];i[0]===i[1]&&o[0]*o[1]!=1?t.splice(r,1):r++}return t}function S(t){var e=parseFloat(t);return e==t&&(0!==e||!r.isString(t)||0>=t.indexOf("x"))?e:NaN}function M(t){return!isNaN(S(t))}function A(){return Math.round(9*Math.random())}function T(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}},3615:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SizeSensorId=e.SensorTabIndex=e.SensorClassName=void 0,e.SizeSensorId="size-sensor-id",e.SensorClassName="size-sensor-object",e.SensorTabIndex="-1"},3809:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(4123);function i(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,o){for(var a={},s=0;s<t.length;s++){var u=t[s][1];if(!(i&&r.indexOf(i,u)>=0||o&&0>r.indexOf(o,u))){var l=n.getShallow(u,e);null!=l&&(a[t[s][0]]=l)}}return a}}},3875:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>C,I5:()=>_,XE:()=>b});var r=n(643),i=n(1823),o=n(6436),a=n(4123),s=n(6847),u=n(3493),l=n(7821),c=n(7669),h=n(5921),f=n(2316),p={fill:"#000"},d={style:(0,a.defaults)({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},h.sW.style)},g=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=p,n.attr(e),n}return(0,r.__extends)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){var t;this._childCursor=0,w(t=this.style),(0,a.each)(t.rich,w),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new c.A(0,0,0,0),e=this._children,n=[],r=null,i=0;i<e.length;i++){var o=e[i],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(r=r||t.clone()).union(t)):(r=r||a.clone()).union(a)}this._rect=r||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||p},e.prototype.setTextContent=function(t){},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,r=t.rich||n&&{};return(0,a.extend)(t,e),n&&r?(this._mergeRich(r,n),t.rich=r):r&&(t.rich=r),t},e.prototype._mergeRich=function(t,e){for(var n=(0,a.keys)(e),r=0;r<n.length;r++){var i=n[r];t[i]=t[i]||{},(0,a.extend)(t[i],e[i])}},e.prototype.getAnimationStyleProps=function(){return d},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||f.OH,n=t.padding,r=T(t),a=(0,i.j_)(r,t),u=k(t),l=!!t.backgroundColor,h=a.outerHeight,p=a.outerWidth,d=a.contentWidth,g=a.lines,y=a.lineHeight,v=this._defaultStyle;this.isTruncated=!!a.isTruncated;var m=t.x||0,_=t.y||0,b=t.align||v.align||"left",w=t.verticalAlign||v.verticalAlign||"top",C=m,I=(0,s.sZ)(_,a.contentHeight,w);if(u||n){var D=(0,s.ll)(m,p,b),O=(0,s.sZ)(_,h,w);u&&this._renderBackground(t,t,D,O,p,h)}I+=y/2,n&&(C=A(m,b,n),"top"===w?I+=n[0]:"bottom"===w&&(I-=n[2]));for(var P=0,L=!1,R=M("fill"in t?t.fill:(L=!0,v.fill)),E=S("stroke"in t?t.stroke:l||v.autoStroke&&!L?null:(P=2,v.stroke)),N=t.textShadowBlur>0,B=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),F=a.calculatedLineHeight,z=0;z<g.length;z++){var V=this._getOrCreateChild(o.A),H=V.createStyle();V.useStyle(H),H.text=g[z],H.x=C,H.y=I,b&&(H.textAlign=b),H.textBaseline="middle",H.opacity=t.opacity,H.strokeFirst=!0,N&&(H.shadowBlur=t.textShadowBlur||0,H.shadowColor=t.textShadowColor||"transparent",H.shadowOffsetX=t.textShadowOffsetX||0,H.shadowOffsetY=t.textShadowOffsetY||0),H.stroke=E,H.fill=R,E&&(H.lineWidth=t.lineWidth||P,H.lineDash=t.lineDash,H.lineDashOffset=t.lineDashOffset||0),H.font=e,x(H,t),I+=y,B&&V.setBoundingRect(new c.A((0,s.ll)(H.x,d,H.textAlign),(0,s.sZ)(H.y,F,H.textBaseline),d,F))}},e.prototype._updateRichTexts=function(){var t=this.style,e=T(t),n=(0,i.FQ)(e,t),r=n.width,o=n.outerWidth,a=n.outerHeight,u=t.padding,l=t.x||0,c=t.y||0,h=this._defaultStyle,f=t.align||h.align,p=t.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var d=(0,s.ll)(l,o,f),g=(0,s.sZ)(c,a,p),y=d,v=g;u&&(y+=u[3],v+=u[0]);var m=y+r;k(t)&&this._renderBackground(t,t,d,g,o,a);for(var _=!!t.backgroundColor,x=0;x<n.lines.length;x++){for(var b=n.lines[x],w=b.tokens,S=w.length,M=b.lineHeight,A=b.width,C=0,I=y,D=m,O=S-1,P=void 0;C<S&&(!(P=w[C]).align||"left"===P.align);)this._placeToken(P,t,M,v,I,"left",_),A-=P.width,I+=P.width,C++;for(;O>=0&&"right"===(P=w[O]).align;)this._placeToken(P,t,M,v,D,"right",_),A-=P.width,D-=P.width,O--;for(I+=(r-(I-y)-(m-D)-A)/2;C<=O;)P=w[C],this._placeToken(P,t,M,v,I+P.width/2,"center",_),I+=P.width,C++;v+=M}},e.prototype._placeToken=function(t,e,n,r,i,u,l){var h=e.rich[t.styleName]||{};h.text=t.text;var p=t.verticalAlign,d=r+n/2;"top"===p?d=r+t.height/2:"bottom"===p&&(d=r+n-t.height/2),!t.isLineHolder&&k(h)&&this._renderBackground(h,e,"right"===u?i-t.width:"center"===u?i-t.width/2:i,d-t.height/2,t.width,t.height);var g=!!h.backgroundColor,y=t.textPadding;y&&(i=A(i,u,y),d-=t.height/2-y[0]-t.innerHeight/2);var v=this._getOrCreateChild(o.A),m=v.createStyle();v.useStyle(m);var _=this._defaultStyle,b=!1,w=0,T=M("fill"in h?h.fill:"fill"in e?e.fill:(b=!0,_.fill)),C=S("stroke"in h?h.stroke:"stroke"in e?e.stroke:g||l||_.autoStroke&&!b?null:(w=2,_.stroke)),I=h.textShadowBlur>0||e.textShadowBlur>0;m.text=t.text,m.x=i,m.y=d,I&&(m.shadowBlur=h.textShadowBlur||e.textShadowBlur||0,m.shadowColor=h.textShadowColor||e.textShadowColor||"transparent",m.shadowOffsetX=h.textShadowOffsetX||e.textShadowOffsetX||0,m.shadowOffsetY=h.textShadowOffsetY||e.textShadowOffsetY||0),m.textAlign=u,m.textBaseline="middle",m.font=t.font||f.OH,m.opacity=(0,a.retrieve3)(h.opacity,e.opacity,1),x(m,h),C&&(m.lineWidth=(0,a.retrieve3)(h.lineWidth,e.lineWidth,w),m.lineDash=(0,a.retrieve2)(h.lineDash,e.lineDash),m.lineDashOffset=e.lineDashOffset||0,m.stroke=C),T&&(m.fill=T);var D=t.contentWidth,O=t.contentHeight;v.setBoundingRect(new c.A((0,s.ll)(m.x,D,m.textAlign),(0,s.sZ)(m.y,O,m.textBaseline),D,O))},e.prototype._renderBackground=function(t,e,n,r,i,o){var s,c,h=t.backgroundColor,f=t.borderWidth,p=t.borderColor,d=h&&h.image,g=h&&!d,y=t.borderRadius,v=this;if(g||t.lineHeight||f&&p){(s=this._getOrCreateChild(l.A)).useStyle(s.createStyle()),s.style.fill=null;var m=s.shape;m.x=n,m.y=r,m.width=i,m.height=o,m.r=y,s.dirtyShape()}if(g){var _=s.style;_.fill=h||null,_.fillOpacity=(0,a.retrieve2)(t.fillOpacity,1)}else if(d){(c=this._getOrCreateChild(u.Ay)).onload=function(){v.dirtyStyle()};var x=c.style;x.image=h.image,x.x=n,x.y=r,x.width=i,x.height=o}if(f&&p){var _=s.style;_.lineWidth=f,_.stroke=p,_.strokeOpacity=(0,a.retrieve2)(t.strokeOpacity,1),_.lineDash=t.borderDash,_.lineDashOffset=t.borderDashOffset||0,s.strokeContainThreshold=0,s.hasFill()&&s.hasStroke()&&(_.strokeFirst=!0,_.lineWidth*=2)}var b=(s||c).style;b.shadowBlur=t.shadowBlur||0,b.shadowColor=t.shadowColor||"transparent",b.shadowOffsetX=t.shadowOffsetX||0,b.shadowOffsetY=t.shadowOffsetY||0,b.opacity=(0,a.retrieve3)(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return b(t)&&(e=[t.fontStyle,t.fontWeight,_(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&(0,a.trim)(e)||t.textFont||t.font},e}(h.Ay),y={left:!0,right:1,center:1},v={top:1,bottom:1,middle:1},m=["fontStyle","fontWeight","fontSize","fontFamily"];function _(t){return"string"==typeof t&&(-1!==t.indexOf("px")||-1!==t.indexOf("rem")||-1!==t.indexOf("em"))?t:isNaN(+t)?f.gI+"px":t+"px"}function x(t,e){for(var n=0;n<m.length;n++){var r=m[n],i=e[r];null!=i&&(t[r]=i)}}function b(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function w(t){if(t){t.font=g.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||y[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||v[n]?n:"top",t.padding&&(t.padding=(0,a.normalizeCssArray)(t.padding))}}function S(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function M(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function A(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function T(t){var e=t.text;return null!=e&&(e+=""),e}function k(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}let C=g},3948:(t,e,n)=>{"use strict";n.d(e,{A:()=>l});var r=n(643),i=n(4271),o=n(6600),a={},s=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},u=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new s},e.prototype.buildPath=function(t,e){if(this.subPixelOptimize){var n,r,i,s,u=(0,o.eB)(a,e,this.style);n=u.x1,r=u.y1,i=u.x2,s=u.y2}else n=e.x1,r=e.y1,i=e.x2,s=e.y2;var l=e.percent;0!==l&&(t.moveTo(n,r),l<1&&(i=n*(1-l)+i*l,s=r*(1-l)+s*l),t.lineTo(i,s))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(i.Ay);u.prototype.type="line";let l=u},3949:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isEqual=void 0,e.isEqual=(0,n(643).__importDefault)(n(3013)).default},4035:(t,e,n)=>{"use strict";n.d(e,{Y5:()=>o,_S:()=>s,el:()=>u,ps:()=>a,tY:()=>l});var r=n(7170),i=1;r.A.hasGlobalWindow&&(i=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var o=i,a=.4,s="#333",u="#ccc",l="#eee"},4123:(t,e,n)=>{"use strict";n.r(e),n.d(e,{HashMap:()=>ts,RADIAN_TO_DEGREE:()=>td,assert:()=>tt,bind:()=>R,clone:()=>m,concatArray:()=>tl,createCanvas:()=>S,createHashMap:()=>tu,createObject:()=>tc,curry:()=>E,defaults:()=>w,disableUserSelect:()=>th,each:()=>C,eqNaN:()=>Y,extend:()=>b,filter:()=>O,find:()=>P,guid:()=>y,hasOwn:()=>tf,indexOf:()=>M,inherits:()=>A,isArray:()=>N,isArrayLike:()=>k,isBuiltInObject:()=>j,isDom:()=>W,isFunction:()=>B,isGradientObject:()=>G,isImagePatternObject:()=>q,isNumber:()=>V,isObject:()=>H,isPrimitive:()=>ti,isRegExp:()=>X,isString:()=>F,isStringSafe:()=>z,isTypedArray:()=>U,keys:()=>L,logError:()=>v,map:()=>I,merge:()=>_,mergeAll:()=>x,mixin:()=>T,noop:()=>tp,normalizeCssArray:()=>J,reduce:()=>D,retrieve:()=>Z,retrieve2:()=>$,retrieve3:()=>K,setAsPrimitive:()=>tr,slice:()=>Q,trim:()=>te});var r=n(2316),i=D(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),o=D(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),a=Object.prototype.toString,s=Array.prototype,u=s.forEach,l=s.filter,c=s.slice,h=s.map,f=(function(){}).constructor,p=f?f.prototype:null,d="__proto__",g=2311;function y(){return g++}function v(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function m(t){if(null==t||"object"!=typeof t)return t;var e=t,n=a.call(t);if("[object Array]"===n){if(!t[tn]){e=[];for(var r=0,s=t.length;r<s;r++)e[r]=m(t[r])}}else if(o[n]){if(!t[tn]){var u=t.constructor;if(u.from)e=u.from(t);else{e=new u(t.length);for(var r=0,s=t.length;r<s;r++)e[r]=t[r]}}}else if(!i[n]&&!t[tn]&&!W(t))for(var l in e={},t)t.hasOwnProperty(l)&&l!==d&&(e[l]=m(t[l]));return e}function _(t,e,n){if(!H(e)||!H(t))return n?m(e):t;for(var r in e)if(e.hasOwnProperty(r)&&r!==d){var i=t[r],o=e[r];!(H(o)&&H(i))||N(o)||N(i)||W(o)||W(i)||j(o)||j(i)||o[tn]||i[tn]?!n&&r in t||(t[r]=m(e[r])):_(i,o,n)}return t}function x(t,e){for(var n=t[0],r=1,i=t.length;r<i;r++)n=_(n,t[r],e);return n}function b(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==d&&(t[n]=e[n]);return t}function w(t,e,n){for(var r=L(e),i=0,o=r.length;i<o;i++){var a=r[i];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var S=r.yh.createCanvas;function M(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n}return -1}function A(t,e){var n=t.prototype;function r(){}for(var i in r.prototype=e.prototype,t.prototype=new r,n)n.hasOwnProperty(i)&&(t.prototype[i]=n[i]);t.prototype.constructor=t,t.superClass=e}function T(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var r=Object.getOwnPropertyNames(e),i=0;i<r.length;i++){var o=r[i];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else w(t,e,n)}function k(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function C(t,e,n){if(t&&e)if(t.forEach&&t.forEach===u)t.forEach(e,n);else if(t.length===+t.length)for(var r=0,i=t.length;r<i;r++)e.call(n,t[r],r,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function I(t,e,n){if(!t)return[];if(!e)return Q(t);if(t.map&&t.map===h)return t.map(e,n);for(var r=[],i=0,o=t.length;i<o;i++)r.push(e.call(n,t[i],i,t));return r}function D(t,e,n,r){if(t&&e){for(var i=0,o=t.length;i<o;i++)n=e.call(r,n,t[i],i,t);return n}}function O(t,e,n){if(!t)return[];if(!e)return Q(t);if(t.filter&&t.filter===l)return t.filter(e,n);for(var r=[],i=0,o=t.length;i<o;i++)e.call(n,t[i],i,t)&&r.push(t[i]);return r}function P(t,e,n){if(t&&e){for(var r=0,i=t.length;r<i;r++)if(e.call(n,t[r],r,t))return t[r]}}function L(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}var R=p&&B(p.bind)?p.call.bind(p.bind):function(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return function(){return t.apply(e,n.concat(c.call(arguments)))}};function E(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(c.call(arguments)))}}function N(t){return Array.isArray?Array.isArray(t):"[object Array]"===a.call(t)}function B(t){return"function"==typeof t}function F(t){return"string"==typeof t}function z(t){return"[object String]"===a.call(t)}function V(t){return"number"==typeof t}function H(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function j(t){return!!i[a.call(t)]}function U(t){return!!o[a.call(t)]}function W(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function G(t){return null!=t.colorStops}function q(t){return null!=t.image}function X(t){return"[object RegExp]"===a.call(t)}function Y(t){return t!=t}function Z(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,r=t.length;n<r;n++)if(null!=t[n])return t[n]}function $(t,e){return null!=t?t:e}function K(t,e,n){return null!=t?t:null!=e?e:n}function Q(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return c.apply(t,e)}function J(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function tt(t,e){if(!t)throw Error(e)}function te(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var tn="__ec_primitive__";function tr(t){t[tn]=!0}function ti(t){return t[tn]}var to=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return L(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},t}(),ta="function"==typeof Map,ts=function(){function t(e){var n=N(e);this.data=ta?new Map:new to;var r=this;function i(t,e){n?r.set(t,e):r.set(e,t)}e instanceof t?e.each(i):e&&C(e,i)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach(function(n,r){t.call(e,n,r)})},t.prototype.keys=function(){var t=this.data.keys();return ta?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function tu(t){return new ts(t)}function tl(t,e){for(var n=new t.constructor(t.length+e.length),r=0;r<t.length;r++)n[r]=t[r];for(var i=t.length,r=0;r<e.length;r++)n[r+i]=e[r];return n}function tc(t,e){var n;if(Object.create)n=Object.create(t);else{var r=function(){};r.prototype=t,n=new r}return e&&b(n,e),n}function th(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function tf(t,e){return t.hasOwnProperty(e)}function tp(){}var td=180/Math.PI},4271:(t,e,n)=>{"use strict";n.d(e,{MW:()=>b,Ay:()=>M});var r=n(643),i=n(5921),o=n(8248);function a(t,e,n,r,i,o,a){if(0===i)return!1;var s=0,u=t;if(a>e+i&&a>r+i||a<e-i&&a<r-i||o>t+i&&o>n+i||o<t-i&&o<n-i)return!1;if(t===n)return Math.abs(o-t)<=i/2;s=(e-r)/(t-n);var l=s*o-a+(u=(t*r-n*e)/(t-n));return l*l/(s*s+1)<=i/2*i/2}var s=n(882),u=n(6748),l=2*Math.PI,c=n(179),h=o.A.CMD,f=2*Math.PI,p=[-1,-1,-1],d=[-1,-1];function g(t,e,n,r,i){for(var o,g,y=t.data,v=t.len(),m=0,_=0,x=0,b=0,w=0,S=0;S<v;){var M,A,T,k,C,I,D,O,P,L,R,E,N,B,F=y[S++],z=1===S;switch(F===h.M&&S>1&&!n&&(m+=(0,c.A)(_,x,b,w,r,i)),z&&(_=y[S],x=y[S+1],b=_,w=x),F){case h.M:b=y[S++],w=y[S++],_=b,x=w;break;case h.L:if(n){if(a(_,x,y[S],y[S+1],e,r,i))return!0}else m+=(0,c.A)(_,x,y[S],y[S+1],r,i)||0;_=y[S++],x=y[S++];break;case h.C:if(n){if(M=_,A=x,T=y[S++],k=y[S++],C=y[S++],I=y[S++],D=y[S],O=y[S+1],0!==e&&(!(i>A+e)||!(i>k+e)||!(i>I+e)||!(i>O+e))&&(!(i<A-e)||!(i<k-e)||!(i<I-e)||!(i<O-e))&&(!(r>M+e)||!(r>T+e)||!(r>C+e)||!(r>D+e))&&(!(r<M-e)||!(r<T-e)||!(r<C-e)||!(r<D-e))&&s.Et(M,A,T,k,C,I,D,O,r,i,null)<=e/2)return!0}else m+=function(t,e,n,r,i,o,a,u,l,c){if(c>e&&c>r&&c>o&&c>u||c<e&&c<r&&c<o&&c<u)return 0;var h=s._E(e,r,o,u,c,p);if(0===h)return 0;for(var f=0,g=-1,y=void 0,v=void 0,m=0;m<h;m++){var _=p[m],x=0===_||1===_?.5:1;s.Yb(t,n,i,a,_)<l||(g<0&&(g=s.lX(e,r,o,u,d),d[1]<d[0]&&g>1&&function(){var t=d[0];d[0]=d[1],d[1]=t}(),y=s.Yb(e,r,o,u,d[0]),g>1&&(v=s.Yb(e,r,o,u,d[1]))),2===g?_<d[0]?f+=y<e?x:-x:_<d[1]?f+=v<y?x:-x:f+=u<v?x:-x:_<d[0]?f+=y<e?x:-x:f+=u<y?x:-x)}return f}(_,x,y[S++],y[S++],y[S++],y[S++],y[S],y[S+1],r,i)||0;_=y[S++],x=y[S++];break;case h.Q:if(n){if(P=_,L=x,R=y[S++],E=y[S++],N=y[S],B=y[S+1],0!==e&&(!(i>L+e)||!(i>E+e)||!(i>B+e))&&(!(i<L-e)||!(i<E-e)||!(i<B-e))&&(!(r>P+e)||!(r>R+e)||!(r>N+e))&&(!(r<P-e)||!(r<R-e)||!(r<N-e))&&(0,s.kh)(P,L,R,E,N,B,r,i,null)<=e/2)return!0}else m+=function(t,e,n,r,i,o,a,u){if(u>e&&u>r&&u>o||u<e&&u<r&&u<o)return 0;var l=s.qY(e,r,o,u,p);if(0===l)return 0;var c=s.gC(e,r,o);if(c>=0&&c<=1){for(var h=0,f=s.k3(e,r,o,c),d=0;d<l;d++){var g=0===p[d]||1===p[d]?.5:1,y=s.k3(t,n,i,p[d]);y<a||(p[d]<c?h+=f<e?g:-g:h+=o<f?g:-g)}return h}var g=0===p[0]||1===p[0]?.5:1,y=s.k3(t,n,i,p[0]);return y<a?0:o<e?g:-g}(_,x,y[S++],y[S++],y[S],y[S+1],r,i)||0;_=y[S++],x=y[S++];break;case h.A:var V=y[S++],H=y[S++],j=y[S++],U=y[S++],W=y[S++],G=y[S++];S+=1;var q=!!(1-y[S++]);o=Math.cos(W)*j+V,g=Math.sin(W)*U+H,z?(b=o,w=g):m+=(0,c.A)(_,x,o,g,r,i);var X=(r-V)*U/j+V;if(n){if(function(t,e,n,r,i,o,a,s,c){if(0===a)return!1;var h=Math.sqrt((s-=t)*s+(c-=e)*c);if(h-a>n||h+a<n)return!1;if(Math.abs(r-i)%l<1e-4)return!0;if(o){var f=r;r=(0,u.n)(i),i=(0,u.n)(f)}else r=(0,u.n)(r),i=(0,u.n)(i);r>i&&(i+=l);var p=Math.atan2(c,s);return p<0&&(p+=l),p>=r&&p<=i||p+l>=r&&p+l<=i}(V,H,U,W,W+G,q,e,X,i))return!0}else m+=function(t,e,n,r,i,o,a,s){if((s-=e)>n||s<-n)return 0;var u=Math.sqrt(n*n-s*s);p[0]=-u,p[1]=u;var l=Math.abs(r-i);if(l<1e-4)return 0;if(l>=f-1e-4){r=0,i=f;var c=o?1:-1;return a>=p[0]+t&&a<=p[1]+t?c:0}if(r>i){var h=r;r=i,i=h}r<0&&(r+=f,i+=f);for(var d=0,g=0;g<2;g++){var y=p[g];if(y+t>a){var v=Math.atan2(s,y),c=o?1:-1;v<0&&(v=f+v),(v>=r&&v<=i||v+f>=r&&v+f<=i)&&(v>Math.PI/2&&v<1.5*Math.PI&&(c=-c),d+=c)}}return d}(V,H,U,W,W+G,q,X,i);_=Math.cos(W+G)*j+V,x=Math.sin(W+G)*U+H;break;case h.R:b=_=y[S++],w=x=y[S++];var Y=y[S++],Z=y[S++];if(o=b+Y,g=w+Z,n){if(a(b,w,o,w,e,r,i)||a(o,w,o,g,e,r,i)||a(o,g,b,g,e,r,i)||a(b,g,b,w,e,r,i))return!0}else m+=(0,c.A)(o,w,o,g,r,i),m+=(0,c.A)(b,g,b,w,r,i);break;case h.Z:if(n){if(a(_,x,b,w,e,r,i))return!0}else m+=(0,c.A)(_,x,b,w,r,i);_=b,x=w}}return n||1e-4>Math.abs(x-w)||(m+=(0,c.A)(_,x,b,w,r,i)||0),0!==m}var y=n(4123),v=n(411),m=n(4035),_=n(3273),x=n(4621),b=(0,y.defaults)({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},i.oN),w={style:(0,y.defaults)({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},i.sW.style)},S=x.Wx.concat(["invisible","culling","z","z2","zlevel","parent"]);let M=function(t){var e;function n(e){return t.call(this,e)||this}return(0,r.__extends)(n,t),n.prototype.update=function(){var e=this;t.prototype.update.call(this);var r=this.style;if(r.decal){var i=this._decalEl=this._decalEl||new n;i.buildPath===n.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0;var o=i.style;for(var a in r)o[a]!==r[a]&&(o[a]=r[a]);o.fill=r.fill?r.decal:null,o.decal=null,o.shadowColor=null,r.strokeFirst&&(o.stroke=null);for(var s=0;s<S.length;++s)i[S[s]]=this[S[s]];i.__dirty|=_.M}else this._decalEl&&(this._decalEl=null)},n.prototype.getDecalElement=function(){return this._decalEl},n.prototype._init=function(e){var n=(0,y.keys)(e);this.shape=this.getDefaultShape();var r=this.getDefaultStyle();r&&this.useStyle(r);for(var i=0;i<n.length;i++){var o=n[i],a=e[o];"style"===o?this.style?(0,y.extend)(this.style,a):this.useStyle(a):"shape"===o?(0,y.extend)(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},n.prototype.getDefaultStyle=function(){return null},n.prototype.getDefaultShape=function(){return{}},n.prototype.canBeInsideText=function(){return this.hasFill()},n.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if((0,y.isString)(t)){var e=(0,v.lum)(t,0);return e>.5?m._S:e>.2?m.tY:m.el}else if(t)return m.el}return m._S},n.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if((0,y.isString)(e)){var n=this.__zr;if(!!(n&&n.isDarkMode())==(0,v.lum)(t,0)<m.ps)return e}},n.prototype.buildPath=function(t,e,n){},n.prototype.pathUpdated=function(){this.__dirty&=~_.Dl},n.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},n.prototype.createPathProxy=function(){this.path=new o.A(!1)},n.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},n.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},n.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var r=!1;this.path||(r=!0,this.createPathProxy());var i=this.path;(r||this.__dirty&_.Dl)&&(i.beginPath(),this.buildPath(i,this.shape,!1),this.pathUpdated()),t=i.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;s=Math.max(s,null==u?4:u)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),r=this.getBoundingRect(),i=this.style;if(t=n[0],e=n[1],r.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=i.lineWidth,s=i.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),g(o,a/s,!0,t,e)))return!0}if(this.hasFill())return g(o,0,!1,t,e)}return!1},n.prototype.dirtyShape=function(){this.__dirty|=_.Dl,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},n.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},n.prototype.animateShape=function(t){return this.animate("shape",t)},n.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},n.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},n.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"==typeof t?n[t]=e:(0,y.extend)(n,t),this.dirtyShape(),this},n.prototype.shapeChanged=function(){return!!(this.__dirty&_.Dl)},n.prototype.createStyle=function(t){return(0,y.createObject)(b,t)},n.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=(0,y.extend)({},this.shape))},n.prototype._applyStateObj=function(e,n,r,i,o,a){t.prototype._applyStateObj.call(this,e,n,r,i,o,a);var s,u=!(n&&i);if(n&&n.shape?o?i?s=n.shape:(s=(0,y.extend)({},r.shape),(0,y.extend)(s,n.shape)):(s=(0,y.extend)({},i?this.shape:r.shape),(0,y.extend)(s,n.shape)):u&&(s=r.shape),s)if(o){this.shape=(0,y.extend)({},this.shape);for(var l={},c=(0,y.keys)(s),h=0;h<c.length;h++){var f=c[h];"object"==typeof s[f]?this.shape[f]=s[f]:l[f]=s[f]}this._transitionState(e,{shape:l},a)}else this.shape=s,this.dirtyShape()},n.prototype._mergeStates=function(e){for(var n,r=t.prototype._mergeStates.call(this,e),i=0;i<e.length;i++){var o=e[i];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(r.shape=n),r},n.prototype.getAnimationStyleProps=function(){return w},n.prototype.isZeroArea=function(){return!1},n.extend=function(t){var e=function(e){function n(n){var r=e.call(this,n)||this;return t.init&&t.init.call(r,n),r}return(0,r.__extends)(n,e),n.prototype.getDefaultStyle=function(){return(0,y.clone)(t.style)},n.prototype.getDefaultShape=function(){return(0,y.clone)(t.shape)},n}(n);for(var i in t)"function"==typeof t[i]&&(e.prototype[i]=t[i]);return e},n.initDefaultProps=void((e=n.prototype).type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=_.M|_.pO|_.Dl),n}(i.Ay)},4290:(t,e,n)=>{"use strict";var r=n(9445).Collapse,i=n(3346).UnmountClosed;t.exports=i,i.Collapse=r,i.UnmountClosed=i},4436:(t,e,n)=>{"use strict";n.d(e,{A:()=>_});var r=n(4123),i=n(6631),o=n(1929),a=n(2660),s=n(6187),u=n(2392),l=n(9942),c=n(514),h=s.$r(),f=function(){var t=(0,s.$r)();return function(e){var n=t(e),r=e.pipelineContext,i=!!n.large,o=!!n.progressiveRender,a=n.large=!!(r&&r.large),s=n.progressiveRender=!!(r&&r.progressiveRender);return(i!==a||o!==s)&&"reset"}}(),p=function(){function t(){this.group=new i.A,this.uid=o.$Q("viewChart"),this.renderTask=(0,l.U)({plan:y,reset:v}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,r){},t.prototype.highlight=function(t,e,n,r){var i=t.getData(r&&r.dataType);i&&g(i,r,"emphasis")},t.prototype.downplay=function(t,e,n,r){var i=t.getData(r&&r.dataType);i&&g(i,r,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,r){this.render(t,e,n,r)},t.prototype.updateLayout=function(t,e,n,r){this.render(t,e,n,r)},t.prototype.updateVisual=function(t,e,n,r){this.render(t,e,n,r)},t.prototype.eachRendered=function(t){(0,c.o4)(this.group,t)},t.markUpdateMethod=function(t,e){h(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function d(t,e,n){t&&(0,u.u6)(t)&&("emphasis"===e?u.HY:u.SD)(t,n)}function g(t,e,n){var i=s.le(t,e),o=e&&null!=e.highlightKey?(0,u._n)(e.highlightKey):null;null!=i?(0,r.each)(s.qB(i),function(e){d(t.getItemGraphicEl(e),n,o)}):t.eachItemGraphicEl(function(t){d(t,n,o)})}function y(t){return f(t.model)}function v(t){var e=t.model,n=t.ecModel,r=t.api,i=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=i&&h(i).updateMethod,u=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==u&&a[u](e,n,r,i),m[u]}a.gq(p,["dispose"]),a.tQ(p);var m={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};let _=p},4524:(t,e,n)=>{"use strict";n.d(e,{YA:()=>p,ad:()=>f,dV:()=>h,vs:()=>d});var r=n(4123),i=n(7669),o=n(3607),a=n(2261),s=r.each,u=["left","right","top","bottom","width","height"],l=[["width","left","right"],["height","top","bottom"]];function c(t,e,n,r,i){var o=0,a=0;null==r&&(r=1/0),null==i&&(i=1/0);var s=0;e.eachChild(function(u,l){var c,h,f=u.getBoundingRect(),p=e.childAt(l+1),d=p&&p.getBoundingRect();if("horizontal"===t){var g=f.width+(d?-d.x+f.x:0);(c=o+g)>r||u.newline?(o=0,c=g,a+=s+n,s=f.height):s=Math.max(s,f.height)}else{var y=f.height+(d?-d.y+f.y:0);(h=a+y)>i||u.newline?(o+=s+n,a=0,h=y,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=o,u.y=a,u.markRedraw(),"horizontal"===t?o=c+n:a=h+n)})}function h(t,e,n){n=a.QX(n||0);var r=e.width,s=e.height,u=(0,o.lo)(t.left,r),l=(0,o.lo)(t.top,s),c=(0,o.lo)(t.right,r),h=(0,o.lo)(t.bottom,s),f=(0,o.lo)(t.width,r),p=(0,o.lo)(t.height,s),d=n[2]+n[0],g=n[1]+n[3],y=t.aspect;switch(isNaN(f)&&(f=r-c-g-u),isNaN(p)&&(p=s-h-d-l),null!=y&&(isNaN(f)&&isNaN(p)&&(y>r/s?f=.8*r:p=.8*s),isNaN(f)&&(f=y*p),isNaN(p)&&(p=f/y)),isNaN(u)&&(u=r-c-f-g),isNaN(l)&&(l=s-h-p-d),t.left||t.right){case"center":u=r/2-f/2-n[3];break;case"right":u=r-f-g}switch(t.top||t.bottom){case"middle":case"center":l=s/2-p/2-n[0];break;case"bottom":l=s-p-d}u=u||0,l=l||0,isNaN(f)&&(f=r-g-u-(c||0)),isNaN(p)&&(p=s-d-l-(h||0));var v=new i.A(u+n[3],l+n[0],f,p);return v.margin=n,v}function f(t){var e=t.layoutMode||t.constructor.layoutMode;return r.isObject(e)?e:e?{type:e}:null}function p(t,e,n){var i=n&&n.ignoreSize;r.isArray(i)||(i=[i,i]);var o=u(l[0],0),a=u(l[1],1);function u(n,r){var o={},a=0,u={},l=0;if(s(n,function(e){u[e]=t[e]}),s(n,function(t){c(e,t)&&(o[t]=u[t]=e[t]),h(o,t)&&a++,h(u,t)&&l++}),i[r])return h(e,n[1])?u[n[2]]=null:h(e,n[2])&&(u[n[1]]=null),u;if(2===l||!a)return u;if(a>=2)return o;for(var f=0;f<n.length;f++){var p=n[f];if(!c(o,p)&&c(t,p)){o[p]=t[p];break}}return o}function c(t,e){return t.hasOwnProperty(e)}function h(t,e){return null!=t[e]&&"auto"!==t[e]}function f(t,e,n){s(t,function(t){e[t]=n[t]})}f(l[0],t,o),f(l[1],t,a)}function d(t){var e,n;return e={},(n=t)&&e&&s(u,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e}r.curry(c,"vertical"),r.curry(c,"horizontal")},4566:(t,e,n)=>{"use strict";n.d(e,{$2:()=>u,Cv:()=>l,MD:()=>S,OH:()=>x,OS:()=>_,Pt:()=>m,WG:()=>A,XP:()=>h,Xu:()=>w,Z1:()=>M,bn:()=>b,dX:()=>g,eQ:()=>p,nV:()=>f,sL:()=>v,sZ:()=>d,si:()=>y});var r=n(4123),i=n(411),o=n(7170),a=n(5376).Buffer,s=Math.round;function u(t){var e;if(t&&"transparent"!==t){if("string"==typeof t&&t.indexOf("rgba")>-1){var n=(0,i.parse)(t);n&&(t="rgb("+n[0]+","+n[1]+","+n[2]+")",e=n[3])}}else t="none";return{color:t,opacity:null==e?1:e}}function l(t){return t<1e-4&&t>-1e-4}function c(t){return s(1e3*t)/1e3}function h(t){return s(1e4*t)/1e4}function f(t){return"matrix("+c(t[0])+","+c(t[1])+","+c(t[2])+","+c(t[3])+","+h(t[4])+","+h(t[5])+")"}var p={left:"start",right:"end",center:"middle",middle:"middle"};function d(t,e,n){return"top"===n?t+=e/2:"bottom"===n&&(t-=e/2),t}function g(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function y(t){var e=t.style,n=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),n[0],n[1]].join(",")}function v(t){return t&&!!t.image}function m(t){return v(t)||t&&!!t.svgElement}function _(t){return"linear"===t.type}function x(t){return"radial"===t.type}function b(t){return t&&("linear"===t.type||"radial"===t.type)}function w(t){return"url(#"+t+")"}function S(t){var e=t.getGlobalScale();return Math.max(Math.ceil(Math.log(Math.max(e[0],e[1]))/Math.log(10)),1)}function M(t){var e=t.x||0,n=t.y||0,i=(t.rotation||0)*r.RADIAN_TO_DEGREE,o=(0,r.retrieve2)(t.scaleX,1),a=(0,r.retrieve2)(t.scaleY,1),u=t.skewX||0,l=t.skewY||0,c=[];return(e||n)&&c.push("translate("+e+"px,"+n+"px)"),i&&c.push("rotate("+i+")"),(1!==o||1!==a)&&c.push("scale("+o+","+a+")"),(u||l)&&c.push("skew("+s(u*r.RADIAN_TO_DEGREE)+"deg, "+s(l*r.RADIAN_TO_DEGREE)+"deg)"),c.join(" ")}var A=o.A.hasGlobalWindow&&(0,r.isFunction)(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:void 0!==a?function(t){return a.from(t).toString("base64")}:function(t){return null}},4605:(t,e,n)=>{"use strict";n.d(e,{F:()=>p,A:()=>_});var r={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return .5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*(t-=1))*Math.sin(2*Math.PI*(t-e)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin(2*Math.PI*(t-e)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1)?-.5*(n*Math.pow(2,10*(t-=1))*Math.sin(2*Math.PI*(t-e)/.4)):n*Math.pow(2,-10*(t-=1))*Math.sin(2*Math.PI*(t-e)/.4)*.5+1},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){return(t*=2)<1?t*t*(3.5949095*t-2.5949095)*.5:.5*((t-=2)*t*(3.5949095*t+2.5949095)+2)},bounceIn:function(t){return 1-r.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*r.bounceIn(2*t):.5*r.bounceOut(2*t-1)+.5}},i=n(4123),o=n(65),a=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||i.noop,this.ondestroy=t.ondestroy||i.noop,this.onrestart=t.onrestart||i.noop,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var n=this._life,r=t-this._startTime-this._pausedTime,i=r/n;i<0&&(i=0),i=Math.min(i,1);var o=this.easingFunc,a=o?o(i):i;if(this.onframe(a),1===i)if(!this.loop)return!0;else this._startTime=t-r%n,this._pausedTime=0,this.onrestart();return!1},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=(0,i.isFunction)(t)?t:r[t]||(0,o.w)(t)},t}(),s=n(411),u=n(4566),l=Array.prototype.slice;function c(t,e,n,r){for(var i,o=e.length,a=0;a<o;a++)t[a]=(i=e[a],(n[a]-i)*r+i);return t}function h(t,e,n,r){for(var i=e.length,o=0;o<i;o++)t[o]=e[o]+n[o]*r;return t}function f(t,e,n,r){for(var i=e.length,o=i&&e[0].length,a=0;a<i;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*r}return t}function p(t){if((0,i.isArrayLike)(t)){var e=t.length;if((0,i.isArrayLike)(t[0])){for(var n=[],r=0;r<e;r++)n.push(l.call(t[r]));return n}return l.call(t)}return t}function d(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function g(t){return 4===t||5===t}function y(t){return 1===t||2===t}var v=[0,0,0,0],m=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var a=this.keyframes,l=a.length,c=!1,h=6,f=e;if((0,i.isArrayLike)(e)){var p=(0,i.isArrayLike)(e&&e[0])?2:1;h=p,(1!==p||(0,i.isNumber)(e[0]))&&(2!==p||(0,i.isNumber)(e[0][0]))||(c=!0)}else if((0,i.isNumber)(e)&&!(0,i.eqNaN)(e))h=0;else if((0,i.isString)(e))if(isNaN(+e)){var d=s.parse(e);d&&(f=d,h=3)}else h=0;else if((0,i.isGradientObject)(e)){var g=(0,i.extend)({},f);g.colorStops=(0,i.map)(e.colorStops,function(t){return{offset:t.offset,color:s.parse(t.color)}}),(0,u.OS)(e)?h=4:(0,u.OH)(e)&&(h=5),f=g}0===l?this.valType=h:(h!==this.valType||6===h)&&(c=!0),this.discrete=this.discrete||c;var y={time:t,value:f,rawValue:e,percent:0};return n&&(y.easing=n,y.easingFunc=(0,i.isFunction)(n)?n:r[n]||(0,o.w)(n)),a.push(y),y},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort(function(t,e){return t.time-e.time});for(var r=this.valType,i=n.length,o=n[i-1],a=this.discrete,s=y(r),u=g(r),c=0;c<i;c++){var p=n[c],d=p.value,v=o.value;p.percent=p.time/t,!a&&(s&&c!==i-1?function(t,e,n){if(t.push&&e.push){var r=t.length,i=e.length;if(r!==i)if(r>i)t.length=i;else for(var o=r;o<i;o++)t.push(1===n?e[o]:l.call(e[o]));for(var a=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var s=0;s<a;s++)isNaN(t[o][s])&&(t[o][s]=e[o][s])}}(d,v,r):u&&function(t,e){for(var n=t.length,r=e.length,i=n>r?e:t,o=Math.min(n,r),a=i[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,r);s++)i.push({offset:a.offset,color:a.color.slice()})}(d.colorStops,v.colorStops))}if(!a&&5!==r&&e&&this.needsAnimate()&&e.needsAnimate()&&r===e.valType&&!e._finished){this._additiveTrack=e;for(var m=n[0].value,c=0;c<i;c++)0===r?n[c].additiveValue=n[c].value-m:3===r?n[c].additiveValue=h([],n[c].value,m,-1):y(r)&&(n[c].additiveValue=1===r?h([],n[c].value,m,-1):f([],n[c].value,m,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",u=this.valType,l=this.keyframes,h=l.length,f=this.propName,p=3===u,m=this._lastFr,_=Math.min;if(1===h)r=o=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){for(n=_(m+1,h-1);n>=0&&!(l[n].percent<=e);n--);n=_(n,h-2)}else{for(n=m;n<h&&!(l[n].percent>e);n++);n=_(n-1,h-2)}o=l[n+1],r=l[n]}if(r&&o){this._lastFr=n,this._lastFrP=e;var x=o.percent-r.percent,b=0===x?1:_((e-r.percent)/x,1);o.easingFunc&&(b=o.easingFunc(b));var w=a?this._additiveValue:p?v:t[f];if((y(u)||p)&&!w&&(w=this._additiveValue=[]),this.discrete)t[f]=b<1?r.rawValue:o.rawValue;else if(y(u))1===u?c(w,r[s],o[s],b):function(t,e,n,r){for(var i=e.length,o=i&&e[0].length,a=0;a<i;a++){t[a]||(t[a]=[]);for(var s,u=0;u<o;u++)t[a][u]=(s=e[a][u],(n[a][u]-s)*r+s)}}(w,r[s],o[s],b);else if(g(u)){var S,M,A,T,k,C=r[s],I=o[s],D=4===u;(t[f]={type:D?"linear":"radial",x:(S=C.x,(I.x-S)*b+S),y:(M=C.y,(I.y-M)*b+M),colorStops:(0,i.map)(C.colorStops,function(t,e){var n,r=I.colorStops[e];return{offset:(n=t.offset,(r.offset-n)*b+n),color:d(c([],t.color,r.color,b))}}),global:I.global},D)?(t[f].x2=(A=C.x2,(I.x2-A)*b+A),t[f].y2=(T=C.y2,(I.y2-T)*b+T)):t[f].r=(k=C.r,(I.r-k)*b+k)}else if(p)c(w,r[s],o[s],b),a||(t[f]=d(w));else{var O,P=(O=r[s],(o[s]-O)*b+O);a?this._additiveValue=P:t[f]=P}a&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,r=this._additiveValue;0===e?t[n]=t[n]+r:3===e?(s.parse(t[n],v),h(v,v,r,1),t[n]=d(v)):1===e?h(t[n],t[n],r,1):2===e&&f(t[n],t[n],r,1)},t}();let _=function(){function t(t,e,n,r){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&r)return void(0,i.logError)("Can' use additive animation on looped animation.");this._additiveAnimators=r,this._allowDiscrete=n}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,(0,i.keys)(e),n)},t.prototype.whenWithKeys=function(t,e,n,r){for(var i=this._tracks,o=0;o<n.length;o++){var a=n[o],s=i[a];if(!s){s=i[a]=new m(a);var u=void 0,l=this._getAdditiveTrack(a);if(l){var c=l.keyframes,h=c[c.length-1];u=h&&h.value,3===l.valType&&u&&(u=d(u))}else u=this._target[a];if(null==u)continue;t>0&&s.addKeyframe(0,p(u),r),this._trackKeys.push(a)}s.addKeyframe(t,p(e[a]),r)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var r=0;r<n.length;r++){var i=n[r].getTrack(t);i&&(e=i)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],r=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var o=this._trackKeys[i],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,c=l.length;if(s.prepare(r,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[c-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else n.push(s)}if(n.length||this._force){var f=new a({life:r,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var r=e._additiveAnimators;if(r){for(var i=!1,o=0;o<r.length;o++)if(r[o]._clip){i=!0;break}i||(e._additiveAnimators=null)}for(var o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(var o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=f,this.animation&&this.animation.addClip(f),t&&f.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return(0,i.map)(this._trackKeys,function(e){return t._tracks[e]})},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,r=this._trackKeys,i=0;i<t.length;i++){var o=n[t[i]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,i=0;i<r.length;i++)if(!n[r[i]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var r=0;r<e.length;r++){var i=e[r],o=this._tracks[i];if(!(!o||o.isFinished())){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[i]=p(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||(0,i.keys)(t);for(var n=0;n<e.length;n++){var r=e[n],o=this._tracks[r];if(o){var a=o.keyframes;if(a.length>1){var s=a.pop();o.addKeyframe(s.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack())}}}},t}()},4621:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>d,IT:()=>p,Wx:()=>f});var r=n(8346),i=n(1148),o=r.identity;function a(t){return t>5e-5||t<-5e-5}var s=[],u=[],l=r.create(),c=Math.abs,h=function(){var t;function e(){}return e.prototype.getLocalTransform=function(t){return e.getLocalTransform(this,t)},e.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},e.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},e.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},e.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},e.prototype.needLocalTransform=function(){return a(this.rotation)||a(this.x)||a(this.y)||a(this.scaleX-1)||a(this.scaleY-1)||a(this.skewX)||a(this.skewY)},e.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;if(!(e||t)){n&&(o(n),this.invTransform=null);return}n=n||r.create(),e?this.getLocalTransform(n):o(n),t&&(e?r.mul(n,t,n):r.copy(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)},e.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(s);var n=s[0]<0?-1:1,i=s[1]<0?-1:1,o=((s[0]-n)*e+n)/s[0]||0,a=((s[1]-i)*e+i)/s[1]||0;t[0]*=o,t[1]*=o,t[2]*=a,t[3]*=a}this.invTransform=this.invTransform||r.create(),r.invert(this.invTransform,t)},e.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},e.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],r=Math.atan2(t[1],t[0]),i=Math.PI/2+r-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(i),e=Math.sqrt(e),this.skewX=i,this.skewY=0,this.rotation=-r,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},e.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||r.create(),r.mul(u,t.invTransform,e),e=u);var n=this.originX,i=this.originY;(n||i)&&(l[4]=n,l[5]=i,r.mul(u,e,l),u[4]-=n,u[5]-=i,e=u),this.setLocalTransform(e)}},e.prototype.getGlobalScale=function(t){var e=this.transform;return(t=t||[],e)?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},e.prototype.transformCoordToLocal=function(t,e){var n=[t,e],r=this.invTransform;return r&&i.applyTransform(n,n,r),n},e.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],r=this.transform;return r&&i.applyTransform(n,n,r),n},e.prototype.getLineScale=function(){var t=this.transform;return t&&c(t[0]-1)>1e-10&&c(t[3]-1)>1e-10?Math.sqrt(c(t[0]*t[3]-t[2]*t[1])):1},e.prototype.copyTransform=function(t){p(this,t)},e.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,o=t.scaleX,a=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,c=t.x,h=t.y,f=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||i||s||u){var d=n+s,g=i+u;e[4]=-d*o-f*g*a,e[5]=-g*a-p*d*o}else e[4]=e[5]=0;return e[0]=o,e[3]=a,e[1]=p*o,e[2]=f*a,l&&r.rotate(e,e,l),e[4]+=n+c,e[5]+=i+h,e},e.initDefaultProps=void((t=e.prototype).scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0),e}(),f=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function p(t,e){for(var n=0;n<f.length;n++){var r=f[n];t[r]=e[r]}}let d=h},4670:(t,e,n)=>{"use strict";n.d(e,{v5:()=>x});var r=n(4123),i=n(4271),o=n(3948),a=n(7821),s=n(6586),u=n(514),l=n(7669),c=n(6847),h=i.Ay.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,r=e.cy,i=e.width/2,o=e.height/2;t.moveTo(n,r-o),t.lineTo(n+i,r+o),t.lineTo(n-i,r+o),t.closePath()}}),f=i.Ay.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,r=e.cy,i=e.width/2,o=e.height/2;t.moveTo(n,r-o),t.lineTo(n+i,r),t.lineTo(n,r+o),t.lineTo(n-i,r),t.closePath()}}),p=i.Ay.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,r=e.y,i=e.width/5*3,o=Math.max(i,e.height),a=i/2,s=a*a/(o-a),u=r-o+a+s,l=Math.asin(s/a),c=Math.cos(l)*a,h=Math.sin(l),f=Math.cos(l),p=.6*a,d=.7*a;t.moveTo(n-c,u+s),t.arc(n,u,a,Math.PI-l,2*Math.PI+l),t.bezierCurveTo(n+c-h*p,u+s+f*p,n,r-d,n,r),t.bezierCurveTo(n,r-d,n-c+h*p,u+s+f*p,n-c,u+s),t.closePath()}}),d=i.Ay.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,r=e.width,i=e.x,o=e.y,a=r/3*2;t.moveTo(i,o),t.lineTo(i+a,o+n),t.lineTo(i,o+n/4*3),t.lineTo(i-a,o+n),t.lineTo(i,o),t.closePath()}}),g={line:o.A,rect:a.A,roundRect:a.A,square:a.A,circle:s.A,diamond:f,pin:p,arrow:d,triangle:h},y={line:function(t,e,n,r,i){i.x1=t,i.y1=e+r/2,i.x2=t+n,i.y2=e+r/2},rect:function(t,e,n,r,i){i.x=t,i.y=e,i.width=n,i.height=r},roundRect:function(t,e,n,r,i){i.x=t,i.y=e,i.width=n,i.height=r,i.r=Math.min(n,r)/4},square:function(t,e,n,r,i){var o=Math.min(n,r);i.x=t,i.y=e,i.width=o,i.height=o},circle:function(t,e,n,r,i){i.cx=t+n/2,i.cy=e+r/2,i.r=Math.min(n,r)/2},diamond:function(t,e,n,r,i){i.cx=t+n/2,i.cy=e+r/2,i.width=n,i.height=r},pin:function(t,e,n,r,i){i.x=t+n/2,i.y=e+r/2,i.width=n,i.height=r},arrow:function(t,e,n,r,i){i.x=t+n/2,i.y=e+r/2,i.width=n,i.height=r},triangle:function(t,e,n,r,i){i.cx=t+n/2,i.cy=e+r/2,i.width=n,i.height=r}},v={};(0,r.each)(g,function(t,e){v[e]=new t});var m=i.Ay.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var r=(0,c.X4)(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(r.y=n.y+.4*n.height),r},buildPath:function(t,e,n){var r=e.symbolType;if("none"!==r){var i=v[r];i||(i=v[r="rect"]),y[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n)}}});function _(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function x(t,e,n,r,i,o,a){var s,c=0===t.indexOf("empty");return c&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?u.Gs(t.slice(8),new l.A(e,n,r,i),a?"center":"cover"):0===t.indexOf("path://")?u.cP(t.slice(7),{},new l.A(e,n,r,i),a?"center":"cover"):new m({shape:{symbolType:t,x:e,y:n,width:r,height:i}})).__isEmptyBrush=c,s.setColor=_,o&&s.setColor(o),s}},4673:(t,e,n)=>{"use strict";n.d(e,{Tc:()=>u,eS:()=>f,fg:()=>l,io:()=>c,j_:()=>h});var r=n(4123),i=n(6187),o=n(8535),a=(0,i.$r)(),s={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},u=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=h(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return(0,r.retrieve2)(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=(0,o.O0)(this.source),n=!f(t),r="",i=[],a=0,u=0;a<t;a++){var l=void 0,c=void 0,h=void 0,p=this.dimensions[u];if(p&&p.storeDimIndex===a)l=e?p.name:null,c=p.type,h=p.ordinalMeta,u++;else{var d=this.getSourceDimension(a);d&&(l=e?d.name:null,c=d.type)}i.push({property:l,type:c,ordinalMeta:h}),!e||null==l||p&&p.isCalculationCoord||(r+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),r+="$",r+=s[c]||"f",h&&(r+=h.uid),r+="$"}var g=this.source;return{dimensions:i,hash:[g.seriesLayoutBy,g.startIndex,r].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var r=void 0,i=this.dimensions[n];if(i&&i.storeDimIndex===e)i.isCalculationCoord||(r=i.name),n++;else{var o=this.getSourceDimension(e);o&&(r=o.name)}t.push(r)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function l(t){return t instanceof u}function c(t){for(var e=(0,r.createHashMap)(),n=0;n<(t||[]).length;n++){var i=t[n],o=(0,r.isObject)(i)?i.name:i;null!=o&&null==e.get(o)&&e.set(o,n)}return e}function h(t){var e=a(t);return e.dimNameMap||(e.dimNameMap=c(t.dimensionsDefine))}function f(t){return t>30}},4948:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>o});var r=function(t){this.value=t},i=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new r(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}();let o=function(){function t(t){this._list=new i,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,i=this._map,o=null;if(null==i[t]){var a=n.len(),s=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var u=n.head;n.remove(u),delete i[u.key],o=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new r(e),s.key=t,n.insertEntry(s),i[t]=s}return o},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}()},4958:(t,e,n)=>{"use strict";n.d(e,{Me:()=>f,ot:()=>l,oq:()=>u});var r=n(7170),i=Math.log(2);function o(t,e,n,r,a,s){var u=r+"-"+a,l=t.length;if(s.hasOwnProperty(u))return s[u];if(1===e){var c=Math.round(Math.log((1<<l)-1&~a)/i);return t[n][c]}for(var h=r|1<<n,f=n+1;r&1<<f;)f++;for(var p=0,d=0,g=0;d<l;d++){var y=1<<d;!(y&a)&&(p+=(g%2?-1:1)*t[n][d]*o(t,e-1,f,h,a|y,s),g++)}return s[u]=p,p}function a(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],r={},i=o(n,8,0,0,0,r);if(0!==i){for(var a=[],s=0;s<8;s++)for(var u=0;u<8;u++)null==a[u]&&(a[u]=0),a[u]+=((s+u)%2?-1:1)*o(n,7,+(0===s),1<<s,1<<u,r)/i*e[s];return function(t,e,n){var r=e*a[6]+n*a[7]+1;t[0]=(e*a[0]+n*a[1]+a[2])/r,t[1]=(e*a[3]+n*a[4]+a[5])/r}}}var s="___zrEVENTSAVED";function u(t,e,n,i,o){if(e.getBoundingClientRect&&r.A.domSupported&&!l(e)){var u=e[s]||(e[s]={}),c=function(t,e,n){for(var r=n?"invTrans":"trans",i=e[r],o=e.srcCoords,s=[],u=[],l=!0,c=0;c<4;c++){var h=t[c].getBoundingClientRect(),f=2*c,p=h.left,d=h.top;s.push(p,d),l=l&&o&&p===o[f]&&d===o[f+1],u.push(t[c].offsetLeft,t[c].offsetTop)}return l&&i?i:(e.srcCoords=s,e[r]=n?a(u,s):a(s,u))}(function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var r=["left","right"],i=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,u=o%2,l=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",r[u]+":0",i[l]+":0",r[1-u]+":auto",i[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,u),u,o);if(c)return c(t,n,i),!0}return!1}function l(t){return"CANVAS"===t.nodeName.toUpperCase()}var c=/([&<>"'])/g,h={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function f(t){return null==t?"":(t+"").replace(c,function(t,e){return h[e]})}},5225:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createSensor=void 0;var r=n(7076),i=n(1425);e.createSensor="undefined"!=typeof ResizeObserver?i.createSensor:r.createSensor},5327:(t,e,n)=>{"use strict";n.d(e,{A:()=>k});var r,i,o,a,s,u,l,c=n(4123),h=n(1613),f=n(442),p=n(2170),d=n(3213),g=n(9486),y=n(7439),v=n(6187),m=n(2663),_=n(8535),x=n(2899),b=n(4673),w=c.isObject,S=c.map,M="undefined"==typeof Int32Array?Array:Int32Array,A=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],T=["_approximateExtent"];let k=function(){function t(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var n,r=!1;(0,b.fg)(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"];for(var i={},o=[],a={},s=!1,u={},l=0;l<n.length;l++){var h=n[l],f=c.isString(h)?new g.A({name:h}):h instanceof g.A?h:new g.A(h),p=f.name;f.type=f.type||"float",f.coordDim||(f.coordDim=p,f.coordDimIndex=0);var d=f.otherDims=f.otherDims||{};o.push(p),i[p]=f,null!=u[p]&&(s=!0),f.createInvertedIndices&&(a[p]=[]),0===d.itemName&&(this._nameDimIdx=l),0===d.itemId&&(this._idDimIdx=l),r&&(f.storeDimIndex=l)}if(this.dimensions=o,this._dimInfos=i,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var y=this._dimIdxToName=c.createHashMap();c.each(o,function(t){y.set(i[t].storeDimIndex,t)})}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var r=this._schema.getSourceDimension(e);if(r)return r.name},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return -1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(c.isNumber(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||0>this._schema.getSourceDimensionIndex(t)))return+t},t.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var r=n.encode[t];return r?r[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var r,i=this;if(t instanceof x.Ay&&(r=t),!r){var o=this.dimensions,a=(0,_.tP)(t)||c.isArrayLike(t)?new p.d1(t,o.length):t;r=new x.Ay;var s=S(o,function(t){return{type:i._dimInfos[t].type,property:t}});r.initData(a,s,n)}this._store=r,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,r.count()),this._dimSummary=(0,d.l)(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e&&e.length),r=n.start,i=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=r;a<i;a++){var s=a-r;this._nameList[a]=e[s],o&&l(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var r=this._dimInfos[e[n]];r.ordinalMeta&&t.collectOrdinalMeta(r.storeDimIndex,r.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==y.XO&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,o=this._idList;if(n.getSource().sourceFormat===y.mK&&!n.pure)for(var a=[],s=t;s<e;s++){var u=n.getItem(s,a);if(!this.hasItemOption&&(0,v.zu)(u)&&(this.hasItemOption=!0),u){var c=u.name;null==i[s]&&null!=c&&(i[s]=(0,v.vS)(c,null));var h=u.id;null==o[s]&&null!=h&&(o[s]=(0,v.vS)(h,null))}}if(this._shouldMakeIdFromName())for(var s=t;s<e;s++)l(this,s);r(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){w(t)?c.extend(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=o(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),r=this._store.getOrdinalMeta(t);return r?r.categories[n]:n},t.prototype.getId=function(t){return i(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,r=this._dimInfos[t];if(r)return n.get(r.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,r=this._dimInfos[t];if(r)return n.getByRawIndex(r.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,r=this._store;return c.isArray(t)?r.getValues(S(t,function(t){return n._getStoreDimIndex(t)}),e):r.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,r=e.length;n<r;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return -1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],r=n&&n[e];return null==r||isNaN(r)?-1:r},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){c.isFunction(t)&&(n=e,e=t,t=[]);var r=n||this,i=S(a(t),this._getStoreDimIndex,this);this._store.each(i,r?c.bind(e,r):e)},t.prototype.filterSelf=function(t,e,n){c.isFunction(t)&&(n=e,e=t,t=[]);var r=n||this,i=S(a(t),this._getStoreDimIndex,this);return this._store=this._store.filter(i,r?c.bind(e,r):e),this},t.prototype.selectRange=function(t){var e=this,n={},r=c.keys(t),i=[];return c.each(r,function(r){var o=e._getStoreDimIndex(r);n[o]=t[r],i.push(o)}),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){c.isFunction(t)&&(n=e,e=t,t=[]),n=n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},t.prototype.map=function(t,e,n,r){var i=n||r||this,o=S(a(t),this._getStoreDimIndex,this),s=u(this);return s._store=this._store.map(o,i?c.bind(e,i):e),s},t.prototype.modify=function(t,e,n,r){var i=n||r||this,o=S(a(t),this._getStoreDimIndex,this);this._store.modify(o,i?c.bind(e,i):e)},t.prototype.downSample=function(t,e,n,r){var i=u(this);return i._store=this._store.downSample(this._getStoreDimIndex(t),e,n,r),i},t.prototype.minmaxDownSample=function(t,e){var n=u(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},t.prototype.lttbDownSample=function(t,e){var n=u(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new h.A(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new f.A(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(e){return i(t,e)},function(t){return i(e,t)})},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},w(t)?c.extend(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],r=n&&n[e];return null==r?this.getVisual(e):r},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,r=n[t];r||(r=n[t]={});var i=r[e];return null==i&&(i=this.getVisual(e),c.isArray(i)?i=i.slice():w(i)&&(i=c.extend({},i)),r[e]=i),i},t.prototype.setItemVisual=function(t,e,n){var r=this._itemVisuals[t]||{};this._itemVisuals[t]=r,w(e)?c.extend(r,e):r[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){w(t)?c.extend(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?c.extend(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){var n=this.hostModel&&this.hostModel.seriesIndex;(0,m.a)(n,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){c.each(this._graphicEls,function(n,r){n&&t&&t.call(e,n,r)})},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:S(this.dimensions,this._getDimInfo,this),this.hostModel)),s(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];c.isFunction(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(c.slice(arguments)))})},t.internalField=void(r=function(t){var e=t._invertedIndicesMap;c.each(e,function(n,r){var i=t._dimInfos[r],o=i.ordinalMeta,a=t._store;if(o){n=e[r]=new M(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(var s=0;s<a.count();s++)n[a.get(i.storeDimIndex,s)]=s}})},o=function(t,e,n){return(0,v.vS)(t._getCategory(e,n),null)},i=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=o(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},a=function(t){return c.isArray(t)||(t=null!=t?[t]:[]),t},u=function(e){var n=new t(e._schema?e._schema:S(e.dimensions,e._getDimInfo,e),e.hostModel);return s(n,e),n},s=function(t,e){c.each(A.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods,c.each(T,function(n){t[n]=c.clone(e[n])}),t._calculationInfo=c.extend({},e._calculationInfo)},l=function(t,e){var n=t._nameList,r=t._idList,i=t._nameDimIdx,a=t._idDimIdx,s=n[e],u=r[e];if(null==s&&null!=i&&(n[e]=s=o(t,i,e)),null==u&&null!=a&&(r[e]=u=o(t,a,e)),null==u&&null!=s){var l=t._nameRepeatCount,c=l[s]=(l[s]||0)+1;u=s,c>1&&(u+="__ec__"+c),r[e]=u}}),t}()},5376:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=u(t),n=e[0],r=e[1];return(n+r)*3/4-r},e.toByteArray=function(t){var e,n,o=u(t),a=o[0],s=o[1],l=new i((a+s)*3/4-s),c=0,h=s>0?a-4:a;for(n=0;n<h;n+=4)e=r[t.charCodeAt(n)]<<18|r[t.charCodeAt(n+1)]<<12|r[t.charCodeAt(n+2)]<<6|r[t.charCodeAt(n+3)],l[c++]=e>>16&255,l[c++]=e>>8&255,l[c++]=255&e;return 2===s&&(e=r[t.charCodeAt(n)]<<2|r[t.charCodeAt(n+1)]>>4,l[c++]=255&e),1===s&&(e=r[t.charCodeAt(n)]<<10|r[t.charCodeAt(n+1)]<<4|r[t.charCodeAt(n+2)]>>2,l[c++]=e>>8&255,l[c++]=255&e),l},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],a=0,s=r-i;a<s;a+=16383)o.push(function(t,e,r){for(var i,o=[],a=e;a<r;a+=3)i=(t[a]<<16&0xff0000)+(t[a+1]<<8&65280)+(255&t[a+2]),o.push(n[i>>18&63]+n[i>>12&63]+n[i>>6&63]+n[63&i]);return o.join("")}(t,a,a+16383>s?s:a+16383));return 1===i?o.push(n[(e=t[r-1])>>2]+n[e<<4&63]+"=="):2===i&&o.push(n[(e=(t[r-2]<<8)+t[r-1])>>10]+n[e>>4&63]+n[e<<2&63]+"="),o.join("")};for(var n=[],r=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,s=o.length;a<s;++a)n[a]=o[a],r[o.charCodeAt(a)]=a;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");-1===n&&(n=e);var r=n===e?0:4-n%4;return[n,r]}r[45]=62,r[95]=63},72:function(t,e,n){"use strict";var r=n(675),i=n(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,s.prototype),e}function s(t,e,n){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return u(t,e,n)}function u(t,e,n){if("string"==typeof t){var r=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!s.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|p(r,i),u=a(o),l=u.write(r,i);return l!==o&&(u=u.slice(0,l)),u}if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(I(t,ArrayBuffer)||t&&I(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(I(t,SharedArrayBuffer)||t&&I(t.buffer,SharedArrayBuffer)))return function(t,e,n){var r;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),s.prototype),r}(t,e,n);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var c=t.valueOf&&t.valueOf();if(null!=c&&c!==t)return s.from(c,e,n);var d=function(t){if(s.isBuffer(t)){var e=0|f(t.length),n=a(e);return 0===n.length||t.copy(n,0,0,e),n}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?a(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(d)return d;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return s.from(t[Symbol.toPrimitive]("string"),e,n);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return l(t),a(t<0?0:0|f(t))}function h(t){for(var e=t.length<0?0:0|f(t.length),n=a(e),r=0;r<e;r+=1)n[r]=255&t[r];return n}e.Buffer=s,e.SlowBuffer=function(t){return+t!=t&&(t=0),s.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,s.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(t,e,n){return u(t,e,n)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(t,e,n){return(l(t),t<=0)?a(t):void 0!==e?"string"==typeof n?a(t).fill(e,n):a(t).fill(e):a(t)},s.allocUnsafe=function(t){return c(t)},s.allocUnsafeSlow=function(t){return c(t)};function f(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(s.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||I(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return A(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return k(t).length;default:if(i)return r?-1:A(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,n){var i,o,a,s=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===n||n>this.length)&&(n=this.length),n<=0||(n>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=e;o<n;++o)i+=D[t[o]];return i}(this,e,n);case"utf8":case"utf-8":return m(this,e,n);case"ascii":return function(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}(this,e,n);case"latin1":case"binary":return function(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}(this,e,n);case"base64":return i=this,o=e,a=n,0===o&&a===i.length?r.fromByteArray(i):r.fromByteArray(i.slice(o,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,n){for(var r=t.slice(e,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}(this,e,n);default:if(s)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),s=!0}}function g(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function y(t,e,n,r,i){var o;if(0===t.length)return -1;if("string"==typeof n?(r=n,n=0):n>0x7fffffff?n=0x7fffffff:n<-0x80000000&&(n=-0x80000000),(o=n*=1)!=o&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length)if(i)return -1;else n=t.length-1;else if(n<0)if(!i)return -1;else n=0;if("string"==typeof e&&(e=s.from(e,r)),s.isBuffer(e))return 0===e.length?-1:v(t,e,n,r,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,n);else return Uint8Array.prototype.lastIndexOf.call(t,e,n);return v(t,[e],n,r,i)}throw TypeError("val must be string, number or Buffer")}function v(t,e,n,r,i){var o,a=1,s=t.length,u=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return -1;a=2,s/=2,u/=2,n/=2}function l(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var c=-1;for(o=n;o<s;o++)if(l(t,o)===l(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===u)return c*a}else -1!==c&&(o-=o-c),c=-1}else for(n+u>s&&(n=s-u),o=n;o>=0;o--){for(var h=!0,f=0;f<u;f++)if(l(t,o+f)!==l(e,f)){h=!1;break}if(h)return o}return -1}s.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==s.prototype},s.compare=function(t,e){if(I(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),I(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(t)||!s.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:+(r<n)},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);if(void 0===e)for(n=0,e=0;n<t.length;++n)e+=t[n].length;var n,r=s.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var o=t[n];if(I(o,Uint8Array)&&(o=s.from(o)),!s.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},s.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},s.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},s.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?m(this,0,t):d.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(t){if(!s.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"},o&&(s.prototype[o]=s.prototype.inspect),s.prototype.compare=function(t,e,n,r,i){if(I(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return -1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,i>>>=0,this===t)return 0;for(var o=i-r,a=n-e,u=Math.min(o,a),l=this.slice(r,i),c=t.slice(e,n),h=0;h<u;++h)if(l[h]!==c[h]){o=l[h],a=c[h];break}return o<a?-1:+(a<o)},s.prototype.includes=function(t,e,n){return -1!==this.indexOf(t,e,n)},s.prototype.indexOf=function(t,e,n){return y(this,t,e,n,!0)},s.prototype.lastIndexOf=function(t,e,n){return y(this,t,e,n,!1)};function m(t,e,n){n=Math.min(t.length,n);for(var r=[],i=e;i<n;){var o,a,s,u,l=t[i],c=null,h=l>239?4:l>223?3:l>191?2:1;if(i+h<=n)switch(h){case 1:l<128&&(c=l);break;case 2:(192&(o=t[i+1]))==128&&(u=(31&l)<<6|63&o)>127&&(c=u);break;case 3:o=t[i+1],a=t[i+2],(192&o)==128&&(192&a)==128&&(u=(15&l)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:o=t[i+1],a=t[i+2],s=t[i+3],(192&o)==128&&(192&a)==128&&(192&s)==128&&(u=(15&l)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&u<1114112&&(c=u)}null===c?(c=65533,h=1):c>65535&&(c-=65536,r.push(c>>>10&1023|55296),c=56320|1023&c),r.push(c),i+=h}var f=r,p=f.length;if(p<=4096)return String.fromCharCode.apply(String,f);for(var d="",g=0;g<p;)d+=String.fromCharCode.apply(String,f.slice(g,g+=4096));return d}function _(t,e,n){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>n)throw RangeError("Trying to access beyond buffer length")}function x(t,e,n,r,i,o){if(!s.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(n+r>t.length)throw RangeError("Index out of range")}function b(t,e,n,r,i,o){if(n+r>t.length||n<0)throw RangeError("Index out of range")}function w(t,e,n,r,o){return e*=1,n>>>=0,o||b(t,e,n,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,n,r,23,4),n+4}function S(t,e,n,r,o){return e*=1,n>>>=0,o||b(t,e,n,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,n,r,52,8),n+8}s.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,a,s,u,l,c,h,f=this.length-e;if((void 0===n||n>f)&&(n=f),t.length>0&&(n<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var p=!1;;)switch(r){case"hex":return function(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=e.length;r>o/2&&(r=o/2);for(var a=0;a<r;++a){var s,u=parseInt(e.substr(2*a,2),16);if((s=u)!=s)break;t[n+a]=u}return a}(this,t,e,n);case"utf8":case"utf-8":return i=e,o=n,C(A(t,this.length-i),this,i,o);case"ascii":return a=e,s=n,C(T(t),this,a,s);case"latin1":case"binary":return function(t,e,n,r){return C(T(e),t,n,r)}(this,t,e,n);case"base64":return u=e,l=n,C(k(t),this,u,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=e,h=n,C(function(t,e){for(var n,r,i=[],o=0;o<t.length&&!((e-=2)<0);++o)r=(n=t.charCodeAt(o))>>8,i.push(n%256),i.push(r);return i}(t,this.length-c),this,c,h);default:if(p)throw TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),p=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(t,e){var n=this.length;t=~~t,e=void 0===e?n:~~e,t<0?(t+=n)<0&&(t=0):t>n&&(t=n),e<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);var r=this.subarray(t,e);return Object.setPrototypeOf(r,s.prototype),r},s.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||_(t,e,this.length);for(var r=this[t],i=1,o=0;++o<e&&(i*=256);)r+=this[t+o]*i;return r},s.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||_(t,e,this.length);for(var r=this[t+--e],i=1;e>0&&(i*=256);)r+=this[t+--e]*i;return r},s.prototype.readUInt8=function(t,e){return t>>>=0,e||_(t,1,this.length),this[t]},s.prototype.readUInt16LE=function(t,e){return t>>>=0,e||_(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUInt16BE=function(t,e){return t>>>=0,e||_(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUInt32LE=function(t,e){return t>>>=0,e||_(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},s.prototype.readUInt32BE=function(t,e){return t>>>=0,e||_(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||_(t,e,this.length);for(var r=this[t],i=1,o=0;++o<e&&(i*=256);)r+=this[t+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*e)),r},s.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||_(t,e,this.length);for(var r=e,i=1,o=this[t+--r];r>0&&(i*=256);)o+=this[t+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},s.prototype.readInt8=function(t,e){return(t>>>=0,e||_(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},s.prototype.readInt16LE=function(t,e){t>>>=0,e||_(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?0xffff0000|n:n},s.prototype.readInt16BE=function(t,e){t>>>=0,e||_(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?0xffff0000|n:n},s.prototype.readInt32LE=function(t,e){return t>>>=0,e||_(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,e){return t>>>=0,e||_(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readFloatLE=function(t,e){return t>>>=0,e||_(t,4,this.length),i.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,e){return t>>>=0,e||_(t,4,this.length),i.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,e){return t>>>=0,e||_(t,8,this.length),i.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,e){return t>>>=0,e||_(t,8,this.length),i.read(this,t,!1,52,8)},s.prototype.writeUIntLE=function(t,e,n,r){if(t*=1,e>>>=0,n>>>=0,!r){var i=Math.pow(2,8*n)-1;x(this,t,e,n,i,0)}var o=1,a=0;for(this[e]=255&t;++a<n&&(o*=256);)this[e+a]=t/o&255;return e+n},s.prototype.writeUIntBE=function(t,e,n,r){if(t*=1,e>>>=0,n>>>=0,!r){var i=Math.pow(2,8*n)-1;x(this,t,e,n,i,0)}var o=n-1,a=1;for(this[e+o]=255&t;--o>=0&&(a*=256);)this[e+o]=t/a&255;return e+n},s.prototype.writeUInt8=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,1,255,0),this[e]=255&t,e+1},s.prototype.writeUInt16LE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeUInt16BE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeUInt32LE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},s.prototype.writeUInt32BE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeIntLE=function(t,e,n,r){if(t*=1,e>>>=0,!r){var i=Math.pow(2,8*n-1);x(this,t,e,n,i-1,-i)}var o=0,a=1,s=0;for(this[e]=255&t;++o<n&&(a*=256);)t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/a|0)-s&255;return e+n},s.prototype.writeIntBE=function(t,e,n,r){if(t*=1,e>>>=0,!r){var i=Math.pow(2,8*n-1);x(this,t,e,n,i-1,-i)}var o=n-1,a=1,s=0;for(this[e+o]=255&t;--o>=0&&(a*=256);)t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/a|0)-s&255;return e+n},s.prototype.writeInt8=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},s.prototype.writeInt16LE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeInt16BE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeInt32LE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},s.prototype.writeInt32BE=function(t,e,n){return t*=1,e>>>=0,n||x(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeFloatLE=function(t,e,n){return w(this,t,e,!0,n)},s.prototype.writeFloatBE=function(t,e,n){return w(this,t,e,!1,n)},s.prototype.writeDoubleLE=function(t,e,n){return S(this,t,e,!0,n)},s.prototype.writeDoubleBE=function(t,e,n){return S(this,t,e,!1,n)},s.prototype.copy=function(t,e,n,r){if(!s.isBuffer(t))throw TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw RangeError("Index out of range");if(r<0)throw RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i=r-n;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,n,r);else if(this===t&&n<e&&e<r)for(var o=i-1;o>=0;--o)t[o+e]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,r),e);return i},s.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw TypeError("Unknown encoding: "+r);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===r&&o<128||"latin1"===r)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw RangeError("Out of range index");if(n<=e)return this;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=e;i<n;++i)this[i]=t;else{var a=s.isBuffer(t)?t:s.from(t,r),u=a.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<n-e;++i)this[i+e]=a[i%u]}return this};var M=/[^+/0-9A-Za-z-_]/g;function A(t,e){e=e||1/0;for(var n,r=t.length,i=null,o=[],a=0;a<r;++a){if((n=t.charCodeAt(a))>55295&&n<57344){if(!i){if(n>56319||a+1===r){(e-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else if(n<1114112){if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}else throw Error("Invalid code point")}return o}function T(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}function k(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(M,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function C(t,e,n,r){for(var i=0;i<r&&!(i+n>=e.length)&&!(i>=t.length);++i)e[i+n]=t[i];return i}function I(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var D=function(){for(var t="0123456789abcdef",e=Array(256),n=0;n<16;++n)for(var r=16*n,i=0;i<16;++i)e[r+i]=t[n]+t[i];return e}()},783:function(t,e){e.read=function(t,e,n,r,i){var o,a,s=8*i-r-1,u=(1<<s)-1,l=u>>1,c=-7,h=n?i-1:0,f=n?-1:1,p=t[e+h];for(h+=f,o=p&(1<<-c)-1,p>>=-c,c+=s;c>0;o=256*o+t[e+h],h+=f,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=r;c>0;a=256*a+t[e+h],h+=f,c-=8);if(0===o)o=1-l;else{if(o===u)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,r),o-=l}return(p?-1:1)*a*Math.pow(2,o-r)},e.write=function(t,e,n,r,i,o){var a,s,u,l=8*o-i-1,c=(1<<l)-1,h=c>>1,f=5960464477539062e-23*(23===i),p=r?0:o-1,d=r?1:-1,g=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(s=+!!isNaN(e),a=c):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),a+h>=1?e+=f/u:e+=f*Math.pow(2,1-h),e*u>=2&&(a++,u/=2),a+h>=c?(s=0,a=c):a+h>=1?(s=(e*u-1)*Math.pow(2,i),a+=h):(s=e*Math.pow(2,h-1)*Math.pow(2,i),a=0));i>=8;t[n+p]=255&s,p+=d,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;t[n+p]=255&a,p+=d,a/=256,l-=8);t[n+p-d]|=128*g}}},n={};function r(t){var i=n[t];if(void 0!==i)return i.exports;var o=n[t]={exports:{}},a=!0;try{e[t](o,o.exports,r),a=!1}finally{a&&delete n[t]}return o.exports}r.ab="//",t.exports=r(72)}()},5692:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60,n=null;return function(){for(var r=this,i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];clearTimeout(n),n=setTimeout(function(){t.apply(r,o)},e)}}},5733:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.pick=void 0,e.pick=function(t,e){var n={};return e.forEach(function(e){n[e]=t[e]}),n}},5759:(t,e,n)=>{"use strict";n.d(e,{A:()=>x});var r=n(4621),i=n(4605),o=n(7669),a=n(8284),s=n(6847),u=n(4123),l=n(4035),c=n(411),h=n(3273),f="__zr_normal__",p=r.Wx.concat(["ignore"]),d=(0,u.reduce)(r.Wx,function(t,e){return t[e]=!0,t},{ignore:!1}),g={},y=new o.A(0,0,0,0),v=function(){function t(t){this.id=(0,u.guid)(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var r=this.transform;r||(r=this.transform=[1,0,0,1,0,0]),r[4]+=t,r[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,r=n.local,i=e.innerTransformable,o=void 0,a=void 0,u=!1;i.parent=r?this:null;var l=!1;if(i.copyTransform(e),null!=n.position){n.layoutRect?y.copy(n.layoutRect):y.copy(this.getBoundingRect()),r||y.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(g,n,y):(0,s.X4)(g,n,y),i.x=g.x,i.y=g.y,o=g.align,a=g.verticalAlign;var c=n.origin;if(c&&null!=n.rotation){var f=void 0,p=void 0;"center"===c?(f=.5*y.width,p=.5*y.height):(f=(0,s.lo)(c[0],y.width),p=(0,s.lo)(c[1],y.height)),l=!0,i.originX=-i.x+f+(r?0:y.x),i.originY=-i.y+p+(r?0:y.y)}}null!=n.rotation&&(i.rotation=n.rotation);var d=n.offset;d&&(i.x+=d[0],i.y+=d[1],l||(i.originX=-d[0],i.originY=-d[1]));var v=null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside,m=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),_=void 0,x=void 0,b=void 0;v&&this.canBeInsideText()?(_=n.insideFill,x=n.insideStroke,(null==_||"auto"===_)&&(_=this.getInsideTextFill()),(null==x||"auto"===x)&&(x=this.getInsideTextStroke(_),b=!0)):(_=n.outsideFill,x=n.outsideStroke,(null==_||"auto"===_)&&(_=this.getOutsideFill()),(null==x||"auto"===x)&&(x=this.getOutsideStroke(_),b=!0)),((_=_||"#000")!==m.fill||x!==m.stroke||b!==m.autoStroke||o!==m.align||a!==m.verticalAlign)&&(u=!0,m.fill=_,m.stroke=x,m.autoStroke=b,m.align=o,m.verticalAlign=a,e.setDefaultTextStyle(m)),e.__dirty|=h.M,u&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?l.el:l._S},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&(0,c.parse)(e);n||(n=[255,255,255,1]);for(var r=n[3],i=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*r+255*!i*(1-r);return n[3]=1,(0,c.stringify)(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},(0,u.extend)(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if((0,u.isObject)(t))for(var n=(0,u.keys)(t),r=0;r<n.length;r++){var i=n[r];this.attrKV(i,t[i])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var r=this.animators[n],i=r.__fromStateTransition;if(!r.getLoop()&&(!i||i===f)){var o=r.targetName,a=o?e[o]:e;r.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,p)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var r=0;r<n.length;r++){var i=n[r];null==t[i]||i in e||(e[i]=this[i])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(f,!1,t)},t.prototype.useState=function(t,e,n,r){var i,o=t===f;if(this.hasState()||!o){var a=this.currentStates,s=this.stateTransition;if(!((0,u.indexOf)(a,t)>=0)||!e&&1!==a.length){if(this.stateProxy&&!o&&(i=this.stateProxy(t)),i||(i=this.states&&this.states[t]),!i&&!o)return void(0,u.logError)("State "+t+" not exists.");o||this.saveCurrentToNormalState(i);var l=!!(i&&i.hoverLayer||r);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,i,this._normalState,e,!n&&!this.__inHover&&s&&s.duration>0,s);var c=this._textContent,p=this._textGuide;return c&&c.useState(t,e,n,l),p&&p.useState(t,e,n,l),o?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~h.M),i}}},t.prototype.useStates=function(t,e,n){if(t.length){var r=[],i=this.currentStates,o=t.length,a=o===i.length;if(a){for(var s=0;s<o;s++)if(t[s]!==i[s]){a=!1;break}}if(!a){for(var s=0;s<o;s++){var u=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(u,t)),l||(l=this.states[u]),l&&r.push(l)}var c=r[o-1],f=!!(c&&c.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(r),d=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var g=this._textContent,y=this._textGuide;g&&g.useStates(t,e,f),y&&y.useStates(t,e,f),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~h.M)}}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=(0,u.indexOf)(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var r=this.currentStates.slice(),i=(0,u.indexOf)(r,t),o=(0,u.indexOf)(r,e)>=0;i>=0?o?r.splice(i,1):r[i]=e:n&&!o&&r.push(e),this.useStates(r)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},r=0;r<t.length;r++){var i=t[r];(0,u.extend)(n,i),i.textConfig&&(e=e||{},(0,u.extend)(e,i.textConfig))}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,r,i,o){var a=!(e&&r);e&&e.textConfig?(this.textConfig=(0,u.extend)({},r?this.textConfig:n.textConfig),(0,u.extend)(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,c=0;c<p.length;c++){var h=p[c],f=i&&d[h];e&&null!=e[h]?f?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(f?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!i)for(var c=0;c<this.animators.length;c++){var g=this.animators[c],y=g.targetName;g.getLoop()||g.__changeFinalValue(y?(e||n)[y]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new r.Ay,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),(0,u.extend)(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=h.M;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var r=t?this[t]:this,o=new i.A(r,e,n);return t&&(o.targetName=t),this.addAnimator(o,t),o},t.prototype.addAnimator=function(t,e){var n=this.__zr,r=this;t.during(function(){r.updateDuringAnimation(e)}).done(function(){var e=r.animators,n=(0,u.indexOf)(e,t);n>=0&&e.splice(n,1)}),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,r=n.length,i=[],o=0;o<r;o++){var a=n[o];t&&t!==a.scope?i.push(a):a.stop(e)}return this.animators=i,this},t.prototype.animateTo=function(t,e,n){m(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){m(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,r){for(var i=m(this,e,n,r),o=0;o<i.length;o++)i[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;function n(t,n,r,i){Object.defineProperty(e,t,{get:function(){return this[n]||o(this,this[n]=[]),this[n]},set:function(t){this[r]=t[0],this[i]=t[1],this[n]=t,o(this,t)}});function o(t,e){Object.defineProperty(e,0,{get:function(){return t[r]},set:function(e){t[r]=e}}),Object.defineProperty(e,1,{get:function(){return t[i]},set:function(e){t[i]=e}})}}e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=h.M,Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function m(t,e,n,r,o){var a=[];!function t(e,n,r,o,a,s,l,c){for(var h=(0,u.keys)(o),f=a.duration,p=a.delay,d=a.additive,g=a.setToFinal,y=!(0,u.isObject)(s),v=e.animators,m=[],x=0;x<h.length;x++){var b=h[x],w=o[b];if(null!=w&&null!=r[b]&&(y||s[b]))if(!(0,u.isObject)(w)||(0,u.isArrayLike)(w)||(0,u.isGradientObject)(w))m.push(b);else{if(n){c||(r[b]=w,e.updateDuringAnimation(n));continue}t(e,b,r[b],w,a,s&&s[b],l,c)}else c||(r[b]=w,e.updateDuringAnimation(n),m.push(b))}var S=m.length;if(!d&&S)for(var M=0;M<v.length;M++){var A=v[M];if(A.targetName===n&&A.stopTracks(m)){var T=(0,u.indexOf)(v,A);v.splice(T,1)}}if(a.force||(S=(m=(0,u.filter)(m,function(t){var e,n;return!((e=o[t])===(n=r[t])||(0,u.isArrayLike)(e)&&(0,u.isArrayLike)(n)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}(e,n))})).length),S>0||a.force&&!l.length){var k=void 0,C=void 0,I=void 0;if(c){C={},g&&(k={});for(var M=0;M<S;M++){var b=m[M];C[b]=r[b],g?k[b]=o[b]:r[b]=o[b]}}else if(g){I={};for(var M=0;M<S;M++){var b=m[M];I[b]=(0,i.F)(r[b]),function(t,e,n){if((0,u.isArrayLike)(e[n]))if((0,u.isArrayLike)(t[n])||(t[n]=[]),(0,u.isTypedArray)(e[n])){var r=e[n].length;t[n].length!==r&&(t[n]=new e[n].constructor(r),_(t[n],e[n],r))}else{var i=e[n],o=t[n],a=i.length;if((0,u.isArrayLike)(i[0]))for(var s=i[0].length,l=0;l<a;l++)o[l]?_(o[l],i[l],s):o[l]=Array.prototype.slice.call(i[l]);else _(o,i,a);o.length=i.length}else t[n]=e[n]}(r,o,b)}}var A=new i.A(r,!1,!1,d?(0,u.filter)(v,function(t){return t.targetName===n}):null);A.targetName=n,a.scope&&(A.scope=a.scope),g&&k&&A.whenWithKeys(0,k,m),I&&A.whenWithKeys(0,I,m),A.whenWithKeys(null==f?500:f,c?C:o,m).delay(p||0),e.addAnimator(A,n),l.push(A)}}(t,"",t,e,n=n||{},r,a,o);var s=a.length,l=!1,c=n.done,h=n.aborted,f=function(){l=!0,--s<=0&&(l?c&&c():h&&h())},p=function(){--s<=0&&(l?c&&c():h&&h())};!s&&c&&c(),a.length>0&&n.during&&a[0].during(function(t,e){n.during(e)});for(var d=0;d<a.length;d++){var g=a[d];f&&g.done(f),p&&g.aborted(p),n.force&&g.duration(n.duration),g.start(n.easing)}return a}function _(t,e,n){for(var r=0;r<n;r++)t[r]=e[r]}(0,u.mixin)(v,a.A),(0,u.mixin)(v,r.Ay);let x=v},5921:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>y,oN:()=>l,sW:()=>c});var r=n(643),i=n(5759),o=n(7669),a=n(4123),s=n(3273),u="__zr_style_"+Math.round(10*Math.random()),l={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},c={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};l[u]=!0;var h=["z","z2","invisible"],f=["invisible"],p=function(t){var e;function n(e){return t.call(this,e)||this}return(0,r.__extends)(n,t),n.prototype._init=function(e){for(var n=(0,a.keys)(e),r=0;r<n.length;r++){var i=n[r];"style"===i?this.useStyle(e[i]):t.prototype.attrKV.call(this,i,e[i])}this.style||this.useStyle({})},n.prototype.beforeBrush=function(){},n.prototype.afterBrush=function(){},n.prototype.innerBeforeBrush=function(){},n.prototype.innerAfterBrush=function(){},n.prototype.shouldBePainted=function(t,e,n,r){var i,o,a,s=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&(i=this,o=t,a=e,d.copy(i.getBoundingRect()),i.transform&&d.applyTransform(i.transform),g.width=o,g.height=a,!d.intersect(g))||s&&!s[0]&&!s[3])return!1;if(n&&this.__clipPaths){for(var u=0;u<this.__clipPaths.length;++u)if(this.__clipPaths[u].isZeroArea())return!1}if(r&&this.parent)for(var l=this.parent;l;){if(l.ignore)return!1;l=l.parent}return!0},n.prototype.contain=function(t,e){return this.rectContain(t,e)},n.prototype.traverse=function(t,e){t.call(e,this)},n.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},n.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),r=this.style,i=r.shadowBlur||0,a=r.shadowOffsetX||0,s=r.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new o.A(0,0,0,0)),e?o.A.applyTransform(t,n,e):t.copy(n),(i||a||s)&&(t.width+=2*i+Math.abs(a),t.height+=2*i+Math.abs(s),t.x=Math.min(t.x,t.x+a-i),t.y=Math.min(t.y,t.y+s-i));var u=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-u),t.y=Math.floor(t.y-u),t.width=Math.ceil(t.width+1+2*u),t.height=Math.ceil(t.height+1+2*u))}return t},n.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new o.A(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},n.prototype.getPrevPaintRect=function(){return this._prevPaintRect},n.prototype.animateStyle=function(t){return this.animate("style",t)},n.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},n.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},n.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:(0,a.extend)(this.style,t),this.dirtyStyle(),this},n.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=s.pO,this._rect&&(this._rect=null)},n.prototype.dirty=function(){this.dirtyStyle()},n.prototype.styleChanged=function(){return!!(this.__dirty&s.pO)},n.prototype.styleUpdated=function(){this.__dirty&=~s.pO},n.prototype.createStyle=function(t){return(0,a.createObject)(l,t)},n.prototype.useStyle=function(t){t[u]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},n.prototype.isStyleObject=function(t){return t[u]},n.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,h)},n.prototype._applyStateObj=function(e,n,r,i,o,s){t.prototype._applyStateObj.call(this,e,n,r,i,o,s);var u,l=!(n&&i);if(n&&n.style?o?i?u=n.style:(u=this._mergeStyle(this.createStyle(),r.style),this._mergeStyle(u,n.style)):(u=this._mergeStyle(this.createStyle(),i?this.style:r.style),this._mergeStyle(u,n.style)):l&&(u=r.style),u)if(o){var c=this.style;if(this.style=this.createStyle(l?{}:c),l)for(var p=(0,a.keys)(c),d=0;d<p.length;d++){var g=p[d];g in u&&(u[g]=u[g],this.style[g]=c[g])}for(var y=(0,a.keys)(u),d=0;d<y.length;d++){var g=y[d];this.style[g]=this.style[g]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u);for(var v=this.__inHover?f:h,d=0;d<v.length;d++){var g=v[d];n&&null!=n[g]?this[g]=n[g]:l&&null!=r[g]&&(this[g]=r[g])}},n.prototype._mergeStates=function(e){for(var n,r=t.prototype._mergeStates.call(this,e),i=0;i<e.length;i++){var o=e[i];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(r.style=n),r},n.prototype._mergeStyle=function(t,e){return(0,a.extend)(t,e),t},n.prototype.getAnimationStyleProps=function(){return c},n.initDefaultProps=void((e=n.prototype).type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=s.M|s.pO),n}(i.A),d=new o.A(0,0,0,0),g=new o.A(0,0,0,0);let y=p},5942:(t,e,n)=>{"use strict";n.d(e,{w:()=>g});var r=Math.round(9*Math.random()),i="function"==typeof Object.defineProperty,o=function(){function t(){this._id="__ec_inner_"+r++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return i?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}(),a=n(4948),s=n(4123),u=n(3607),l=n(4670),c=n(1834),h=n(2316),f=new o,p=new a.Ay(100),d=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function g(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),r=e.getZr(),i="svg"===r.painter.type;t.dirty&&f.delete(t);var o=f.get(t);if(o)return o;var a=(0,s.defaults)(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var g={repeat:"repeat"};return function(t){for(var e,o,f,g=[n],v=!0,m=0;m<d.length;++m){var _=a[d[m]];if(null!=_&&!(0,s.isArray)(_)&&!(0,s.isString)(_)&&!(0,s.isNumber)(_)&&"boolean"!=typeof _){v=!1;break}g.push(_)}if(v){o=g.join(",")+(i?"-svg":"");var x=p.get(o);x&&(i?t.svgElement=x:t.image=x)}var b=function t(e){if(!e||0===e.length)return[[0,0]];if((0,s.isNumber)(e)){var n=Math.ceil(e);return[[n,n]]}for(var r=!0,i=0;i<e.length;++i)if(!(0,s.isNumber)(e[i])){r=!1;break}if(r)return t([e]);for(var o=[],i=0;i<e.length;++i)if((0,s.isNumber)(e[i])){var n=Math.ceil(e[i]);o.push([n,n])}else{var n=(0,s.map)(e[i],function(t){return Math.ceil(t)});n.length%2==1?o.push(n.concat(n)):o.push(n)}return o}(a.dashArrayX),w=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if((0,s.isNumber)(t)){var e=Math.ceil(t);return[e,e]}var n=(0,s.map)(t,function(t){return Math.ceil(t)});return t.length%2?n.concat(n):n}(a.dashArrayY),S=function t(e){if(!e||0===e.length)return[["rect"]];if((0,s.isString)(e))return[[e]];for(var n=!0,r=0;r<e.length;++r)if(!(0,s.isString)(e[r])){n=!1;break}if(n)return t([e]);for(var i=[],r=0;r<e.length;++r)(0,s.isString)(e[r])?i.push([e[r]]):i.push(e[r]);return i}(a.symbol),M=(e=b,(0,s.map)(e,function(t){return y(t)})),A=y(w),T=!i&&h.yh.createCanvas(),k=i&&{tag:"g",attrs:{},key:"dcl",children:[]},C=function(){for(var t=1,e=0,n=M.length;e<n;++e)t=(0,u.lQ)(t,M[e]);for(var r=1,e=0,n=S.length;e<n;++e)r=(0,u.lQ)(r,S[e].length);t*=r;var i=A*M.length*S.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(i,a.maxTileHeight))}}();T&&(T.width=C.width*n,T.height=C.height*n,f=T.getContext("2d")),function(){f&&(f.clearRect(0,0,T.width,T.height),a.backgroundColor&&(f.fillStyle=a.backgroundColor,f.fillRect(0,0,T.width,T.height)));for(var t=0,e=0;e<w.length;++e)t+=w[e];if(!(t<=0))for(var o=-A,s=0,u=0,h=0;o<C.height;){if(s%2==0){for(var p=u/2%S.length,d=0,g=0,y=0;d<2*C.width;){for(var v=0,e=0;e<b[h].length;++e)v+=b[h][e];if(v<=0)break;if(g%2==0){var m=(1-a.symbolSize)*.5,_=d+b[h][g]*m,x=o+w[s]*m,M=b[h][g]*a.symbolSize,I=w[s]*a.symbolSize,D=y/2%S[p].length;!function(t,e,o,s,u){var h=i?1:n,p=(0,l.v5)(u,t*h,e*h,o*h,s*h,a.color,a.symbolKeepAspect);if(i){var d=r.painter.renderOneToVNode(p);d&&k.children.push(d)}else(0,c.Xi)(f,p)}(_,x,M,I,S[p][D])}d+=b[h][g],++y,++g===b[h].length&&(g=0)}++h===b.length&&(h=0)}o+=w[s],++u,++s===w.length&&(s=0)}}(),v&&p.put(o,T||k),t.image=T,t.svgElement=k,t.svgWidth=C.width,t.svgHeight=C.height}(g),g.rotation=a.rotation,g.scaleX=g.scaleY=i?1:1/n,f.set(t,g),t.dirty=!1,g}function y(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}},5951:(t,e,n)=>{"use strict";function r(t,e,n,r){var i=e+1;if(i===n)return 1;if(0>r(t[i++],t[e])){for(;i<n&&0>r(t[i],t[i-1]);)i++;var o=t,a=e,s=i;for(s--;a<s;){var u=o[a];o[a++]=o[s],o[s--]=u}}else for(;i<n&&r(t[i],t[i-1])>=0;)i++;return i-e}function i(t,e,n,r,i){for(r===e&&r++;r<n;r++){for(var o,a=t[r],s=e,u=r;s<u;)0>i(a,t[o=s+u>>>1])?u=o:s=o+1;var l=r-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;l>0;)t[s+l]=t[s+l-1],l--}t[s]=a}}function o(t,e,n,r,i,o){var a=0,s=0,u=1;if(o(t,e[n+i])>0){for(s=r-i;u<s&&o(t,e[n+i+u])>0;)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s),a+=i,u+=i}else{for(s=i+1;u<s&&0>=o(t,e[n+i-u]);)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s);var l=a;a=i-u,u=i-l}for(a++;a<u;){var c=a+(u-a>>>1);o(t,e[n+c])>0?a=c+1:u=c}return u}function a(t,e,n,r,i,o){var a=0,s=0,u=1;if(0>o(t,e[n+i])){for(s=i+1;u<s&&0>o(t,e[n+i-u]);)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s);var l=a;a=i-u,u=i-l}else{for(s=r-i;u<s&&o(t,e[n+i+u])>=0;)a=u,(u=(u<<1)+1)<=0&&(u=s);u>s&&(u=s),a+=i,u+=i}for(a++;a<u;){var c=a+(u-a>>>1);0>o(t,e[n+c])?u=c:a=c+1}return u}function s(t,e,n,s){n||(n=0),s||(s=t.length);var u=s-n;if(!(u<2)){var l=0;if(u<32){l=r(t,n,s,e),i(t,n,s,n+l,e);return}var c=function(t,e){var n,r,i=7,s=0,u=[];function l(l){var c=n[l],h=r[l],f=n[l+1],p=r[l+1];r[l]=h+p,l===s-3&&(n[l+1]=n[l+2],r[l+1]=r[l+2]),s--;var d=a(t[f],t,c,h,0,e);c+=d,0!=(h-=d)&&0!==(p=o(t[c+h-1],t,f,p,p-1,e))&&(h<=p?function(n,r,s,l){var c,h,f,p=0;for(p=0;p<r;p++)u[p]=t[n+p];var d=0,g=s,y=n;if(t[y++]=t[g++],0==--l){for(p=0;p<r;p++)t[y+p]=u[d+p];return}if(1===r){for(p=0;p<l;p++)t[y+p]=t[g+p];t[y+l]=u[d];return}for(var v=i;;){c=0,h=0,f=!1;do if(0>e(t[g],u[d])){if(t[y++]=t[g++],h++,c=0,0==--l){f=!0;break}}else if(t[y++]=u[d++],c++,h=0,1==--r){f=!0;break}while((c|h)<v);if(f)break;do{if(0!==(c=a(t[g],u,d,r,0,e))){for(p=0;p<c;p++)t[y+p]=u[d+p];if(y+=c,d+=c,(r-=c)<=1){f=!0;break}}if(t[y++]=t[g++],0==--l){f=!0;break}if(0!==(h=o(u[d],t,g,l,0,e))){for(p=0;p<h;p++)t[y+p]=t[g+p];if(y+=h,g+=h,0==(l-=h)){f=!0;break}}if(t[y++]=u[d++],1==--r){f=!0;break}v--}while(c>=7||h>=7);if(f)break;v<0&&(v=0),v+=2}if((i=v)<1&&(i=1),1===r){for(p=0;p<l;p++)t[y+p]=t[g+p];t[y+l]=u[d]}else if(0===r)throw Error();else for(p=0;p<r;p++)t[y+p]=u[d+p]}(c,h,f,p):function(n,r,s,l){var c=0;for(c=0;c<l;c++)u[c]=t[s+c];var h=n+r-1,f=l-1,p=s+l-1,d=0,g=0;if(t[p--]=t[h--],0==--r){for(c=0,d=p-(l-1);c<l;c++)t[d+c]=u[c];return}if(1===l){for(p-=r,h-=r,g=p+1,d=h+1,c=r-1;c>=0;c--)t[g+c]=t[d+c];t[p]=u[f];return}for(var y=i;;){var v=0,m=0,_=!1;do if(0>e(u[f],t[h])){if(t[p--]=t[h--],v++,m=0,0==--r){_=!0;break}}else if(t[p--]=u[f--],m++,v=0,1==--l){_=!0;break}while((v|m)<y);if(_)break;do{if(0!=(v=r-a(u[f],t,n,r,r-1,e))){for(p-=v,h-=v,r-=v,g=p+1,d=h+1,c=v-1;c>=0;c--)t[g+c]=t[d+c];if(0===r){_=!0;break}}if(t[p--]=u[f--],1==--l){_=!0;break}if(0!=(m=l-o(t[h],u,0,l,l-1,e))){for(p-=m,f-=m,l-=m,g=p+1,d=f+1,c=0;c<m;c++)t[g+c]=u[d+c];if(l<=1){_=!0;break}}if(t[p--]=t[h--],0==--r){_=!0;break}y--}while(v>=7||m>=7);if(_)break;y<0&&(y=0),y+=2}if((i=y)<1&&(i=1),1===l){for(p-=r,h-=r,g=p+1,d=h+1,c=r-1;c>=0;c--)t[g+c]=t[d+c];t[p]=u[f]}else if(0===l)throw Error();else for(c=0,d=p-(l-1);c<l;c++)t[d+c]=u[c]}(c,h,f,p))}return n=[],r=[],{mergeRuns:function(){for(;s>1;){var t=s-2;if(t>=1&&r[t-1]<=r[t]+r[t+1]||t>=2&&r[t-2]<=r[t]+r[t-1])r[t-1]<r[t+1]&&t--;else if(r[t]>r[t+1])break;l(t)}},forceMergeRuns:function(){for(;s>1;){var t=s-2;t>0&&r[t-1]<r[t+1]&&t--,l(t)}},pushRun:function(t,e){n[s]=t,r[s]=e,s+=1}}}(t,e),h=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(u);do{if((l=r(t,n,s,e))<h){var f=u;f>h&&(f=h),i(t,n,n+f,n+l,e),l=f}c.pushRun(n,l),c.mergeRuns(),u-=l,n+=l}while(0!==u);c.forceMergeRuns()}}n.d(e,{A:()=>s})},5960:(t,e,n)=>{"use strict";n.d(e,{A:()=>s});var r=n(643),i=n(4271),o=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,r=e.cy,i=e.rx,o=e.ry,a=.5522848*i,s=.5522848*o;t.moveTo(n-i,r),t.bezierCurveTo(n-i,r-s,n-a,r-o,n,r-o),t.bezierCurveTo(n+a,r-o,n+i,r-s,n+i,r),t.bezierCurveTo(n+i,r+s,n+a,r+o,n,r+o),t.bezierCurveTo(n-a,r+o,n-i,r+s,n-i,r),t.closePath()},e}(i.Ay);a.prototype.type="ellipse";let s=a},6033:(t,e,n)=>{"use strict";n.d(e,{m:()=>i,v:()=>o});var r={};function i(t,e){r[t]=e}function o(t){return r[t]}},6036:(t,e,n)=>{"use strict";n.d(e,{A:()=>s});var r=n(6631),i=n(1929),o=n(2660),a=function(){function t(){this.group=new r.A,this.uid=i.$Q("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,r){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,r){},t.prototype.updateLayout=function(t,e,n,r){},t.prototype.updateVisual=function(t,e,n,r){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();o.gq(a),o.tQ(a);let s=a},6187:(t,e,n)=>{"use strict";n.d(e,{$r:()=>_,Bq:()=>A,D$:()=>T,GX:()=>v,HB:()=>w,Il:()=>k,JO:()=>M,JS:()=>u,M5:()=>s,O0:()=>g,O2:()=>h,US:()=>S,_e:()=>b,le:()=>m,oh:()=>y,qB:()=>a,vS:()=>d,vj:()=>l,zu:()=>c});var r=n(4123),i=n(3607),o="series\0";function a(t){return t instanceof Array?t:null==t?[]:[t]}function s(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var r=0,i=n.length;r<i;r++){var o=n[r];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var u=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function l(t){return!(0,r.isObject)(t)||(0,r.isArray)(t)||t instanceof Date?t:t.value}function c(t){return(0,r.isObject)(t)&&!(t instanceof Array)}function h(t,e,n){var i,a,s,u,l,c,h,d,g,v,m,_,x,b="normalMerge"===n,w="replaceMerge"===n,S="replaceAll"===n;t=t||[],e=(e||[]).slice();var M=(0,r.createHashMap)();(0,r.each)(e,function(t,n){if(!(0,r.isObject)(t)){e[n]=null;return}});var A=function(t,e,n){var r=[];if("replaceAll"===n)return r;for(var i=0;i<t.length;i++){var o=t[i];o&&null!=o.id&&e.set(o.id,i),r.push({existing:"replaceMerge"===n||y(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return r}(t,M,n);return(b||w)&&(i=A,a=t,s=M,u=e,(0,r.each)(u,function(t,e){if(t&&null!=t.id){var n=p(t.id),o=s.get(n);if(null!=o){var l=i[o];(0,r.assert)(!l.newOption,'Duplicated option on id "'+n+'".'),l.newOption=t,l.existing=a[o],u[e]=null}}})),b&&(l=A,c=e,(0,r.each)(c,function(t,e){if(t&&null!=t.name)for(var n=0;n<l.length;n++){var r=l[n].existing;if(!l[n].newOption&&r&&(null==r.id||null==t.id)&&!y(t)&&!y(r)&&f("name",r,t)){l[n].newOption=t,c[e]=null;return}}})),b||w?(h=A,d=e,g=w,(0,r.each)(d,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||y(e.existing)||e.existing&&null!=t.id&&!f("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=g):h.push({newOption:t,brandNew:g,existing:null,keyInfo:null}),n++}})):S&&(v=A,m=e,(0,r.each)(m,function(t){v.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),_=A,x=(0,r.createHashMap)(),(0,r.each)(_,function(t){var e=t.existing;e&&x.set(e.id,t)}),(0,r.each)(_,function(t){var e=t.newOption;(0,r.assert)(!e||null==e.id||!x.get(e.id)||x.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&x.set(e.id,t),t.keyInfo||(t.keyInfo={})}),(0,r.each)(_,function(t,e){var n=t.existing,i=t.newOption,a=t.keyInfo;if((0,r.isObject)(i)){if(a.name=null!=i.name?p(i.name):n?n.name:o+e,n)a.id=p(n.id);else if(null!=i.id)a.id=p(i.id);else{var s=0;do a.id="\0"+a.name+"\0"+s++;while(x.get(a.id))}x.set(a.id,t)}}),A}function f(t,e,n){var r=d(e[t],null),i=d(n[t],null);return null!=r&&null!=i&&r===i}function p(t){return d(t,"")}function d(t,e){return null==t?e:(0,r.isString)(t)?t:(0,r.isNumber)(t)||(0,r.isStringSafe)(t)?t+"":e}function g(t){var e=t.name;return!!(e&&e.indexOf(o))}function y(t){return t&&null!=t.id&&0===p(t.id).indexOf("\0_ec_\0")}function v(t,e,n){(0,r.each)(t,function(t){var i,o,a,s,u=t.newOption;(0,r.isObject)(u)&&(t.keyInfo.mainType=e,t.keyInfo.subType=(i=e,o=u,a=t.existing,s=n,o.type?o.type:a?a.subType:s.determineSubType(i,o)))})}function m(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?(0,r.isArray)(e.dataIndex)?(0,r.map)(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?(0,r.isArray)(e.name)?(0,r.map)(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function _(){var t="__ec_inner_"+x++;return function(e){return e[t]||(e[t]={})}}var x=(0,i.IH)();function b(t,e,n){var r=w(e,n),i=r.mainTypeSpecified,o=r.queryOptionMap,a=r.others,s=n?n.defaultMainType:null;return!i&&s&&o.set(s,{}),o.each(function(e,r){var i=M(t,r,e,{useDefault:s===r,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[r+"Models"]=i.models,a[r+"Model"]=i.models[0]}),a}function w(t,e){if((0,r.isString)(t)){var n,i={};i[t+"Index"]=0,n=i}else n=t;var o=(0,r.createHashMap)(),a={},s=!1;return(0,r.each)(n,function(t,n){if("dataIndex"===n||"dataIndexInside"===n){a[n]=t;return}var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],u=i[1],l=(i[2]||"").toLowerCase();!u||!l||e&&e.includeMainTypes&&0>(0,r.indexOf)(e.includeMainTypes,u)||(s=s||!!u,(o.get(u)||o.set(u,{}))[l]=t)}),{mainTypeSpecified:s,queryOptionMap:o,others:a}}var S={useDefault:!0,enableAll:!1,enableNone:!1};function M(t,e,n,i){i=i||S;var o=n.index,a=n.id,s=n.name,u={models:null,specified:null!=o||null!=a||null!=s};if(!u.specified){var l=void 0;return u.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],u}return"none"===o||!1===o?((0,r.assert)(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),u.models=[]):("all"===o&&((0,r.assert)(i.enableAll,'`"all"` is not a valid value on index option.'),o=a=s=null),u.models=t.queryComponents({mainType:e,index:o,id:a,name:s})),u}function A(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function T(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function k(t,e,n,o,a){var s=null==e||"auto"===e;if(null==o)return o;if((0,r.isNumber)(o)){var u,l=(o-(u=n||0))*a+u;return(0,i.LI)(l,s?Math.max((0,i.XV)(n||0),(0,i.XV)(o)):e)}if((0,r.isString)(o))return a<1?n:o;for(var c=[],h=Math.max(n?n.length:0,o.length),f=0;f<h;++f){var p=t.getDimensionInfo(f);if(p&&"ordinal"===p.type)c[f]=(a<1&&n?n:o)[f];else{var d=n&&n[f]?n[f]:0,g=o[f],l=(g-d)*a+d;c[f]=(0,i.LI)(l,s?Math.max((0,i.XV)(d),(0,i.XV)(g)):e)}}return c}},6235:(t,e,n)=>{"use strict";n.d(e,{U:()=>i});var r=n(1148);function i(t,e,n){var i=e.smooth,o=e.points;if(o&&o.length>=2){if(i){var a=function(t,e,n,i){var o,a,s,u,l=[],c=[],h=[],f=[];if(i){s=[1/0,1/0],u=[-1/0,-1/0];for(var p=0,d=t.length;p<d;p++)(0,r.min)(s,s,t[p]),(0,r.max)(u,u,t[p]);(0,r.min)(s,s,i[0]),(0,r.max)(u,u,i[1])}for(var p=0,d=t.length;p<d;p++){var g=t[p];if(n)o=t[p?p-1:d-1],a=t[(p+1)%d];else if(0===p||p===d-1){l.push((0,r.clone)(t[p]));continue}else o=t[p-1],a=t[p+1];(0,r.sub)(c,a,o),(0,r.scale)(c,c,e);var y=(0,r.distance)(g,o),v=(0,r.distance)(g,a),m=y+v;0!==m&&(y/=m,v/=m),(0,r.scale)(h,c,-y),(0,r.scale)(f,c,v);var _=(0,r.add)([],g,h),x=(0,r.add)([],g,f);i&&((0,r.max)(_,_,s),(0,r.min)(_,_,u),(0,r.max)(x,x,s),(0,r.min)(x,x,u)),l.push(_),l.push(x)}return n&&l.push(l.shift()),l}(o,i,n,e.smoothConstraint);t.moveTo(o[0][0],o[0][1]);for(var s=o.length,u=0;u<(n?s:s-1);u++){var l=a[2*u],c=a[2*u+1],h=o[(u+1)%s];t.bezierCurveTo(l[0],l[1],c[0],c[1],h[0],h[1])}}else{t.moveTo(o[0][0],o[0][1]);for(var u=1,f=o.length;u<f;u++)t.lineTo(o[u][0],o[u][1])}n&&t.closePath()}}},6436:(t,e,n)=>{"use strict";n.d(e,{A:()=>h});var r=n(643),i=n(5921),o=n(6847),a=n(4271),s=n(4123),u=n(2316),l=(0,s.defaults)({strokeFirst:!0,font:u.OH,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},a.MW),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.__extends)(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.createStyle=function(t){return(0,s.createObject)(l,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=(0,o.NO)(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var r=t.lineWidth;n.x-=r/2,n.y-=r/2,n.width+=r,n.height+=r}this._rect=n}return this._rect},e.initDefaultProps=void(e.prototype.dirtyRectTolerance=10),e}(i.Ay);c.prototype.type="tspan";let h=c},6586:(t,e,n)=>{"use strict";n.d(e,{A:()=>s});var r=n(643),i=n(4271),o=function(){this.cx=0,this.cy=0,this.r=0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(i.Ay);a.prototype.type="circle";let s=a},6600:(t,e,n)=>{"use strict";n.d(e,{M7:()=>a,Op:()=>o,eB:()=>i});var r=Math.round;function i(t,e,n){if(e){var i=e.x1,o=e.x2,s=e.y1,u=e.y2;t.x1=i,t.x2=o,t.y1=s,t.y2=u;var l=n&&n.lineWidth;return l&&(r(2*i)===r(2*o)&&(t.x1=t.x2=a(i,l,!0)),r(2*s)===r(2*u)&&(t.y1=t.y2=a(s,l,!0))),t}}function o(t,e,n){if(e){var r=e.x,i=e.y,o=e.width,s=e.height;t.x=r,t.y=i,t.width=o,t.height=s;var u=n&&n.lineWidth;return u&&(t.x=a(r,u,!0),t.y=a(i,u,!0),t.width=Math.max(a(r+o,u,!1)-t.x,+(0!==o)),t.height=Math.max(a(i+s,u,!1)-t.y,+(0!==s))),t}}function a(t,e,n){if(!e)return t;var i=r(2*t);return(i+r(e))%2==0?i/2:(i+(n?1:-1))/2}},6631:(t,e,n)=>{"use strict";n.d(e,{A:()=>u});var r=n(643),i=n(4123),o=n(5759),a=n(7669),s=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return(0,r.__extends)(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,r=n.indexOf(e);r>=0&&(n.splice(r,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=i.indexOf(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,r=n[e];if(t&&t!==this&&t.parent!==this&&t!==r){n[e]=t,r.parent=null;var i=this.__zr;i&&r.removeSelfFromZr(i),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,r=i.indexOf(n,t);return r<0||(n.splice(r,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var r=t[n];e&&r.removeSelfFromZr(e),r.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,r=0;r<n.length;r++){var i=n[r];t.call(e,i,r)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var r=this._children[n],i=t.call(e,r);r.isGroup&&!i&&r.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].addSelfToZr(e)},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].removeSelfFromZr(e)},e.prototype.getBoundingRect=function(t){for(var e=new a.A(0,0,0,0),n=t||this._children,r=[],i=null,o=0;o<n.length;o++){var s=n[o];if(!s.ignore&&!s.invisible){var u=s.getBoundingRect(),l=s.getLocalTransform(r);l?(a.A.applyTransform(e,u,l),(i=i||e.clone()).union(e)):(i=i||u.clone()).union(u)}}return i||e},e}(o.A);s.prototype.type="group";let u=s},6748:(t,e,n)=>{"use strict";n.d(e,{n:()=>i});var r=2*Math.PI;function i(t){return(t%=r)<0&&(t+=r),t}},6847:(t,e,n)=>{"use strict";n.d(e,{NO:()=>l,RG:()=>s,X4:()=>d,ks:()=>f,ll:()=>c,lo:()=>p,sZ:()=>h});var r=n(7669),i=n(4948),o=n(2316),a={};function s(t,e){var n=a[e=e||o.OH];n||(n=a[e]=new i.Ay(500));var r=n.get(t);return null==r&&(r=o.yh.measureText(t,e).width,n.put(t,r)),r}function u(t,e,n,i){var o=s(t,e),a=f(e),u=c(0,o,n),l=h(0,a,i);return new r.A(u,l,o,a)}function l(t,e,n,i){var o=((t||"")+"").split("\n");if(1===o.length)return u(o[0],e,n,i);for(var a=new r.A(0,0,0,0),s=0;s<o.length;s++){var l=u(o[s],e,n,i);0===s?a.copy(l):a.union(l)}return a}function c(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function h(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function f(t){return s("国",t)}function p(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function d(t,e,n){var r=e.position||"inside",i=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,u=n.x,l=n.y,c="left",h="top";if(r instanceof Array)u+=p(r[0],n.width),l+=p(r[1],n.height),c=null,h=null;else switch(r){case"left":u-=i,l+=s,c="right",h="middle";break;case"right":u+=i+a,l+=s,h="middle";break;case"top":u+=a/2,l-=i,c="center",h="bottom";break;case"bottom":u+=a/2,l+=o+i,c="center";break;case"inside":u+=a/2,l+=s,c="center",h="middle";break;case"insideLeft":u+=i,l+=s,h="middle";break;case"insideRight":u+=a-i,l+=s,c="right",h="middle";break;case"insideTop":u+=a/2,l+=i,c="center";break;case"insideBottom":u+=a/2,l+=o-i,c="center",h="bottom";break;case"insideTopLeft":u+=i,l+=i;break;case"insideTopRight":u+=a-i,l+=i,c="right";break;case"insideBottomLeft":u+=i,l+=o-i,h="bottom";break;case"insideBottomRight":u+=a-i,l+=o-i,c="right",h="bottom"}return(t=t||{}).x=u,t.y=l,t.align=c,t.verticalAlign=h,t}},6864:(t,e,n)=>{"use strict";n.d(e,{A:()=>h});var r=n(643),i=n(4271),o=n(1148),a=n(882),s=[],u=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function l(t,e,n){var r=t.cpx2,i=t.cpy2;return null!=r||null!=i?[(n?a.rD:a.Yb)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?a.rD:a.Yb)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?a.z7:a.k3)(t.x1,t.cpx1,t.x2,e),(n?a.z7:a.k3)(t.y1,t.cpy1,t.y2,e)]}var c=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new u},e.prototype.buildPath=function(t,e){var n=e.x1,r=e.y1,i=e.x2,o=e.y2,u=e.cpx1,l=e.cpy1,c=e.cpx2,h=e.cpy2,f=e.percent;0!==f&&(t.moveTo(n,r),null==c||null==h?(f<1&&((0,a.kx)(n,u,i,f,s),u=s[1],i=s[2],(0,a.kx)(r,l,o,f,s),l=s[1],o=s[2]),t.quadraticCurveTo(u,l,i,o)):(f<1&&((0,a.YT)(n,u,c,i,f,s),u=s[1],c=s[2],i=s[3],(0,a.YT)(r,l,h,o,f,s),l=s[1],h=s[2],o=s[3]),t.bezierCurveTo(u,l,c,h,i,o)))},e.prototype.pointAt=function(t){return l(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=l(this.shape,t,!0);return o.normalize(e,e)},e}(i.Ay);c.prototype.type="bezier-curve";let h=c},7076:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createSensor=void 0;var r=function(t){return t&&t.__esModule?t:{default:t}}(n(5692)),i=n(3615);e.createSensor=function(t,e){var n=void 0,o=[],a=function(){"static"===getComputedStyle(t).position&&(t.style.position="relative");var e=document.createElement("object");return e.onload=function(){e.contentDocument.defaultView.addEventListener("resize",s),s()},e.style.display="block",e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.height="100%",e.style.width="100%",e.style.overflow="hidden",e.style.pointerEvents="none",e.style.zIndex="-1",e.style.opacity="0",e.setAttribute("class",i.SensorClassName),e.setAttribute("tabindex",i.SensorTabIndex),e.type="text/html",t.appendChild(e),e.data="about:blank",e},s=(0,r.default)(function(){o.forEach(function(e){e(t)})}),u=function(){n&&n.parentNode&&(n.contentDocument&&n.contentDocument.defaultView.removeEventListener("resize",s),n.parentNode.removeChild(n),t.removeAttribute(i.SizeSensorId),n=void 0,o=[],e&&e())};return{element:t,bind:function(t){n||(n=a()),-1===o.indexOf(t)&&o.push(t)},destroy:u,unbind:function(t){var e=o.indexOf(t);-1!==e&&o.splice(e,1),0===o.length&&n&&u()}}}},7143:(t,e,n)=>{"use strict";n.d(e,{A:()=>_});var r=n(643),i=n(4271),o=n(4123),a=Math.PI,s=2*a,u=Math.sin,l=Math.cos,c=Math.acos,h=Math.atan2,f=Math.abs,p=Math.sqrt,d=Math.max,g=Math.min;function y(t,e,n,r,i,o,a){var s=t-n,u=e-r,l=(a?o:-o)/p(s*s+u*u),c=l*u,h=-l*s,f=t+c,g=e+h,y=n+c,v=r+h,m=(f+y)/2,_=(g+v)/2,x=y-f,b=v-g,w=x*x+b*b,S=i-o,M=f*v-y*g,A=(b<0?-1:1)*p(d(0,S*S*w-M*M)),T=(M*b-x*A)/w,k=(-M*x-b*A)/w,C=(M*b+x*A)/w,I=(-M*x+b*A)/w,D=T-m,O=k-_,P=C-m,L=I-_;return D*D+O*O>P*P+L*L&&(T=C,k=I),{cx:T,cy:k,x0:-c,y0:-h,x1:T*(i/S-1),y1:k*(i/S-1)}}var v=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},m=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultShape=function(){return new v},e.prototype.buildPath=function(t,e){!function(t,e){var n,r=d(e.r,0),i=d(e.r0||0,0),v=r>0,m=i>0;if(v||m){if(v||(r=i,i=0),i>r){var _=r;r=i,i=_}var x=e.startAngle,b=e.endAngle;if(!(isNaN(x)||isNaN(b))){var w=e.cx,S=e.cy,M=!!e.clockwise,A=f(b-x),T=A>s&&A%s;if(T>1e-4&&(A=T),r>1e-4)if(A>s-1e-4)t.moveTo(w+r*l(x),S+r*u(x)),t.arc(w,S,r,x,b,!M),i>1e-4&&(t.moveTo(w+i*l(b),S+i*u(b)),t.arc(w,S,i,b,x,M));else{var k=void 0,C=void 0,I=void 0,D=void 0,O=void 0,P=void 0,L=void 0,R=void 0,E=void 0,N=void 0,B=void 0,F=void 0,z=void 0,V=void 0,H=void 0,j=void 0,U=r*l(x),W=r*u(x),G=i*l(b),q=i*u(b),X=A>1e-4;if(X){var Y=e.cornerRadius;Y&&(k=(n=function(t){var e;if((0,o.isArray)(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}(Y))[0],C=n[1],I=n[2],D=n[3]);var Z=f(r-i)/2;if(O=g(Z,I),P=g(Z,D),L=g(Z,k),R=g(Z,C),B=E=d(O,P),F=N=d(L,R),(E>1e-4||N>1e-4)&&(z=r*l(b),V=r*u(b),H=i*l(x),j=i*u(x),A<a)){var $=function(t,e,n,r,i,o,a,s){var u=n-t,l=r-e,c=a-i,h=s-o,f=h*u-c*l;if(!(f*f<1e-4))return f=(c*(e-o)-h*(t-i))/f,[t+f*u,e+f*l]}(U,W,H,j,z,V,G,q);if($){var K=U-$[0],Q=W-$[1],J=z-$[0],tt=V-$[1],te=1/u(c((K*J+Q*tt)/(p(K*K+Q*Q)*p(J*J+tt*tt)))/2),tn=p($[0]*$[0]+$[1]*$[1]);B=g(E,(r-tn)/(te+1)),F=g(N,(i-tn)/(te-1))}}}if(X)if(B>1e-4){var tr=g(I,B),ti=g(D,B),to=y(H,j,U,W,r,tr,M),ta=y(z,V,G,q,r,ti,M);t.moveTo(w+to.cx+to.x0,S+to.cy+to.y0),B<E&&tr===ti?t.arc(w+to.cx,S+to.cy,B,h(to.y0,to.x0),h(ta.y0,ta.x0),!M):(tr>0&&t.arc(w+to.cx,S+to.cy,tr,h(to.y0,to.x0),h(to.y1,to.x1),!M),t.arc(w,S,r,h(to.cy+to.y1,to.cx+to.x1),h(ta.cy+ta.y1,ta.cx+ta.x1),!M),ti>0&&t.arc(w+ta.cx,S+ta.cy,ti,h(ta.y1,ta.x1),h(ta.y0,ta.x0),!M))}else t.moveTo(w+U,S+W),t.arc(w,S,r,x,b,!M);else t.moveTo(w+U,S+W);if(i>1e-4&&X)if(F>1e-4){var tr=g(k,F),ti=g(C,F),to=y(G,q,z,V,i,-ti,M),ta=y(U,W,H,j,i,-tr,M);t.lineTo(w+to.cx+to.x0,S+to.cy+to.y0),F<N&&tr===ti?t.arc(w+to.cx,S+to.cy,F,h(to.y0,to.x0),h(ta.y0,ta.x0),!M):(ti>0&&t.arc(w+to.cx,S+to.cy,ti,h(to.y0,to.x0),h(to.y1,to.x1),!M),t.arc(w,S,i,h(to.cy+to.y1,to.cx+to.x1),h(ta.cy+ta.y1,ta.cx+ta.x1),M),tr>0&&t.arc(w+ta.cx,S+ta.cy,tr,h(ta.y1,ta.x1),h(ta.y0,ta.x0),!M))}else t.lineTo(w+G,S+q),t.arc(w,S,i,b,x,M);else t.lineTo(w+G,S+q)}else t.moveTo(w,S);t.closePath()}}}(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(i.Ay);m.prototype.type="sector";let _=m},7156:(t,e,n)=>{"use strict";n.d(e,{X:()=>a,x:()=>s});var r=n(6187),i=(0,r.$r)(),o=(0,r.$r)(),a=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){return u(this,i,(0,r.qB)(this.get("color",!0)),this.get("colorLayer",!0),t,e,n)},t.prototype.clearColorPalette=function(){var t,e;t=this,(e=i)(t).paletteIdx=0,e(t).paletteNameMap={}},t}();function s(t,e,n,i){var a=(0,r.qB)(t.get(["aria","decal","decals"]));return u(t,o,a,null,e,n,i)}function u(t,e,n,r,i,o,a){var s=e(o=o||t),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(i))return l[i];var c=null!=a&&r?function(t,e){for(var n=t.length,r=0;r<n;r++)if(t[r].length>e)return t[r];return t[n-1]}(r,a):n;if((c=c||n)&&c.length){var h=c[u];return i&&(l[i]=h),s.paletteIdx=(u+1)%c.length,h}}},7170:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});var r=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},i=new function(){this.browser=new r,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(i.wxa=!0,i.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?i.worker=!0:!i.hasGlobalWindow||"Deno"in window?(i.node=!0,i.svgSupported=!0):function(t,e){var n=e.browser,r=t.match(/Firefox\/([\d.]+)/),i=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);r&&(n.firefox=!0,n.version=r[1]),i&&(n.ie=!0,n.version=i[1]),o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18),a&&(n.weChat=!0),e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}(navigator.userAgent,i);let o=i},7172:(t,e,n)=>{"use strict";n.r(e),n.d(e,{dispose:()=>tc,disposeAll:()=>th,getElementSSRData:()=>td,getInstance:()=>tf,init:()=>tl,registerPainter:()=>tp,registerSSRDataGetter:()=>tg,version:()=>ty});var r,i,o=n(7170),a=n(4123),s=n(643),u=n(1148),l=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},c=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new l(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,r=t.offsetY,i=n-this._x,o=r-this._y;this._x=n,this._y=r,e.drift(i,o,t),this.handler.dispatchToElement(new l(e,t),"drag",t.event);var a=this.handler.findHover(n,r,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new l(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new l(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new l(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new l(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),h=n(8284),f=n(4958),p=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,d=[],g=o.A.browser.firefox&&39>+o.A.browser.version.split(".")[0];function y(t,e,n,r){return n=n||{},r?v(t,e,n):g&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):v(t,e,n),n}function v(t,e,n){if(o.A.domSupported&&t.getBoundingClientRect){var r=e.clientX,i=e.clientY;if((0,f.ot)(t)){var a=t.getBoundingClientRect();n.zrX=r-a.left,n.zrY=i-a.top;return}if((0,f.oq)(d,t,r,i)){n.zrX=d[0],n.zrY=d[1];return}}n.zrX=n.zrY=0}function m(t){return t||window.event}function _(t,e,n){if(null!=(e=m(e)).zrX)return e;var r=e.type;if(r&&r.indexOf("touch")>=0){var i="touchend"!==r?e.targetTouches[0]:e.changedTouches[0];i&&y(t,i,e,n)}else{y(t,e,e,n);var o=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,r=t.deltaY;return null==n||null==r?e:3*(0!==r?Math.abs(r):Math.abs(n))*(r>0?-1:r<0?1:n>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&p.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var x=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},b=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var r=t.touches;if(r){for(var i={points:[],touches:[],target:e,event:t},o=0,a=r.length;o<a;o++){var s=r[o],u=y(n,s,{});i.points.push([u.zrX,u.zrY]),i.touches.push(s)}this._track.push(i)}},t.prototype._recognize=function(t){for(var e in S)if(S.hasOwnProperty(e)){var n=S[e](this._track,t);if(n)return n}},t}();function w(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var S={pinch:function(t,e){var n=t.length;if(n){var r=(t[n-1]||{}).points,i=(t[n-2]||{}).points||r;if(i&&i.length>1&&r&&r.length>1){var o=w(r)/w(i);isFinite(o)||(o=1),e.pinchScale=o;var a=[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2];return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},M=n(7669),A="silent";function T(){x(this.event)}var k=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return(0,s.__extends)(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(h.A),C=function(t,e){this.x=t,this.y=e},I=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],D=new M.A(0,0,0,0),O=function(t){function e(e,n,r,i,o){var a=t.call(this)||this;return a._hovered=new C(0,0),a.storage=e,a.painter=n,a.painterRoot=i,a._pointerSize=o,r=r||new k,a.proxy=null,a.setHandlerProxy(r),a._draggingMgr=new c(a),a}return(0,s.__extends)(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(a.each(I,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,r=L(this,e,n),i=this._hovered,o=i.target;o&&!o.__zr&&(o=(i=this.findHover(i.x,i.y)).target);var a=this._hovered=r?new C(e,n):this.findHover(e,n),s=a.target,u=this.proxy;u.setCursor&&u.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(i,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new C(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){var r=(t=t||{}).target;if(!r||!r.silent){for(var i,o="on"+e,a={type:e,event:n,target:(i=t).target,topTarget:i.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:T};r&&(r[o]&&(a.cancelBubble=!!r[o].call(r,a)),r.trigger(e,a),r=r.__hostTarget?r.__hostTarget:r.parent,!a.cancelBubble););!a.cancelBubble&&(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[o]&&t[o].call(t,a),t.trigger&&t.trigger(e,a)}))}},e.prototype.findHover=function(t,e,n){var r=this.storage.getDisplayList(),i=new C(t,e);if(P(r,i,t,e,n),this._pointerSize&&!i.target){for(var o=[],a=this._pointerSize,s=a/2,u=new M.A(t-s,e-s,a,a),l=r.length-1;l>=0;l--){var c=r[l];c===n||c.ignore||c.ignoreCoarsePointer||c.parent&&c.parent.ignoreCoarsePointer||(D.copy(c.getBoundingRect()),c.transform&&D.applyTransform(c.transform),D.intersect(u)&&o.push(c))}if(o.length){for(var h=Math.PI/12,f=2*Math.PI,p=0;p<s;p+=4)for(var d=0;d<f;d+=h)if(P(o,i,t+p*Math.cos(d),e+p*Math.sin(d),n),i.target)return i}}return i},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new b);var n=this._gestureMgr;"start"===e&&n.clear();var r=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),r){var i=r.type;t.gestureEvent=i;var o=new C;o.target=r.target,this.dispatchToElement(o,i,r.event)}},e}(h.A);function P(t,e,n,r,i){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==i&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var r=t,i=void 0,o=!1;r;){if(r.ignoreClip&&(o=!0),!o){var a=r.getClipPath();if(a&&!a.contain(e,n))return!1}r.silent&&(i=!0),r=r.__hostTarget||r.parent}return!i||A}return!1}(a,n,r))&&(e.topTarget||(e.topTarget=a),s!==A)){e.target=a;break}}}function L(t,e,n){var r=t.painter;return e<0||e>r.getWidth()||n<0||n>r.getHeight()}a.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){O.prototype[t]=function(e){var n,r,i=e.zrX,o=e.zrY,a=L(this,i,o);if("mouseup"===t&&a||(r=(n=this.findHover(i,o)).target),"mousedown"===t)this._downEl=r,this._downPoint=[e.zrX,e.zrY],this._upEl=r;else if("mouseup"===t)this._upEl=r;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||u.dist(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}});var R=n(5951),E=n(3273),N=!1;function B(){N||(N=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function F(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var z=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=F}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return(t||!n.length)&&this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,r=0,i=e.length;r<i;r++)this._updateAndAddDisplayable(e[r],null,t);n.length=this._displayListLen,(0,R.A)(n,F)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var r=t.getClipPath();if(t.ignoreClip)e=null;else if(r){e=e?e.slice():[];for(var i=r,o=t;i;)i.parent=o,i.updateTransform(),e.push(i),o=i,i=i.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var u=a[s];t.__dirty&&(u.__dirty|=E.M),this._updateAndAddDisplayable(u,e,n)}t.__dirty=0}else e&&e.length?t.__clipPaths=e:t.__clipPaths&&t.__clipPaths.length>0&&(t.__clipPaths=[]),isNaN(t.z)&&(B(),t.z=0),isNaN(t.z2)&&(B(),t.z2=0),isNaN(t.zlevel)&&(B(),t.zlevel=0),this._displayList[this._displayListLen++]=t;var l=t.getDecalElement&&t.getDecalElement();l&&this._updateAndAddDisplayable(l,e,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,n);var h=t.getTextContent();h&&this._updateAndAddDisplayable(h,e,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);return}var r=a.indexOf(this._roots,t);r>=0&&this._roots.splice(r,1)},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}();r=o.A.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)};var V=n(4605);function H(){return new Date().getTime()}var j=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,n.stage=(e=e||{}).stage||{},n}return(0,s.__extends)(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){for(var e=H()-this._pausedTime,n=e-this._time,r=this._head;r;){var i=r.next;r.step(e,n)&&(r.ondestroy(),this.removeClip(r)),r=i}this._time=e,!t&&(this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;this._running=!0,r(function e(){t._running&&(r(e),t._paused||t.update())})},e.prototype.start=function(){this._running||(this._time=H(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=H(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=H()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new V.A(t,e.loop);return this.addAnimator(n),n},e}(h.A),U=o.A.domSupported,W=function(){var t=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},n=a.map(t,function(t){var n=t.replace("mouse","pointer");return e.hasOwnProperty(n)?n:t});return{mouse:t,touch:["touchstart","touchend","touchmove"],pointer:n}}(),G={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},q=!1;function X(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Y(t){t&&(t.zrByTouch=!0)}function Z(t,e){for(var n=e,r=!1;n&&9!==n.nodeType&&!(r=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return r}var $=function(t,e){this.stopPropagation=a.noop,this.stopImmediatePropagation=a.noop,this.preventDefault=a.noop,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},K={mousedown:function(t){t=_(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=_(this.dom,t);var e=this.__mayPointerCapture;e&&(t.zrX!==e[0]||t.zrY!==e[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=_(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Z(this,(t=_(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){q=!0,t=_(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){q||(t=_(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Y(t=_(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),K.mousemove.call(this,t),K.mousedown.call(this,t)},touchmove:function(t){Y(t=_(this.dom,t)),this.handler.processGesture(t,"change"),K.mousemove.call(this,t)},touchend:function(t){Y(t=_(this.dom,t)),this.handler.processGesture(t,"end"),K.mouseup.call(this,t),new Date-this.__lastTouchMoment<300&&K.click.call(this,t)},pointerdown:function(t){K.mousedown.call(this,t)},pointermove:function(t){X(t)||K.mousemove.call(this,t)},pointerup:function(t){K.mouseup.call(this,t)},pointerout:function(t){X(t)||K.mouseout.call(this,t)}};a.each(["click","dblclick","contextmenu"],function(t){K[t]=function(e){e=_(this.dom,e),this.trigger(t,e)}});var Q={pointermove:function(t){X(t)||Q.mousemove.call(this,t)},pointerup:function(t){Q.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function J(t,e,n,r){var i;t.mounted[e]=n,t.listenerOpts[e]=r,i=t.domTarget,i.addEventListener(e,n,r)}function tt(t){var e,n,r,i=t.mounted;for(var o in i)i.hasOwnProperty(o)&&(e=t.domTarget,n=i[o],r=t.listenerOpts[o],e.removeEventListener(o,n,r));t.mounted={}}var te=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},tn=function(t){function e(e,n){var r,i,s=t.call(this)||this;return s.__pointerCapturing=!1,s.dom=e,s.painterRoot=n,s._localHandlerScope=new te(e,K),U&&(s._globalHandlerScope=new te(document,Q)),i=(r=s._localHandlerScope).domHandlers,o.A.pointerEventsSupported?a.each(W.pointer,function(t){J(r,t,function(e){i[t].call(s,e)})}):(o.A.touchEventsSupported&&a.each(W.touch,function(t){J(r,t,function(e){i[t].call(s,e),r.touching=!0,null!=r.touchTimer&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)})}),a.each(W.mouse,function(t){J(r,t,function(e){e=m(e),r.touching||i[t].call(s,e)})})),s}return(0,s.__extends)(e,t),e.prototype.dispose=function(){tt(this._localHandlerScope),U&&tt(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,U&&this.__pointerCapturing^t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?function(t,e){o.A.pointerEventsSupported?a.each(G.pointer,n):o.A.touchEventsSupported||a.each(G.mouse,n);function n(n){J(e,n,function(r){if(!Z(t,(r=m(r)).target)){var i;i=r,r=_(t.dom,new $(t,i),!0),e.domHandlers[n].call(t,r)}},{capture:!0})}}(this,e):tt(e)}},e}(h.A),tr=n(411),ti=n(4035),to=n(6631),ta={},ts={},tu=function(){function t(t,e,n){var r,i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var s=new z,u=n.renderer||"canvas";ta[u]||(u=a.keys(ta)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var l=new ta[u](e,s,n,t),c=n.ssr||l.ssrOnly;this.storage=s,this.painter=l;var h=o.A.node||o.A.worker||c?null:new tn(l.getViewportRoot(),l.root),f=n.useCoarsePointer;(null==f||"auto"===f?o.A.touchEventsSupported:!!f)&&(r=a.retrieve2(n.pointerSize,44)),this.handler=new O(s,l,h,l.root,r),this.animation=new j({stage:{update:c?null:function(){return i._flush(!0)}}}),c||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return(0,tr.lum)(t,1)<ti.ps;if(t.colorStops){for(var e=t.colorStops,n=0,r=e.length,i=0;i<r;i++)n+=(0,tr.lum)(e[i].color,1);return(n/=r)<ti.ps}return!1}(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,n=H();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var r=H();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:r-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){!this._disposed&&(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof to.A&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){if(!this._disposed){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete ts[t]}},t}();function tl(t,e){var n=new tu(a.guid(),t,e);return ts[n.id]=n,n}function tc(t){t.dispose()}function th(){for(var t in ts)ts.hasOwnProperty(t)&&ts[t].dispose();ts={}}function tf(t){return ts[t]}function tp(t,e){ta[t]=e}function td(t){if("function"==typeof i)return i(t)}function tg(t){i=t}var ty="5.6.1"},7355:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.removeSensor=e.getSensor=e.Sensors=void 0;var r=function(t){return t&&t.__esModule?t:{default:t}}(n(8614)),i=n(5225),o=n(3615),a={};function s(t){t&&a[t]&&delete a[t]}e.Sensors=a,e.getSensor=function(t){var e=t.getAttribute(o.SizeSensorId);if(e&&a[e])return a[e];var n=(0,r.default)();t.setAttribute(o.SizeSensorId,n);var u=(0,i.createSensor)(t,function(){return s(n)});return a[n]=u,u},e.removeSensor=function(t){var e=t.element.getAttribute(o.SizeSensorId);t.destroy(),s(e)}},7369:(t,e,n)=>{"use strict";n.d(e,{a:()=>X});var r=n(643),i=n(4123),o=n(4436),a=n(3875),s=n(8991),u=n(7143),l=n(2392),c=n(169),h=n(2663),f=n(6847);function p(t,e,n){var r=t.get("borderRadius");if(null==r)return n?{cornerRadius:0}:null;(0,i.isArray)(r)||(r=[r,r,r,r]);var o=Math.abs(e.r||0-e.r0||0);return{cornerRadius:(0,i.map)(r,function(t){return(0,f.lo)(t,o)})}}var d=n(5942),g=n(6748),y=n(3607),v=function(t){function e(e,n,r,i){var o=t.call(this)||this;o.z2=2,o.textConfig={inside:!0},(0,h.z)(o).seriesIndex=n.seriesIndex;var s=new a.Ay({z2:4,silent:e.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,e,n,r,i),o}return(0,r.__extends)(e,t),e.prototype.updateData=function(t,e,n,r,o){this.node=e,e.piece=this,n=n||this._seriesModel,r=r||this._ecModel;var a=this;(0,h.z)(a).dataIndex=e.dataIndex;var u=e.getModel(),c=u.getModel("emphasis"),f=e.getLayout(),g=i.extend({},f);g.label=null;var y=e.getVisual("style");y.lineJoin="bevel";var v=e.getVisual("decal");v&&(y.decal=(0,d.w)(v,o));var m=p(u.getModel("itemStyle"),g,!0);i.extend(g,m),i.each(l.BV,function(t){var e=a.ensureState(t),n=u.getModel([t,"itemStyle"]);e.style=n.getItemStyle();var r=p(n,g);r&&(e.shape=r)}),t?(a.setShape(g),a.shape.r=f.r0,s.LW(a,{shape:{r:f.r}},n,e.dataIndex)):(s.oi(a,{shape:g},n),(0,s.ap)(a)),a.useStyle(y),this._updateLabel(n);var _=u.getShallow("cursor");_&&a.attr("cursor",_),this._seriesModel=n||this._seriesModel,this._ecModel=r||this._ecModel;var x=c.get("focus"),b="relative"===x?i.concatArray(e.getAncestorsIndices(),e.getDescendantIndices()):"ancestor"===x?e.getAncestorsIndices():"descendant"===x?e.getDescendantIndices():x;(0,l.Lm)(this,b,c.get("blurScope"),c.get("disabled"))},e.prototype._updateLabel=function(t){var e=this,n=this.node.getModel(),r=n.getModel("label"),o=this.node.getLayout(),a=o.endAngle-o.startAngle,s=(o.startAngle+o.endAngle)/2,u=Math.cos(s),h=Math.sin(s),f=this,p=f.getTextContent(),d=this.node.dataIndex,v=r.get("minAngle")/180*Math.PI;function m(t,e){var n=t.get(e);return null==n?r.get(e):n}p.ignore=!(r.get("show")&&!(null!=v&&Math.abs(a)<v)),i.each(l.wV,function(r){var l,v="normal"===r?n.getModel("label"):n.getModel([r,"label"]),_="normal"===r,x=_?p:p.ensureState(r),b=t.getFormattedLabel(d,r);_&&(b=b||e.node.name),x.style=(0,c.VB)(v,{},null,"normal"!==r,!0),b&&(x.style.text=b);var w=v.get("show");null==w||_||(x.ignore=!w);var S=m(v,"position"),M=_?f:f.states[r],A=M.style.fill;M.textConfig={outsideFill:"inherit"===v.get("color")?A:null,inside:"outside"!==S};var T=m(v,"distance")||0,k=m(v,"align"),C=m(v,"rotate"),I=.5*Math.PI,D=1.5*Math.PI,O=(0,g.n)("tangential"===C?Math.PI/2-s:s),P=O>I&&!(0,y.dh)(O-I)&&O<D;"outside"===S?(l=o.r+T,k=P?"right":"left"):k&&"center"!==k?"left"===k?(l=o.r0+T,k=P?"right":"left"):"right"===k&&(l=o.r-T,k=P?"left":"right"):(l=a===2*Math.PI&&0===o.r0?0:(o.r+o.r0)/2,k="center"),x.style.align=k,x.style.verticalAlign=m(v,"verticalAlign")||"middle",x.x=l*u+o.cx,x.y=l*h+o.cy;var L=0;"radial"===C?L=(0,g.n)(-s)+(P?Math.PI:0):"tangential"===C?L=(0,g.n)(Math.PI/2-s)+(P?Math.PI:0):i.isNumber(C)&&(L=C*Math.PI/180),x.rotation=(0,g.n)(L)}),p.dirtyStyle()},e}(u.A),m=n(442);function _(t,e,n){if(t&&i.indexOf(e,t.type)>=0){var r=n.getData().tree.root,o=t.targetNode;if(i.isString(o)&&(o=r.getNodeById(o)),o&&r.contains(o))return{node:o};var a=t.targetNodeId;if(null!=a&&(o=r.getNodeById(a)))return{node:o}}}var x="sunburstRootToNode",b="sunburstHighlight",w=n(2261),S=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return(0,r.__extends)(e,t),e.prototype.render=function(t,e,n,r){var o,a,s=this;this.seriesModel=t,this.api=n,this.ecModel=e;var u=t.getData(),l=u.tree.root,c=t.getViewRoot(),h=this.group,f=t.get("renderLabelForZeroData"),p=[];c.eachNode(function(t){p.push(t)}),function(r,o){function a(t){return t.getId()}function s(i,a){!function(r,i){if(f||!r||r.getValue()||(r=null),r!==l&&i!==l){if(i&&i.piece){var o;r?(i.piece.updateData(!1,r,t,e,n),u.setItemGraphicEl(r.dataIndex,i.piece)):(o=i)&&o.piece&&(h.remove(o.piece),o.piece=null)}else if(r){var a=new v(r,t,e,n);h.add(a),u.setItemGraphicEl(r.dataIndex,a)}}}(null==i?null:r[i],null==a?null:o[a])}(0!==r.length||0!==o.length)&&new m.A(o,r,a,a).add(s).update(s).remove(i.curry(s,null)).execute()}(p,this._oldChildren||[]),o=l,(a=c).depth>0?(s.virtualPiece?s.virtualPiece.updateData(!1,o,t,e,n):(s.virtualPiece=new v(o,t,e,n),h.add(s.virtualPiece)),a.piece.off("click"),s.virtualPiece.on("click",function(t){s._rootToNode(a.parentNode)})):s.virtualPiece&&(h.remove(s.virtualPiece),s.virtualPiece=null),this._initEvents(),this._oldChildren=p},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(e){var n=!1;t.seriesModel.getViewRoot().eachNode(function(r){if(!n&&r.piece&&r.piece===e.target){var i=r.getModel().get("nodeClick");if("rootToNode"===i)t._rootToNode(r);else if("link"===i){var o=r.getModel(),a=o.get("link");if(a){var s=o.get("target",!0)||"_blank";(0,w.JW)(a,s)}}n=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:x,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var r=t[0]-n.cx,i=t[1]-n.cy,o=Math.sqrt(r*r+i*i);return o<=n.r&&o>=n.r0}},e.type="sunburst",e}(o.A),M=n(2403),A=n(6187),T=(0,A.$r)();function k(t,e){var n;if(n=this,T(n).mainData===n){var r=(0,i.extend)({},T(this).datas);r[this.dataType]=e,P(e,r,t)}else L(e,this.dataType,T(this).mainData,t);return e}function C(t,e){return t.struct&&t.struct.update(),e}function I(t,e){return(0,i.each)(T(e).datas,function(n,r){n!==e&&L(n.cloneShallow(),r,e,t)}),e}function D(t){var e=T(this).mainData;return null==t||null==e?e:T(e).datas[t]}function O(){var t=T(this).mainData;return null==t?[{data:t}]:(0,i.map)((0,i.keys)(T(t).datas),function(e){return{type:e,data:T(t).datas[e]}})}function P(t,e,n){T(t).datas={},(0,i.each)(e,function(e,r){L(e,r,t,n)})}function L(t,e,n,r){T(n).datas[e]=t,T(t).mainData=n,t.dataType=e,r.struct&&(t[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=t),t.getLinkedData=D,t.getLinkedDataAll=O}let R=function(t){var e=t.mainData,n=t.datas;n||(n={main:e},t.datasAttr={main:"data"}),t.datas=t.mainData=null,P(e,n,t),(0,i.each)(n,function(n){(0,i.each)(e.TRANSFERABLE_METHODS,function(e){n.wrapMethod(e,(0,i.curry)(k,t))})}),e.wrapMethod("cloneShallow",(0,i.curry)(I,t)),(0,i.each)(e.CHANGABLE_METHODS,function(n){e.wrapMethod(n,(0,i.curry)(C,t))}),(0,i.assert)(n[e.dataType]===e)};var E=n(5327),N=n(1598),B=function(){function t(t,e){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=t||"",this.hostTree=e}return t.prototype.isRemoved=function(){return this.dataIndex<0},t.prototype.eachNode=function(t,e,n){i.isFunction(t)&&(n=e,e=t,t=null),t=t||{},i.isString(t)&&(t={order:t});var r,o=t.order||"preorder",a=this[t.attr||"children"];"preorder"===o&&(r=e.call(n,this));for(var s=0;!r&&s<a.length;s++)a[s].eachNode(t,e,n);"postorder"===o&&e.call(n,this)},t.prototype.updateDepthAndHeight=function(t){var e=0;this.depth=t;for(var n=0;n<this.children.length;n++){var r=this.children[n];r.updateDepthAndHeight(t+1),r.height>e&&(e=r.height)}this.height=e+1},t.prototype.getNodeById=function(t){if(this.getId()===t)return this;for(var e=0,n=this.children,r=n.length;e<r;e++){var i=n[e].getNodeById(t);if(i)return i}},t.prototype.contains=function(t){if(t===this)return!0;for(var e=0,n=this.children,r=n.length;e<r;e++){var i=n[e].contains(t);if(i)return i}},t.prototype.getAncestors=function(t){for(var e=[],n=t?this:this.parentNode;n;)e.push(n),n=n.parentNode;return e.reverse(),e},t.prototype.getAncestorsIndices=function(){for(var t=[],e=this;e;)t.push(e.dataIndex),e=e.parentNode;return t.reverse(),t},t.prototype.getDescendantIndices=function(){var t=[];return this.eachNode(function(e){t.push(e.dataIndex)}),t},t.prototype.getValue=function(t){var e=this.hostTree.data;return e.getStore().get(e.getDimensionIndex(t||"value"),this.dataIndex)},t.prototype.setLayout=function(t,e){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,t,e)},t.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostTree.data.getItemModel(this.dataIndex).getModel(t)},t.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},t.prototype.setVisual=function(t,e){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,t,e)},t.prototype.getVisual=function(t){return this.hostTree.data.getItemVisual(this.dataIndex,t)},t.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},t.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},t.prototype.getChildIndex=function(){if(this.parentNode){for(var t=this.parentNode.children,e=0;e<t.length;++e)if(t[e]===this)return e}return -1},t.prototype.isAncestorOf=function(t){for(var e=t.parentNode;e;){if(e===this)return!0;e=e.parentNode}return!1},t.prototype.isDescendantOf=function(t){return t!==this&&t.isAncestorOf(this)},t}(),F=function(){function t(t){this.type="tree",this._nodes=[],this.hostModel=t}return t.prototype.eachNode=function(t,e,n){this.root.eachNode(t,e,n)},t.prototype.getNodeByDataIndex=function(t){var e=this.data.getRawIndex(t);return this._nodes[e]},t.prototype.getNodeById=function(t){return this.root.getNodeById(t)},t.prototype.update=function(){for(var t=this.data,e=this._nodes,n=0,r=e.length;n<r;n++)e[n].dataIndex=-1;for(var n=0,r=t.count();n<r;n++)e[t.getRawIndex(n)].dataIndex=n},t.prototype.clearLayouts=function(){this.data.clearItemLayouts()},t.createTree=function(e,n,r){var o=new t(n),a=[],s=1;!function t(e,n){var r,u,l,c=e.value;s=Math.max(s,i.isArray(c)?c.length:1),a.push(e);var h=new B((0,A.vS)(e.name,""),o);n?(r=h,l=(u=n).children,r.parentNode!==u&&(l.push(r),r.parentNode=u)):o.root=h,o._nodes.push(h);var f=e.children;if(f)for(var p=0;p<f.length;p++)t(f[p],h)}(e),o.root.updateDepthAndHeight(0);var u=(0,N.A)(a,{coordDimensions:["value"],dimensionsCount:s}).dimensions,l=new E.A(u,n);return l.initData(a),r&&r(l),R({mainData:l,struct:o,structAttr:"tree"}),o.update(),o},t}(),z=n(1613),V=n(7156),H=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.ignoreStyleOnData=!0,n}return(0,r.__extends)(e,t),e.prototype.getInitialData=function(t,e){var n={name:t.name,children:t.data};!function t(e){var n=0;i.each(e.children,function(e){t(e);var r=e.value;i.isArray(r)&&(r=r[0]),n+=r});var r=e.value;i.isArray(r)&&(r=r[0]),(null==r||isNaN(r))&&(r=n),r<0&&(r=0),i.isArray(e.value)?e.value[0]=r:e.value=r}(n);var r=this._levelModels=i.map(t.levels||[],function(t){return new z.A(t,this,e)},this),o=F.createTree(n,this,function(t){t.wrapMethod("getItemModel",function(t,e){var n=r[o.getNodeByDataIndex(e).depth];return n&&(t.parentModel=n),t})});return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments);return n.treePathInfo=function(t,e){for(var n=[];t;){var r=t.dataIndex;n.push({name:t.name,dataIndex:r,value:e.getRawValue(r)}),t=t.parentNode}return n.reverse(),n}(this.getData().tree.getNodeByDataIndex(e),this),n},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var e=this.getRawData().tree.root;t&&(t===e||e.contains(t))||(this._viewRoot=e)},e.prototype.enableAriaDecal=function(){var t,e,n;t=this,e=t.getData().tree,n={},e.eachNode(function(e){for(var r=e;r&&r.depth>1;)r=r.parentNode;var i=(0,V.x)(t.ecModel,r.name||r.dataIndex+"",n);e.setVisual("decal",i)})},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(M.A),j=Math.PI/180;function U(t,e,n){e.eachSeriesByType(t,function(t){var e=t.get("center"),r=t.get("radius");i.isArray(r)||(r=[0,r]),i.isArray(e)||(e=[e,e]);var o=n.getWidth(),a=n.getHeight(),s=Math.min(o,a),u=(0,y.lo)(e[0],o),l=(0,y.lo)(e[1],a),c=(0,y.lo)(r[0],s/2),h=(0,y.lo)(r[1],s/2),f=-t.get("startAngle")*j,p=t.get("minAngle")*j,d=t.getData().tree.root,g=t.getViewRoot(),v=g.depth,m=t.get("sort");null!=m&&function t(e,n){var r=e.children||[];e.children=function(t,e){if(i.isFunction(e)){var n=i.map(t,function(t,e){var n=t.getValue();return{params:{depth:t.depth,height:t.height,dataIndex:t.dataIndex,getValue:function(){return n}},index:e}});return n.sort(function(t,n){return e(t.params,n.params)}),i.map(n,function(e){return t[e.index]})}var r="asc"===e;return t.sort(function(t,e){var n=(t.getValue()-e.getValue())*(r?1:-1);return 0===n?(t.dataIndex-e.dataIndex)*(r?-1:1):n})}(r,n),r.length&&i.each(e.children,function(e){t(e,n)})}(g,m);var _=0;i.each(g.children,function(t){!isNaN(t.getValue())&&_++});var x=g.getValue(),b=Math.PI/(x||_)*2,w=g.depth>0,S=(h-c)/(g.height-(w?-1:1)||1),M=t.get("clockwise"),A=t.get("stillShowZeroSum"),T=M?1:-1,k=function(e,n){if(e){var r=n;if(e!==d){var o=e.getValue(),a=0===x&&A?b:o*b;a<p&&(a=p),r=n+T*a;var h=e.depth-v-(w?-1:1),f=c+S*h,g=c+S*(h+1),m=t.getLevelModel(e);if(m){var _=m.get("r0",!0),C=m.get("r",!0),I=m.get("radius",!0);null!=I&&(_=I[0],C=I[1]),null!=_&&(f=(0,y.lo)(_,s/2)),null!=C&&(g=(0,y.lo)(C,s/2))}e.setLayout({angle:a,startAngle:n,endAngle:r,clockwise:M,cx:u,cy:l,r0:f,r:g})}if(e.children&&e.children.length){var D=0;i.each(e.children,function(t){D+=k(t,n+D)})}return r-n}};if(w){var C=2*Math.PI;d.setLayout({angle:C,startAngle:f,endAngle:f+C,clockwise:M,cx:u,cy:l,r0:c,r:c+S})}k(g,f)})}var W=n(411);function G(t){var e={};t.eachSeriesByType("sunburst",function(t){var n=t.getData(),r=n.tree;r.eachNode(function(o){var a=o.getModel().getModel("itemStyle").getItemStyle();a.fill||(a.fill=function(t,n,r){for(var o=t;o&&o.depth>1;)o=o.parentNode;var a=n.getColorFromPalette(o.name||o.dataIndex+"",e);return t.depth>1&&(0,i.isString)(a)&&(a=(0,W.lift)(a,(t.depth-1)/(r-1)*.5)),a}(o,t,r.root.height));var s=n.ensureUniqueItemVisual(o.dataIndex,"style");(0,i.extend)(s,a)})})}function q(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var r=t.getData();r.filterSelf(function(t){for(var e=r.getName(t),i=0;i<n.length;i++)if(!n[i].isSelected(e))return!1;return!0})}}}}function X(t){t.registerChartView(S),t.registerSeriesModel(H),t.registerLayout((0,i.curry)(U,"sunburst")),t.registerProcessor((0,i.curry)(q,"sunburst")),t.registerVisual(G),t.registerAction({type:x,update:"updateView"},function(t,e){e.eachComponent({mainType:"series",subType:"sunburst",query:t},function(e,n){var r=_(t,[x],e);if(r){var o,a,s=e.getViewRoot();s&&(o=r.node,a=function(t){for(var e=[];t;)(t=t.parentNode)&&e.push(t);return e.reverse()}(s),t.direction=i.indexOf(a,o)>=0?"rollUp":"drillDown"),e.resetViewRoot(r.node)}})}),t.registerAction({type:b,update:"none"},function(t,e,n){t=(0,i.extend)({},t),e.eachComponent({mainType:"series",subType:"sunburst",query:t},function(e){var n=_(t,[b],e);n&&(t.dataIndex=n.node.dataIndex)}),n.dispatchAction((0,i.extend)(t,{type:"highlight"}))}),t.registerAction({type:"sunburstUnhighlight",update:"updateView"},function(t,e,n){t=(0,i.extend)({},t),n.dispatchAction((0,i.extend)(t,{type:"downplay"}))})}},7416:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});var r=n(4123),i={};let o=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];r.each(i,function(r,i){var o=r.create(t,e);n=n.concat(o||[])}),this._coordinateSystems=n},t.prototype.update=function(t,e){r.each(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){i[t]=e},t.get=function(t){return i[t]},t}()},7439:(t,e,n)=>{"use strict";n.d(e,{Km:()=>o,Pe:()=>r,Wk:()=>a,XO:()=>u,i_:()=>c,mK:()=>i,oC:()=>h,t1:()=>s,vm:()=>l});var r=(0,n(4123).createHashMap)(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),i="original",o="arrayRows",a="objectRows",s="keyedColumns",u="typedArray",l="unknown",c="column",h="row"},7628:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(2115);let i=r.forwardRef(function({title:t,titleId:e,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":e},n),t?r.createElement("title",{id:e},t):null,r.createElement("path",{fillRule:"evenodd",d:"M8 14a.75.75 0 0 1-.75-.75V4.56L4.03 7.78a.75.75 0 0 1-1.06-1.06l4.5-4.5a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06L8.75 4.56v8.69A.75.75 0 0 1 8 14Z",clipRule:"evenodd"}))})},7669:(t,e,n)=>{"use strict";n.d(e,{A:()=>p});var r=n(8346),i=n(2227),o=Math.min,a=Math.max,s=new i.A,u=new i.A,l=new i.A,c=new i.A,h=new i.A,f=new i.A;let p=function(){function t(t,e,n,r){n<0&&(t+=n,n=-n),r<0&&(e+=r,r=-r),this.x=t,this.y=e,this.width=n,this.height=r}return t.prototype.union=function(t){var e=o(t.x,this.x),n=o(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=a(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=a(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=r.create();return r.translate(i,i,[-this.x,-this.y]),r.scale(i,i,[e,n]),r.translate(i,i,[t.x,t.y]),i},t.prototype.intersect=function(e,n){if(!e)return!1;e instanceof t||(e=t.create(e));var r=this.x,o=this.x+this.width,a=this.y,s=this.y+this.height,u=e.x,l=e.x+e.width,c=e.y,p=e.y+e.height,d=!(o<u||l<r||s<c||p<a);if(n){var g=1/0,y=0,v=Math.abs(o-u),m=Math.abs(l-r),_=Math.abs(s-c),x=Math.abs(p-a),b=Math.min(v,m),w=Math.min(_,x);o<u||l<r?b>y&&(y=b,v<m?i.A.set(f,-v,0):i.A.set(f,m,0)):b<g&&(g=b,v<m?i.A.set(h,v,0):i.A.set(h,-m,0)),s<c||p<a?w>y&&(y=w,_<x?i.A.set(f,0,-_):i.A.set(f,0,x)):b<g&&(g=b,_<x?i.A.set(h,0,_):i.A.set(h,0,-x))}return n&&i.A.copy(n,d?h:f),d},t.prototype.contain=function(t,e){return t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,r){if(!r){e!==n&&t.copy(e,n);return}if(r[1]<1e-5&&r[1]>-1e-5&&r[2]<1e-5&&r[2]>-1e-5){var i=r[0],h=r[3],f=r[4],p=r[5];e.x=n.x*i+f,e.y=n.y*h+p,e.width=n.width*i,e.height=n.height*h,e.width<0&&(e.x+=e.width,e.width=-e.width),e.height<0&&(e.y+=e.height,e.height=-e.height);return}s.x=l.x=n.x,s.y=c.y=n.y,u.x=c.x=n.x+n.width,u.y=l.y=n.y+n.height,s.transform(r),c.transform(r),u.transform(r),l.transform(r),e.x=o(s.x,u.x,l.x,c.x),e.y=o(s.y,u.y,l.y,c.y);var d=a(s.x,u.x,l.x,c.x),g=a(s.y,u.y,l.y,c.y);e.width=d-e.x,e.height=g-e.y},t}()},7821:(t,e,n)=>{"use strict";n.d(e,{A:()=>l});var r=n(643),i=n(4271),o=n(6600),a=function(){this.x=0,this.y=0,this.width=0,this.height=0},s={},u=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){if(this.subPixelOptimize){var n,r,i,a,u,l,c,h,f,p,d,g,y,v,m,_=(0,o.Op)(s,e,this.style);g=_.x,y=_.y,v=_.width,m=_.height,_.r=e.r,e=_}else g=e.x,y=e.y,v=e.width,m=e.height;e.r?(c=(n=e).x,h=n.y,f=n.width,p=n.height,d=n.r,f<0&&(c+=f,f=-f),p<0&&(h+=p,p=-p),"number"==typeof d?r=i=a=u=d:d instanceof Array?1===d.length?r=i=a=u=d[0]:2===d.length?(r=a=d[0],i=u=d[1]):3===d.length?(r=d[0],i=u=d[1],a=d[2]):(r=d[0],i=d[1],a=d[2],u=d[3]):r=i=a=u=0,r+i>f&&(l=r+i,r*=f/l,i*=f/l),a+u>f&&(l=a+u,a*=f/l,u*=f/l),i+a>p&&(l=i+a,i*=p/l,a*=p/l),r+u>p&&(l=r+u,r*=p/l,u*=p/l),t.moveTo(c+r,h),t.lineTo(c+f-i,h),0!==i&&t.arc(c+f-i,h+i,i,-Math.PI/2,0),t.lineTo(c+f,h+p-a),0!==a&&t.arc(c+f-a,h+p-a,a,0,Math.PI/2),t.lineTo(c+u,h+p),0!==u&&t.arc(c+u,h+p-u,u,Math.PI/2,Math.PI),t.lineTo(c,h+r),0!==r&&t.arc(c+r,h+r,r,Math.PI,1.5*Math.PI)):t.rect(g,y,v,m)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(i.Ay);u.prototype.type="rect";let l=u},8072:(t,e,n)=>{"use strict";n.d(e,{A:()=>A});var r=n(2115);function i(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function o(t,e){let n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&n.every(n=>{let r=t[n],a=e[n];return"function"==typeof r?`${r}`==`${a}`:i(r)&&i(a)?o(r,a):r===a})}function a(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function s(t){return"number"==typeof t}function u(t){return"string"==typeof t}function l(t){return"boolean"==typeof t}function c(t){return"[object Object]"===Object.prototype.toString.call(t)}function h(t){return Math.abs(t)}function f(t){return Math.sign(t)}function p(t){return v(t).map(Number)}function d(t){return t[g(t)]}function g(t){return Math.max(0,t.length-1)}function y(t,e=0){return Array.from(Array(t),(t,n)=>e+n)}function v(t){return Object.keys(t)}function m(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function _(){let t=[],e={add:function(n,r,i,o={passive:!0}){let a;return"addEventListener"in n?(n.addEventListener(r,i,o),a=()=>n.removeEventListener(r,i,o)):(n.addListener(i),a=()=>n.removeListener(i)),t.push(a),e},clear:function(){t=t.filter(t=>t())}};return e}function x(t=0,e=0){let n=h(t-e);function r(n){return n<t||n>e}return{length:n,max:e,min:t,constrain:function(n){return r(n)?n<t?t:e:n},reachedAny:r,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return n?t-n*Math.ceil((t-e)/n):t}}}function b(t){let e=t;function n(t){return s(t)?t:t.get()}return{get:function(){return e},set:function(t){e=n(t)},add:function(t){e+=n(t)},subtract:function(t){e-=n(t)}}}function w(t,e){let n="x"===t.scroll?function(t){return`translate3d(${t}px,0px,0px)`}:function(t){return`translate3d(0px,${t}px,0px)`},r=e.style,i=null,o=!1;return{clear:function(){!o&&(r.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(o)return;let a=Math.round(100*t.direction(e))/100;a!==i&&(r.transform=n(a),i=a)},toggleActive:function(t){o=!t}}}let S={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function M(t,e,n){let r,i,o,a,A,T=t.ownerDocument,k=T.defaultView,C=function(t){function e(t,e){return function t(e,n){return[e,n].reduce((e,n)=>(v(n).forEach(r=>{let i=e[r],o=n[r],a=c(i)&&c(o);e[r]=a?t(i,o):o}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(n){let r=n.breakpoints||{},i=v(r).filter(e=>t.matchMedia(e).matches).map(t=>r[t]).reduce((t,n)=>e(t,n),{});return e(n,i)},optionsMediaQueries:function(e){return e.map(t=>v(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(k),I=(A=[],{init:function(t,e){return(A=e.filter(({options:t})=>!1!==C.optionsAtMedia(t).active)).forEach(e=>e.init(t,C)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){A=A.filter(t=>t.destroy())}}),D=_(),O=function(){let t,e={},n={init:function(e){t=e},emit:function(r){return(e[r]||[]).forEach(e=>e(t,r)),n},off:function(t,r){return e[t]=(e[t]||[]).filter(t=>t!==r),n},on:function(t,r){return e[t]=(e[t]||[]).concat([r]),n},clear:function(){e={}}};return n}(),{mergeOptions:P,optionsAtMedia:L,optionsMediaQueries:R}=C,{on:E,off:N,emit:B}=O,F=!1,z=P(S,M.globalOptions),V=P(z),H=[];function j(e,n){if(F)return;V=L(z=P(z,e)),H=n||H;let{container:c,slides:S}=V;o=(u(c)?t.querySelector(c):c)||t.children[0];let M=u(S)?o.querySelectorAll(S):S;a=[].slice.call(M||o.children),r=function e(n){let r=function(t,e,n,r,i,o,a){let c,S,{align:M,axis:A,direction:T,startIndex:k,loop:C,duration:I,dragFree:D,dragThreshold:O,inViewThreshold:P,slidesToScroll:L,skipSnaps:R,containScroll:E,watchResize:N,watchSlides:B,watchDrag:F,watchFocus:z}=o,V={measure:function(t){let{offsetTop:e,offsetLeft:n,offsetWidth:r,offsetHeight:i}=t;return{top:e,right:n+r,bottom:e+i,left:n,width:r,height:i}}},H=V.measure(e),j=n.map(V.measure),U=function(t,e){let n="rtl"===e,r="y"===t,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(t){let{height:e,width:n}=t;return r?e:n},direction:function(t){return t*i}}}(A,T),W=U.measureSize(H),G={measure:function(t){return t/100*W}},q=function(t,e){let n={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(r,i){return u(t)?n[t](r):t(e,r,i)}}}(M,W),X=!C&&!!E,{slideSizes:Y,slideSizesWithGaps:Z,startGap:$,endGap:K}=function(t,e,n,r,i,o){let{measureSize:a,startEdge:s,endEdge:u}=t,l=n[0]&&i,c=function(){if(!l)return 0;let t=n[0];return h(e[s]-t[s])}(),f=l?parseFloat(o.getComputedStyle(d(r)).getPropertyValue(`margin-${u}`)):0,p=n.map(a),y=n.map((t,e,n)=>{let r=e===g(n);return e?r?p[e]+f:n[e+1][s]-t[s]:p[e]+c}).map(h);return{slideSizes:p,slideSizesWithGaps:y,startGap:c,endGap:f}}(U,H,j,n,C||!!E,i),Q=function(t,e,n,r,i,o,a,u,l){let{startEdge:c,endEdge:f,direction:y}=t,v=s(n);return{groupSlides:function(t){return v?p(t).filter(t=>t%n==0).map(e=>t.slice(e,e+n)):t.length?p(t).reduce((n,s,l)=>{let p=d(n)||0,v=s===g(t),m=i[c]-o[p][c],_=i[c]-o[s][f],x=r||0!==p?0:y(a),b=h(_-(!r&&v?y(u):0)-(m+x));return l&&b>e+2&&n.push(s),v&&n.push(t.length),n},[]).map((e,n,r)=>{let i=Math.max(r[n-1]||0);return t.slice(i,e)}):[]}}}(U,W,L,C,H,j,$,K,0),{snaps:J,snapsAligned:tt}=function(t,e,n,r,i){let{startEdge:o,endEdge:a}=t,{groupSlides:s}=i,u=s(r).map(t=>d(t)[a]-t[0][o]).map(h).map(e.measure),l=r.map(t=>n[o]-t[o]).map(t=>-h(t)),c=s(l).map(t=>t[0]).map((t,e)=>t+u[e]);return{snaps:l,snapsAligned:c}}(U,q,H,j,Q),te=-d(J)+d(Z),{snapsContained:tn,scrollContainLimit:tr}=function(t,e,n,r,i){let o=x(-e+t,0),a=n.map((t,e)=>{let{min:r,max:i}=o,a=o.constrain(t),s=e===g(n);return e?s||function(t,e){return 1>=h(t-e)}(r,a)?r:function(t,e){return 1>=h(t-e)}(i,a)?i:a:i}).map(t=>parseFloat(t.toFixed(3))),s=function(){let t=a[0],e=d(a);return x(a.lastIndexOf(t),a.indexOf(e)+1)}();return{snapsContained:function(){if(e<=t+2)return[o.max];if("keepSnaps"===r)return a;let{min:n,max:i}=s;return a.slice(n,i)}(),scrollContainLimit:s}}(W,te,tt,E,0),ti=X?tn:tt,{limit:to}=function(t,e,n){let r=e[0];return{limit:x(n?r-t:d(e),r)}}(te,ti,C),ta=function t(e,n,r){let{constrain:i}=x(0,e),o=e+1,a=s(n);function s(t){return r?h((o+t)%o):i(t)}function u(){return t(e,a,r)}let l={get:function(){return a},set:function(t){return a=s(t),l},add:function(t){return u().set(a+t)},clone:u};return l}(g(ti),k,C),ts=ta.clone(),tu=p(n),tl=function(t,e,n,r){let i=_(),o=1e3/60,a=null,s=0,u=0;function l(t){if(!u)return;a||(a=t,n(),n());let i=t-a;for(a=t,s+=i;s>=o;)n(),s-=o;r(s/o),u&&(u=e.requestAnimationFrame(l))}function c(){e.cancelAnimationFrame(u),a=null,s=0,u=0}return{init:function(){i.add(t,"visibilitychange",()=>{t.hidden&&(a=null,s=0)})},destroy:function(){c(),i.clear()},start:function(){u||(u=e.requestAnimationFrame(l))},stop:c,update:n,render:r}}(r,i,()=>(({dragHandler:t,scrollBody:e,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(t.pointerDown()),e.seek()})(tS),t=>(({scrollBody:t,translate:e,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:a,dragHandler:s,animation:u,eventHandler:l,scrollBounds:c,options:{loop:h}},f)=>{let p=t.settled(),d=!c.shouldConstrain(),g=h?p:p&&d;g&&!s.pointerDown()&&(u.stop(),l.emit("settle")),g||l.emit("scroll");let y=n.get()*f+i.get()*(1-f);r.set(y),h&&(o.loop(t.direction()),a.loop()),e.to(r.get())})(tS,t)),tc=ti[ta.get()],th=b(tc),tf=b(tc),tp=b(tc),td=b(tc),tg=function(t,e,n,r,i,o){let a=0,s=0,u=i,l=.68,c=t.get(),p=0;function d(t){return u=t,y}function g(t){return l=t,y}let y={direction:function(){return s},duration:function(){return u},velocity:function(){return a},seek:function(){let e=r.get()-t.get(),i=0;return u?(n.set(t),a+=e/u,a*=l,c+=a,t.add(a),i=c-p):(a=0,n.set(r),t.set(r),i=e),s=f(i),p=c,y},settled:function(){return .001>h(r.get()-e.get())},useBaseFriction:function(){return g(.68)},useBaseDuration:function(){return d(i)},useFriction:g,useDuration:d};return y}(th,tp,tf,td,I,.68),ty=function(t,e,n,r,i){let{reachedAny:o,removeOffset:a,constrain:s}=r;function u(t){return t.concat().sort((t,e)=>h(t)-h(e))[0]}function l(e,r){let i=[e,e+n,e-n];if(!t)return e;if(!r)return u(i);let o=i.filter(t=>f(t)===r);return o.length?u(o):d(i)-n}return{byDistance:function(n,r){let u=i.get()+n,{index:c,distance:f}=function(n){let r=t?a(n):s(n),{index:i}=e.map((t,e)=>({diff:l(t-r,0),index:e})).sort((t,e)=>h(t.diff)-h(e.diff))[0];return{index:i,distance:r}}(u),p=!t&&o(u);if(!r||p)return{index:c,distance:n};let d=n+l(e[c]-f,0);return{index:c,distance:d}},byIndex:function(t,n){let r=l(e[t]-i.get(),n);return{index:t,distance:r}},shortcut:l}}(C,ti,te,to,td),tv=function(t,e,n,r,i,o,a){function s(i){let s=i.distance,u=i.index!==e.get();o.add(s),s&&(r.duration()?t.start():(t.update(),t.render(1),t.update())),u&&(n.set(e.get()),e.set(i.index),a.emit("select"))}return{distance:function(t,e){s(i.byDistance(t,e))},index:function(t,n){let r=e.clone().set(t);s(i.byIndex(r.get(),n))}}}(tl,ta,ts,tg,ty,td,a),tm=function(t){let{max:e,length:n}=t;return{get:function(t){return n?-((t-e)/n):0}}}(to),t_=_(),tx=function(t,e,n,r){let i,o={},a=null,s=null,u=!1;return{init:function(){i=new IntersectionObserver(t=>{u||(t.forEach(t=>{o[e.indexOf(t.target)]=t}),a=null,s=null,n.emit("slidesInView"))},{root:t.parentElement,threshold:r}),e.forEach(t=>i.observe(t))},destroy:function(){i&&i.disconnect(),u=!0},get:function(t=!0){if(t&&a)return a;if(!t&&s)return s;let e=v(o).reduce((e,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(t&&i||!t&&!i)&&e.push(r),e},[]);return t&&(a=e),t||(s=e),e}}}(e,n,a,P),{slideRegistry:tb}=function(t,e,n,r,i,o){let{groupSlides:a}=i,{min:s,max:u}=r;return{slideRegistry:function(){let r=a(o);return 1===n.length?[o]:t&&"keepSnaps"!==e?r.slice(s,u).map((t,e,n)=>{let r=e===g(n);return e?r?y(g(o)-d(n)[0]+1,d(n)[0]):t:y(d(n[0])+1)}):r}()}}(X,E,ti,tr,Q,tu),tw=function(t,e,n,r,i,o,a,u){let c={passive:!0,capture:!0},h=0;function f(t){"Tab"===t.code&&(h=new Date().getTime())}return{init:function(p){u&&(o.add(document,"keydown",f,!1),e.forEach((e,f)=>{o.add(e,"focus",e=>{(l(u)||u(p,e))&&function(e){if(new Date().getTime()-h>10)return;a.emit("slideFocusStart"),t.scrollLeft=0;let o=n.findIndex(t=>t.includes(e));s(o)&&(i.useDuration(0),r.index(o,0),a.emit("slideFocus"))}(f)},c)}))}}}(t,n,tb,tv,tg,t_,a,z),tS={ownerDocument:r,ownerWindow:i,eventHandler:a,containerRect:H,slideRects:j,animation:tl,axis:U,dragHandler:function(t,e,n,r,i,o,a,s,u,c,p,d,g,y,v,b,w,S,M){let{cross:A,direction:T}=t,k=["INPUT","SELECT","TEXTAREA"],C={passive:!1},I=_(),D=_(),O=x(50,225).constrain(y.measure(20)),P={mouse:300,touch:400},L={mouse:500,touch:600},R=v?43:25,E=!1,N=0,B=0,F=!1,z=!1,V=!1,H=!1;function j(t){if(!m(t,r)&&t.touches.length>=2)return U(t);let e=o.readPoint(t),n=o.readPoint(t,A),a=h(e-N),u=h(n-B);if(!z&&!H&&(!t.cancelable||!(z=a>u)))return U(t);let l=o.pointerMove(t);a>b&&(V=!0),c.useFriction(.3).useDuration(.75),s.start(),i.add(T(l)),t.preventDefault()}function U(t){let e=p.byDistance(0,!1).index!==d.get(),n=o.pointerUp(t)*(v?L:P)[H?"mouse":"touch"],r=function(t,e){let n=d.add(-1*f(t)),r=p.byDistance(t,!v).distance;return v||h(t)<O?r:w&&e?.5*r:p.byIndex(n.get(),0).distance}(T(n),e),i=function(t,e){var n,r;if(0===t||0===e||h(t)<=h(e))return 0;let i=(n=h(t),r=h(e),h(n-r));return h(i/t)}(n,r);z=!1,F=!1,D.clear(),c.useDuration(R-10*i).useFriction(.68+i/50),u.distance(r,!v),H=!1,g.emit("pointerUp")}function W(t){V&&(t.stopPropagation(),t.preventDefault(),V=!1)}return{init:function(t){M&&I.add(e,"dragstart",t=>t.preventDefault(),C).add(e,"touchmove",()=>void 0,C).add(e,"touchend",()=>void 0).add(e,"touchstart",s).add(e,"mousedown",s).add(e,"touchcancel",U).add(e,"contextmenu",U).add(e,"click",W,!0);function s(s){(l(M)||M(t,s))&&function(t){let s=m(t,r);if((H=s,V=v&&s&&!t.buttons&&E,E=h(i.get()-a.get())>=2,!s||0===t.button)&&!function(t){let e=t.nodeName||"";return k.includes(e)}(t.target)){F=!0,o.pointerDown(t),c.useFriction(0).useDuration(0),i.set(a);let r=H?n:e;D.add(r,"touchmove",j,C).add(r,"touchend",U).add(r,"mousemove",j,C).add(r,"mouseup",U),N=o.readPoint(t),B=o.readPoint(t,A),g.emit("pointerDown")}}(s)}},destroy:function(){I.clear(),D.clear()},pointerDown:function(){return F}}}(U,t,r,i,td,function(t,e){let n,r;function i(t){return t.timeStamp}function o(n,r){let i=r||t.scroll,o=`client${"x"===i?"X":"Y"}`;return(m(n,e)?n:n.touches[0])[o]}return{pointerDown:function(t){return n=t,r=t,o(t)},pointerMove:function(t){let e=o(t)-o(r),a=i(t)-i(n)>170;return r=t,a&&(n=t),e},pointerUp:function(t){if(!n||!r)return 0;let e=o(r)-o(n),a=i(t)-i(n),s=i(t)-i(r)>170,u=e/a;return a&&!s&&h(u)>.1?u:0},readPoint:o}}(U,i),th,tl,tv,tg,ty,ta,a,G,D,O,R,0,F),eventStore:t_,percentOfView:G,index:ta,indexPrevious:ts,limit:to,location:th,offsetLocation:tp,previousLocation:tf,options:o,resizeHandler:function(t,e,n,r,i,o,a){let s,u,c=[t].concat(r),f=[],p=!1;function d(t){return i.measureSize(a.measure(t))}return{init:function(i){o&&(u=d(t),f=r.map(d),s=new ResizeObserver(n=>{(l(o)||o(i,n))&&function(n){for(let o of n){if(p)return;let n=o.target===t,a=r.indexOf(o.target),s=n?u:f[a];if(h(d(n?t:r[a])-s)>=.5){i.reInit(),e.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{c.forEach(t=>s.observe(t))}))},destroy:function(){p=!0,s&&s.disconnect()}}}(e,a,i,n,U,N,V),scrollBody:tg,scrollBounds:function(t,e,n,r,i){let o=i.measure(10),a=i.measure(50),s=x(.1,.99),u=!1;function l(){return!u&&!!t.reachedAny(n.get())&&!!t.reachedAny(e.get())}return{shouldConstrain:l,constrain:function(i){if(!l())return;let u=t.reachedMin(e.get())?"min":"max",c=h(t[u]-e.get()),f=n.get()-e.get(),p=s.constrain(c/a);n.subtract(f*p),!i&&h(f)<o&&(n.set(t.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(t){u=!t}}}(to,tp,td,tg,G),scrollLooper:function(t,e,n,r){let{reachedMin:i,reachedMax:o}=x(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?o(n.get()):-1===e&&i(n.get())))return;let a=-1*e*t;r.forEach(t=>t.add(a))}}}(te,to,tp,[th,tp,tf,td]),scrollProgress:tm,scrollSnapList:ti.map(tm.get),scrollSnaps:ti,scrollTarget:ty,scrollTo:tv,slideLooper:function(t,e,n,r,i,o,a,s,u){let l=p(i),c=p(i).reverse(),h=g(d(c,a[0]),n,!1).concat(g(d(l,e-a[0]-1),-n,!0));function f(t,e){return t.reduce((t,e)=>t-i[e],e)}function d(t,e){return t.reduce((t,n)=>f(t,e)>0?t.concat([n]):t,[])}function g(i,a,l){let c=o.map((t,n)=>({start:t-r[n]+.5+a,end:t+e-.5+a}));return i.map(e=>{let r=l?0:-n,i=l?n:0,o=c[e][l?"end":"start"];return{index:e,loopPoint:o,slideLocation:b(-1),translate:w(t,u[e]),target:()=>s.get()>o?r:i}})}return{canLoop:function(){return h.every(({index:t})=>.1>=f(l.filter(e=>e!==t),e))},clear:function(){h.forEach(t=>t.translate.clear())},loop:function(){h.forEach(t=>{let{target:e,translate:n,slideLocation:r}=t,i=e();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:h}}(U,W,te,Y,Z,J,ti,tp,n),slideFocus:tw,slidesHandler:(S=!1,{init:function(t){B&&(c=new MutationObserver(e=>{!S&&(l(B)||B(t,e))&&function(e){for(let n of e)if("childList"===n.type){t.reInit(),a.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){c&&c.disconnect(),S=!0}}),slidesInView:tx,slideIndexes:tu,slideRegistry:tb,slidesToScroll:Q,target:td,translate:w(U,e)};return tS}(t,o,a,T,k,n,O);return n.loop&&!r.slideLooper.canLoop()?e(Object.assign({},n,{loop:!1})):r}(V),R([z,...H.map(({options:t})=>t)]).forEach(t=>D.add(t,"change",U)),V.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(X),r.eventHandler.init(X),r.resizeHandler.init(X),r.slidesHandler.init(X),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&a.length&&r.dragHandler.init(X),i=I.init(X,H))}function U(t,e){let n=q();W(),j(P({startIndex:n},t),e),O.emit("reInit")}function W(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),I.destroy(),D.clear()}function G(t,e,n){V.active&&!F&&(r.scrollBody.useBaseFriction().useDuration(!0===e?0:V.duration),r.scrollTo.index(t,n||0))}function q(){return r.index.get()}let X={canScrollNext:function(){return r.index.add(1).get()!==q()},canScrollPrev:function(){return r.index.add(-1).get()!==q()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){F||(F=!0,D.clear(),W(),O.emit("destroy"),O.clear())},off:N,on:E,emit:B,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:U,rootNode:function(){return t},scrollNext:function(t){G(r.index.add(1).get(),t,-1)},scrollPrev:function(t){G(r.index.add(-1).get(),t,1)},scrollProgress:function(){return r.scrollProgress.get(r.location.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:G,selectedScrollSnap:q,slideNodes:function(){return a},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return j(e,n),setTimeout(()=>O.emit("init"),0),X}function A(t={},e=[]){let n=(0,r.useRef)(t),i=(0,r.useRef)(e),[s,u]=(0,r.useState)(),[l,c]=(0,r.useState)(),h=(0,r.useCallback)(()=>{s&&s.reInit(n.current,i.current)},[s]);return(0,r.useEffect)(()=>{o(n.current,t)||(n.current=t,h())},[t,h]),(0,r.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let n=a(t),r=a(e);return n.every((t,e)=>o(t,r[e]))}(i.current,e)&&(i.current=e,h())},[e,h]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&l){M.globalOptions=A.globalOptions;let t=M(l,n.current,i.current);return u(t),()=>t.destroy()}u(void 0)},[l,u]),[c,s]}M.globalOptions=void 0,A.globalOptions=void 0},8118:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ver=e.clear=e.bind=void 0;var r=n(7355);e.bind=function(t,e){var n=(0,r.getSensor)(t);return n.bind(e),function(){n.unbind(e)}},e.clear=function(t){var e=(0,r.getSensor)(t);(0,r.removeSensor)(e)},e.ver="1.0.2"},8142:(t,e,n)=>{"use strict";n.d(e,{G9:()=>c,Gt:()=>h,OC:()=>l,PU:()=>f,ps:()=>u,sc:()=>a});var r=n(6187),i=n(4123),o=n(7439),a={Must:1,Might:2,Not:3},s=(0,r.$r)();function u(t){s(t).datasetMap=(0,i.createHashMap)()}function l(t,e,n){var r,o,a={},u=c(e);if(!u||!t)return a;var l=[],h=[],f=s(e.ecModel).datasetMap,p=u.uid+"_"+n.seriesLayoutBy;t=t.slice(),(0,i.each)(t,function(e,n){var s=(0,i.isObject)(e)?e:t[n]={name:e};"ordinal"===s.type&&null==r&&(r=n,o=y(s)),a[s.name]=[]});var d=f.get(p)||f.set(p,{categoryWayDim:o,valueWayDim:0});function g(t,e,n){for(var r=0;r<n;r++)t.push(e+r)}function y(t){var e=t.dimsDef;return e?e.length:1}return(0,i.each)(t,function(t,e){var n=t.name,i=y(t);if(null==r){var o=d.valueWayDim;g(a[n],o,i),g(h,o,i),d.valueWayDim+=i}else if(r===e)g(a[n],0,i),g(l,0,i);else{var o=d.categoryWayDim;g(a[n],o,i),g(h,o,i),d.categoryWayDim+=i}}),l.length&&(a.itemName=l),h.length&&(a.seriesName=h),a}function c(t){if(!t.get("data",!0))return(0,r.JO)(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},r.US).models[0]}function h(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?(0,r.JO)(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},r.US).models:[]}function f(t,e){return function(t,e,n,s,u,l){var c,h,f;if((0,i.isTypedArray)(t))return a.Not;if(s){var p=s[l];(0,i.isObject)(p)?(h=p.name,f=p.type):(0,i.isString)(p)&&(h=p)}if(null!=f)return"ordinal"===f?a.Must:a.Not;if(e===o.Km)if(n===o.oC){for(var d=t[l],g=0;g<(d||[]).length&&g<5;g++)if(null!=(c=_(d[u+g])))return c}else for(var g=0;g<t.length&&g<5;g++){var y=t[u+g];if(y&&null!=(c=_(y[l])))return c}else if(e===o.Wk){if(!h)return a.Not;for(var g=0;g<t.length&&g<5;g++){var v=t[g];if(v&&null!=(c=_(v[h])))return c}}else if(e===o.t1){if(!h)return a.Not;var d=t[h];if(!d||(0,i.isTypedArray)(d))return a.Not;for(var g=0;g<d.length&&g<5;g++)if(null!=(c=_(d[g])))return c}else if(e===o.mK)for(var g=0;g<t.length&&g<5;g++){var v=t[g],m=(0,r.vj)(v);if(!(0,i.isArray)(m))return a.Not;if(null!=(c=_(m[l])))return c}function _(t){var e=(0,i.isString)(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?a.Might:a.Not:e&&"-"!==t?a.Must:void 0}return a.Not}(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}},8248:(t,e,n)=>{"use strict";n.d(e,{A:()=>E});var r=n(1148),i=n(7669),o=n(4035),a=n(882),s=Math.min,u=Math.max,l=Math.sin,c=Math.cos,h=2*Math.PI,f=r.create(),p=r.create(),d=r.create();function g(t,e,n,r,i,o){i[0]=s(t,n),i[1]=s(e,r),o[0]=u(t,n),o[1]=u(e,r)}var y=[],v=[],m={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},_=[],x=[],b=[],w=[],S=[],M=[],A=Math.min,T=Math.max,k=Math.cos,C=Math.sin,I=Math.abs,D=Math.PI,O=2*D,P="undefined"!=typeof Float32Array,L=[];function R(t){return Math.round(t/D*1e8)/1e8%2*D}let E=function(){var t;function e(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return e.prototype.increaseVersion=function(){this._version++},e.prototype.getVersion=function(){return this._version},e.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=I(n/o.Y5/t)||0,this._uy=I(n/o.Y5/e)||0)},e.prototype.setDPR=function(t){this.dpr=t},e.prototype.setContext=function(t){this._ctx=t},e.prototype.getContext=function(){return this._ctx},e.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},e.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},e.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(m.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},e.prototype.lineTo=function(t,e){var n=I(t-this._xi),r=I(e-this._yi),i=n>this._ux||r>this._uy;if(this.addData(m.L,t,e),this._ctx&&i&&this._ctx.lineTo(t,e),i)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+r*r;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},e.prototype.bezierCurveTo=function(t,e,n,r,i,o){return this._drawPendingPt(),this.addData(m.C,t,e,n,r,i,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,r,i,o),this._xi=i,this._yi=o,this},e.prototype.quadraticCurveTo=function(t,e,n,r){return this._drawPendingPt(),this.addData(m.Q,t,e,n,r),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,r),this._xi=n,this._yi=r,this},e.prototype.arc=function(t,e,n,r,i,o){this._drawPendingPt(),L[0]=r,L[1]=i,(a=R(L[0]))<0&&(a+=O),s=a-L[0],u=L[1]+s,!o&&u-a>=O?u=a+O:o&&a-u>=O?u=a-O:!o&&a>u?u=a+(O-R(a-u)):o&&a<u&&(u=a-(O-R(u-a))),L[0]=a,L[1]=u,r=L[0];var a,s,u,l=(i=L[1])-r;return this.addData(m.A,t,e,n,n,r,l,0,+!o),this._ctx&&this._ctx.arc(t,e,n,r,i,o),this._xi=k(i)*n+t,this._yi=C(i)*n+e,this},e.prototype.arcTo=function(t,e,n,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,r,i),this},e.prototype.rect=function(t,e,n,r){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,r),this.addData(m.R,t,e,n,r),this},e.prototype.closePath=function(){this._drawPendingPt(),this.addData(m.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},e.prototype.fill=function(t){t&&t.fill(),this.toStatic()},e.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},e.prototype.len=function(){return this._len},e.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&P&&(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},e.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,r=this._len,i=0;i<e;i++)n+=t[i].len();P&&this.data instanceof Float32Array&&(this.data=new Float32Array(r+n));for(var i=0;i<e;i++)for(var o=t[i].data,a=0;a<o.length;a++)this.data[r++]=o[a];this._len=r},e.prototype.addData=function(t,e,n,r,i,o,a,s,u){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var c=0;c<arguments.length;c++)l[this._len++]=arguments[c]}},e.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},e.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},e.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,P&&this._len>11&&(this.data=new Float32Array(t)))}},e.prototype.getBoundingRect=function(){b[0]=b[1]=S[0]=S[1]=Number.MAX_VALUE,w[0]=w[1]=M[0]=M[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,o=0,_=0,x=0;for(t=0;t<this._len;){var A=e[t++],T=1===t;switch(T&&(n=e[t],o=e[t+1],_=n,x=o),A){case m.M:n=_=e[t++],o=x=e[t++],S[0]=_,S[1]=x,M[0]=_,M[1]=x;break;case m.L:g(n,o,e[t],e[t+1],S,M),n=e[t++],o=e[t++];break;case m.C:!function(t,e,n,r,i,o,l,c,h,f){var p=a.lX,d=a.Yb,g=p(t,n,i,l,y);h[0]=1/0,h[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<g;m++){var _=d(t,n,i,l,y[m]);h[0]=s(_,h[0]),f[0]=u(_,f[0])}g=p(e,r,o,c,v);for(var m=0;m<g;m++){var x=d(e,r,o,c,v[m]);h[1]=s(x,h[1]),f[1]=u(x,f[1])}h[0]=s(t,h[0]),f[0]=u(t,f[0]),h[0]=s(l,h[0]),f[0]=u(l,f[0]),h[1]=s(e,h[1]),f[1]=u(e,f[1]),h[1]=s(c,h[1]),f[1]=u(c,f[1])}(n,o,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],S,M),n=e[t++],o=e[t++];break;case m.Q:!function(t,e,n,r,i,o,l,c){var h=a.gC,f=a.k3,p=u(s(h(t,n,i),1),0),d=u(s(h(e,r,o),1),0),g=f(t,n,i,p),y=f(e,r,o,d);l[0]=s(t,i,g),l[1]=s(e,o,y),c[0]=u(t,i,g),c[1]=u(e,o,y)}(n,o,e[t++],e[t++],e[t],e[t+1],S,M),n=e[t++],o=e[t++];break;case m.A:var I=e[t++],D=e[t++],O=e[t++],P=e[t++],L=e[t++],R=e[t++]+L;t+=1;var E=!e[t++];T&&(_=k(L)*O+I,x=C(L)*P+D),function(t,e,n,i,o,a,s,u,g){var y=r.min,v=r.max,m=Math.abs(o-a);if(m%h<1e-4&&m>1e-4){u[0]=t-n,u[1]=e-i,g[0]=t+n,g[1]=e+i;return}if(f[0]=c(o)*n+t,f[1]=l(o)*i+e,p[0]=c(a)*n+t,p[1]=l(a)*i+e,y(u,f,p),v(g,f,p),(o%=h)<0&&(o+=h),(a%=h)<0&&(a+=h),o>a&&!s?a+=h:o<a&&s&&(o+=h),s){var _=a;a=o,o=_}for(var x=0;x<a;x+=Math.PI/2)x>o&&(d[0]=c(x)*n+t,d[1]=l(x)*i+e,y(u,d,u),v(g,d,g))}(I,D,O,P,L,R,E,S,M),n=k(R)*O+I,o=C(R)*P+D;break;case m.R:g(_=n=e[t++],x=o=e[t++],_+e[t++],x+e[t++],S,M);break;case m.Z:n=_,o=x}r.min(b,b,S),r.max(w,w,M)}return 0===t&&(b[0]=b[1]=w[0]=w[1]=0),new i.A(b[0],b[1],w[0]-b[0],w[1]-b[1])},e.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,r=this._uy,i=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,c=0,h=0,f=0;f<e;){var p=t[f++],d=1===f;d&&(i=t[f],o=t[f+1],s=i,u=o);var g=-1;switch(p){case m.M:i=s=t[f++],o=u=t[f++];break;case m.L:var y=t[f++],v=t[f++],_=y-i,x=v-o;(I(_)>n||I(x)>r||f===e-1)&&(g=Math.sqrt(_*_+x*x),i=y,o=v);break;case m.C:var b=t[f++],w=t[f++],y=t[f++],v=t[f++],S=t[f++],M=t[f++];g=(0,a.h0)(i,o,b,w,y,v,S,M,10),i=S,o=M;break;case m.Q:var b=t[f++],w=t[f++],y=t[f++],v=t[f++];g=(0,a.d8)(i,o,b,w,y,v,10),i=y,o=v;break;case m.A:var D=t[f++],P=t[f++],L=t[f++],R=t[f++],E=t[f++],N=t[f++],B=N+E;f+=1,d&&(s=k(E)*L+D,u=C(E)*R+P),g=T(L,R)*A(O,Math.abs(N)),i=k(B)*L+D,o=C(B)*R+P;break;case m.R:s=i=t[f++],u=o=t[f++],g=2*t[f++]+2*t[f++];break;case m.Z:var _=s-i,x=u-o;g=Math.sqrt(_*_+x*x),i=s,o=u}g>=0&&(l[h++]=g,c+=g)}return this._pathLen=c,c},e.prototype.rebuildPath=function(t,e){var n,r,i,o,s,u,l,c,h,f,p=this.data,d=this._ux,g=this._uy,y=this._len,v=e<1,b=0,w=0,S=0;if(!v||(this._pathSegLen||this._calculateLength(),l=this._pathSegLen,c=e*this._pathLen))n:for(var M=0;M<y;){var D=p[M++],O=1===M;switch(O&&(i=p[M],o=p[M+1],n=i,r=o),D!==m.L&&S>0&&(t.lineTo(h,f),S=0),D){case m.M:n=i=p[M++],r=o=p[M++],t.moveTo(i,o);break;case m.L:s=p[M++],u=p[M++];var P=I(s-i),L=I(u-o);if(P>d||L>g){if(v){var R=l[w++];if(b+R>c){var E=(c-b)/R;t.lineTo(i*(1-E)+s*E,o*(1-E)+u*E);break n}b+=R}t.lineTo(s,u),i=s,o=u,S=0}else{var N=P*P+L*L;N>S&&(h=s,f=u,S=N)}break;case m.C:var B=p[M++],F=p[M++],z=p[M++],V=p[M++],H=p[M++],j=p[M++];if(v){var R=l[w++];if(b+R>c){var E=(c-b)/R;(0,a.YT)(i,B,z,H,E,_),(0,a.YT)(o,F,V,j,E,x),t.bezierCurveTo(_[1],x[1],_[2],x[2],_[3],x[3]);break n}b+=R}t.bezierCurveTo(B,F,z,V,H,j),i=H,o=j;break;case m.Q:var B=p[M++],F=p[M++],z=p[M++],V=p[M++];if(v){var R=l[w++];if(b+R>c){var E=(c-b)/R;(0,a.kx)(i,B,z,E,_),(0,a.kx)(o,F,V,E,x),t.quadraticCurveTo(_[1],x[1],_[2],x[2]);break n}b+=R}t.quadraticCurveTo(B,F,z,V),i=z,o=V;break;case m.A:var U=p[M++],W=p[M++],G=p[M++],q=p[M++],X=p[M++],Y=p[M++],Z=p[M++],$=!p[M++],K=G>q?G:q,Q=I(G-q)>.001,J=X+Y,tt=!1;if(v){var R=l[w++];b+R>c&&(J=X+Y*(c-b)/R,tt=!0),b+=R}if(Q&&t.ellipse?t.ellipse(U,W,G,q,Z,X,J,$):t.arc(U,W,K,X,J,$),tt)break n;O&&(n=k(X)*G+U,r=C(X)*q+W),i=k(J)*G+U,o=C(J)*q+W;break;case m.R:n=i=p[M],r=o=p[M+1],s=p[M++],u=p[M++];var te=p[M++],tn=p[M++];if(v){var R=l[w++];if(b+R>c){var tr=c-b;t.moveTo(s,u),t.lineTo(s+A(tr,te),u),(tr-=te)>0&&t.lineTo(s+te,u+A(tr,tn)),(tr-=tn)>0&&t.lineTo(s+T(te-tr,0),u+tn),(tr-=te)>0&&t.lineTo(s,u+T(tn-tr,0));break n}b+=R}t.rect(s,u,te,tn);break;case m.Z:if(v){var R=l[w++];if(b+R>c){var E=(c-b)/R;t.lineTo(i*(1-E)+n*E,o*(1-E)+r*E);break n}b+=R}t.closePath(),i=n,o=r}}},e.prototype.clone=function(){var t=new e,n=this.data;return t.data=n.slice?n.slice():Array.prototype.slice.call(n),t._len=this._len,t},e.CMD=m,e.initDefaultProps=void((t=e.prototype)._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0),e}()},8284:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,r){this._$handlers||(this._$handlers={});var i=this._$handlers;if("function"==typeof e&&(r=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),i[t]||(i[t]=[]);for(var a=0;a<i[t].length;a++)if(i[t][a].h===n)return this;var s={h:n,query:e,ctx:r||this,callAtLast:n.zrEventfulCallAtLast},u=i[t].length-1,l=i[t][u];return l&&l.callAtLast?i[t].splice(u,0,s):i[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var r=[],i=0,o=n[t].length;i<o;i++)n[t][i].h!==e&&r.push(n[t][i]);n[t]=r}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var r=this._$handlers[t],i=this._$eventProcessor;if(r)for(var o=e.length,a=r.length,s=0;s<a;s++){var u=r[s];if(!i||!i.filter||null==u.query||i.filter(t,u.query))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e)}}return i&&i.afterTrigger&&i.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var r=this._$handlers[t],i=this._$eventProcessor;if(r)for(var o=e.length,a=e[o-1],s=r.length,u=0;u<s;u++){var l=r[u];if(!i||!i.filter||null==l.query||i.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}return i&&i.afterTrigger&&i.afterTrigger(t),this},t}()},8346:(t,e,n)=>{"use strict";function r(){return[1,0,0,1,0,0]}function i(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function o(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function a(t,e,n){var r=e[0]*n[0]+e[2]*n[1],i=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],u=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=r,t[1]=i,t[2]=o,t[3]=a,t[4]=s,t[5]=u,t}function s(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function u(t,e,n,r){void 0===r&&(r=[0,0]);var i=e[0],o=e[2],a=e[4],s=e[1],u=e[3],l=e[5],c=Math.sin(n),h=Math.cos(n);return t[0]=i*h+s*c,t[1]=-i*c+s*h,t[2]=o*h+u*c,t[3]=-o*c+h*u,t[4]=h*(a-r[0])+c*(l-r[1])+r[0],t[5]=h*(l-r[1])-c*(a-r[0])+r[1],t}function l(t,e,n){var r=n[0],i=n[1];return t[0]=e[0]*r,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*i,t[4]=e[4]*r,t[5]=e[5]*i,t}function c(t,e){var n=e[0],r=e[2],i=e[4],o=e[1],a=e[3],s=e[5],u=n*a-o*r;return u?(u=1/u,t[0]=a*u,t[1]=-o*u,t[2]=-r*u,t[3]=n*u,t[4]=(r*s-a*i)*u,t[5]=(o*i-n*s)*u,t):null}function h(t){var e=r();return o(e,t),e}n.r(e),n.d(e,{clone:()=>h,copy:()=>o,create:()=>r,identity:()=>i,invert:()=>c,mul:()=>a,rotate:()=>u,scale:()=>l,translate:()=>s})},8535:(t,e,n)=>{"use strict";n.d(e,{AF:()=>c,BE:()=>h,O0:()=>g,gV:()=>l,tP:()=>u,wZ:()=>f});var r=n(4123),i=n(7439),o=n(6187),a=n(8142),s=function(t){this.data=t.data||(t.sourceFormat===i.t1?{}:[]),this.sourceFormat=t.sourceFormat||i.vm,this.seriesLayoutBy=t.seriesLayoutBy||i.i_,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var r=e[n];null==r.type&&(0,a.PU)(this,n)===a.sc.Must&&(r.type="ordinal")}};function u(t){return t instanceof s}function l(t,e,n){n=n||f(t);var a=e.seriesLayoutBy,u=function(t,e,n,a,s){if(!t)return{dimensionsDefine:p(s),startIndex:l,dimensionsDetectedCount:u};if(e===i.Km){var u,l;"auto"===a||null==a?d(function(t){null!=t&&"-"!==t&&((0,r.isString)(t)?null==l&&(l=1):l=0)},n,t,10):l=(0,r.isNumber)(a)?a:+!!a,s||1!==l||(s=[],d(function(t,e){s[e]=null!=t?t+"":""},n,t,1/0)),u=s?s.length:n===i.oC?t.length:t[0]?t[0].length:null}else if(e===i.Wk)s||(s=function(t){for(var e,n=0;n<t.length&&!(e=t[n++]););if(e)return(0,r.keys)(e)}(t));else if(e===i.t1)s||(s=[],(0,r.each)(t,function(t,e){s.push(e)}));else if(e===i.mK){var c=(0,o.vj)(t[0]);u=(0,r.isArray)(c)&&c.length||1}else i.XO;return{startIndex:l,dimensionsDefine:p(s),dimensionsDetectedCount:u}}(t,n,a,e.sourceHeader,e.dimensions);return new s({data:t,sourceFormat:n,seriesLayoutBy:a,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectedCount:u.dimensionsDetectedCount,metaRawOption:(0,r.clone)(e)})}function c(t){return new s({data:t,sourceFormat:(0,r.isTypedArray)(t)?i.XO:i.mK})}function h(t){return new s({data:t.data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:(0,r.clone)(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})}function f(t){var e=i.vm;if((0,r.isTypedArray)(t))e=i.XO;else if((0,r.isArray)(t)){0===t.length&&(e=i.Km);for(var n=0,o=t.length;n<o;n++){var a=t[n];if(null!=a){if((0,r.isArray)(a)||(0,r.isTypedArray)(a)){e=i.Km;break}else if((0,r.isObject)(a)){e=i.Wk;break}}}}else if((0,r.isObject)(t)){for(var s in t)if((0,r.hasOwn)(t,s)&&(0,r.isArrayLike)(t[s])){e=i.t1;break}}return e}function p(t){if(t){var e=(0,r.createHashMap)();return(0,r.map)(t,function(t,n){var i={name:(t=(0,r.isObject)(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var o=e.get(i.name);return o?i.name+="-"+o.count++:e.set(i.name,{count:1}),i})}}function d(t,e,n,r){if(e===i.oC)for(var o=0;o<n.length&&o<r;o++)t(n[o]?n[o][0]:null,o);else for(var a=n[0]||[],o=0;o<a.length&&o<r;o++)t(a[o],o)}function g(t){var e=t.sourceFormat;return e===i.Wk||e===i.t1}},8544:(t,e,n)=>{"use strict";n.d(e,{A:()=>u});var r=n(643),i=n(4271),o=n(6235),a=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},s=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.U(t,e,!0)},e}(i.Ay);s.prototype.type="polygon";let u=s},8614:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=1;e.default=function(){return"".concat(n++)}},8947:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isFunction=void 0,e.isFunction=function(t){return"function"==typeof t}},8991:(t,e,n)=>{"use strict";n.d(e,{LR:()=>u,LW:()=>s,ap:()=>l,oi:()=>a});var r=n(4123),i=(0,n(6187).$r)();function o(t,e,n,i,o,a,s){var u,l=!1;(0,r.isFunction)(o)?(s=a,a=o,o=null):(0,r.isObject)(o)&&(a=o.cb,s=o.during,l=o.isFrom,u=o.removeOpt,o=o.dataIndex);var c="leave"===t;c||e.stopAnimation("leave");var h=function(t,e,n,i,o){if(e&&e.ecModel){var a,s=e.ecModel.getUpdatePayload();a=s&&s.animation}var u=e&&e.isAnimationEnabled(),l="update"===t;if(!u)return null;var c=void 0,h=void 0,f=void 0;return i?(c=(0,r.retrieve2)(i.duration,200),h=(0,r.retrieve2)(i.easing,"cubicOut"),f=0):(c=e.getShallow(l?"animationDurationUpdate":"animationDuration"),h=e.getShallow(l?"animationEasingUpdate":"animationEasing"),f=e.getShallow(l?"animationDelayUpdate":"animationDelay")),a&&(null!=a.duration&&(c=a.duration),null!=a.easing&&(h=a.easing),null!=a.delay&&(f=a.delay)),(0,r.isFunction)(f)&&(f=f(n,o)),(0,r.isFunction)(c)&&(c=c(n)),{duration:c||0,delay:f,easing:h}}(t,i,o,c?u||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,o):null);if(h&&h.duration>0){var f={duration:h.duration,delay:h.delay||0,easing:h.easing,done:a,force:!!a||!!s,setToFinal:!c,scope:t,during:s};l?e.animateFrom(n,f):e.animateTo(n,f)}else e.stopAnimation(),l||e.attr(n),s&&s(1),a&&a()}function a(t,e,n,r,i,a){o("update",t,e,n,r,i,a)}function s(t,e,n,r,i,a){o("enter",t,e,n,r,i,a)}function u(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function l(t){i(t).oldStyle=t.style}},9254:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isString=void 0,e.isString=function(t){return"string"==typeof t}},9445:(t,e,n)=>{"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.Collapse=void 0;var i=function(t){return t&&t.__esModule?t:{default:t}}(n(2115));function o(t,e){return(o=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function a(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var l=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");c.prototype=Object.create(t&&t.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),t&&o(c,t);var e,n,l=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=s(c);return t=e?Reflect.construct(n,arguments,s(this).constructor):n.apply(this,arguments),function(t,e){if(e&&("object"===r(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return a(t)}(this,t)});function c(t){var e;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return u(a(e=l.call(this,t)),"timeout",void 0),u(a(e),"container",void 0),u(a(e),"content",void 0),u(a(e),"onResize",function(){if(clearTimeout(e.timeout),e.container&&e.content){var t=e.props,n=t.isOpened,r=t.checkTimeout,i=Math.floor(e.container.clientHeight),o=Math.floor(e.content.clientHeight),a=n&&1>=Math.abs(o-i),s=!n&&1>=Math.abs(i);a||s?e.onRest({isFullyOpened:a,isFullyClosed:s,isOpened:n,containerHeight:i,contentHeight:o}):(e.onWork({isFullyOpened:a,isFullyClosed:s,isOpened:n,containerHeight:i,contentHeight:o}),e.timeout=setTimeout(function(){return e.onResize()},r))}}),u(a(e),"onRest",function(t){var n=t.isFullyOpened,r=t.isFullyClosed,i=t.isOpened,o=t.containerHeight,a=t.contentHeight;if(e.container&&e.content){var s=i&&e.container.style.height==="".concat(a,"px"),u=!i&&"0px"===e.container.style.height;if(s||u){e.container.style.overflow=i?"initial":"hidden",e.container.style.height=i?"auto":"0px";var l=e.props.onRest;l&&l({isFullyOpened:n,isFullyClosed:r,isOpened:i,containerHeight:o,contentHeight:a})}}}),u(a(e),"onWork",function(t){var n=t.isFullyOpened,r=t.isFullyClosed,i=t.isOpened,o=t.containerHeight,a=t.contentHeight;if(e.container&&e.content){var s=i&&e.container.style.height==="".concat(a,"px"),u=!i&&"0px"===e.container.style.height;if(!s&&!u){e.container.style.overflow="hidden",e.container.style.height=i?"".concat(a,"px"):"0px";var l=e.props.onWork;l&&l({isFullyOpened:n,isFullyClosed:r,isOpened:i,containerHeight:o,contentHeight:a})}}}),u(a(e),"onRefContainer",function(t){e.container=t}),u(a(e),"onRefContent",function(t){e.content=t}),t.initialStyle?e.initialStyle=t.initialStyle:e.initialStyle=t.isOpened?{height:"auto",overflow:"initial"}:{height:"0px",overflow:"hidden"},e}return n=[{key:"componentDidMount",value:function(){this.onResize()}},{key:"shouldComponentUpdate",value:function(t){var e=this.props,n=e.theme,r=e.isOpened;return e.children!==t.children||r!==t.isOpened||Object.keys(n).some(function(e){return n[e]!==t.theme[e]})}},{key:"getSnapshotBeforeUpdate",value:function(){if(!this.container||!this.content)return null;if("auto"===this.container.style.height){var t=this.content.clientHeight;this.container.style.height="".concat(t,"px")}return null}},{key:"componentDidUpdate",value:function(){this.onResize()}},{key:"componentWillUnmount",value:function(){clearTimeout(this.timeout)}},{key:"render",value:function(){var t=this.props,e=t.theme,n=t.children,r=t.isOpened;return i.default.createElement("div",{ref:this.onRefContainer,className:e.collapse,style:this.initialStyle,"aria-hidden":!r},i.default.createElement("div",{ref:this.onRefContent,className:e.content},n))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}(c.prototype,n),c}(i.default.Component);e.Collapse=l,u(l,"defaultProps",{theme:{collapse:"ReactCollapse--collapse",content:"ReactCollapse--content"},initialStyle:void 0,onRest:void 0,onWork:void 0,checkTimeout:50})},9486:(t,e,n)=>{"use strict";n.d(e,{A:()=>i});var r=n(4123);let i=function(t){this.otherDims={},null!=t&&r.extend(this,t)}},9562:(t,e,n)=>{"use strict";n.d(e,{A:()=>u});var r=n(643),i=n(4271),o=n(6235),a=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},s=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new a},e.prototype.buildPath=function(t,e){o.U(t,e,!1)},e}(i.Ay);s.prototype.type="polyline";let u=s},9566:(t,e,n)=>{"use strict";n.d(e,{Y:()=>function t(e){if((0,u.isArray)(e))return void(0,u.each)(e,function(e){t(e)});(0,u.indexOf)(h,e)>=0||(h.push(e),(0,u.isFunction)(e)&&(e={install:e}),e.install(f))}});var r=n(202),i=n(6036),o=n(4436),a=n(706),s=n(2403),u=n(4123),l=n(6033),c=n(7172),h=[],f={registerPreprocessor:r.lP,registerProcessor:r.qg,registerPostInit:r.cf,registerPostUpdate:r.tb,registerUpdateLifecycle:r.xV,registerAction:r.OH,registerCoordinateSystem:r.pX,registerLayout:r.Oh,registerVisual:r.AF,registerTransform:r.iY,registerLoading:r.Ej,registerMap:r.mz,registerImpl:l.m,PRIORITY:r.FQ,ComponentModel:a.A,ComponentView:i.A,SeriesModel:s.A,ChartView:o.A,registerComponentModel:function(t){a.A.registerClass(t)},registerComponentView:function(t){i.A.registerClass(t)},registerSeriesModel:function(t){s.A.registerClass(t)},registerChartView:function(t){o.A.registerClass(t)},registerSubTypeDefaulter:function(t,e){a.A.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){(0,c.registerPainter)(t,e)}}},9662:(t,e,n)=>{"use strict";n.d(e,{OD:()=>s,SR:()=>a,xA:()=>l});var r=n(4948),i=n(2316),o=new r.Ay(50);function a(t){if("string"!=typeof t)return t;var e=o.get(t);return e&&e.image}function s(t,e,n,r,a){if(!t)return e;if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!n)return e;var s=o.get(t),c={hostEl:n,cb:r,cbPayload:a};return s?l(e=s.image)||s.pending.push(c):((e=i.yh.loadImage(t,u,u)).__zrImageSrc=t,o.put(t,e.__cachedImgObj={image:e,pending:[c]})),e}function u(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],r=n.cb;r&&r(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function l(t){return t&&t.width&&t.height}},9799:(t,e,n)=>{"use strict";n.d(e,{$9:()=>x,$H:()=>h,CZ:()=>c,F7:()=>y,FP:()=>F,GP:()=>b,KF:()=>N,Lm:()=>d,MA:()=>l,OY:()=>s,Wf:()=>D,X_:()=>w,Yd:()=>E,Zz:()=>O,bP:()=>k,ce:()=>_,eV:()=>v,g0:()=>M,hY:()=>A,iC:()=>C,iW:()=>u,jJ:()=>T,tM:()=>P,ti:()=>R,ww:()=>B,xu:()=>L,yB:()=>I,ym:()=>m});var r=n(4123),i=n(3607),o=n(2045),a=n(1613),s=1e3,u=6e4,l=36e5,c=864e5,h=31536e6,f={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},p="{yyyy}-{MM}-{dd}",d={year:"{yyyy}",month:"{yyyy}-{MM}",day:p,hour:p+" "+f.hour,minute:p+" "+f.minute,second:p+" "+f.second,millisecond:f.none},g=["year","month","day","hour","minute","second","millisecond"],y=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function v(t,e){return t+="","0000".substr(0,e-t.length)+t}function m(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function _(t){return t===m(t)}function x(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function b(t,e,n,r){var s=i._U(t),u=s[A(n)](),l=s[T(n)]()+1,c=Math.floor((l-1)/3)+1,h=s[k(n)](),f=s["get"+(n?"UTC":"")+"Day"](),p=s[C(n)](),d=(p-1)%12+1,g=s[I(n)](),y=s[D(n)](),m=s[O(n)](),_=p>=12?"pm":"am",x=_.toUpperCase(),b=(r instanceof a.A?r:(0,o.A4)(r||o.Lv)||(0,o.pr)()).getModel("time"),w=b.get("month"),S=b.get("monthAbbr"),M=b.get("dayOfWeek"),P=b.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,_+"").replace(/{A}/g,x+"").replace(/{yyyy}/g,u+"").replace(/{yy}/g,v(u%100+"",2)).replace(/{Q}/g,c+"").replace(/{MMMM}/g,w[l-1]).replace(/{MMM}/g,S[l-1]).replace(/{MM}/g,v(l,2)).replace(/{M}/g,l+"").replace(/{dd}/g,v(h,2)).replace(/{d}/g,h+"").replace(/{eeee}/g,M[f]).replace(/{ee}/g,P[f]).replace(/{e}/g,f+"").replace(/{HH}/g,v(p,2)).replace(/{H}/g,p+"").replace(/{hh}/g,v(d+"",2)).replace(/{h}/g,d+"").replace(/{mm}/g,v(g,2)).replace(/{m}/g,g+"").replace(/{ss}/g,v(y,2)).replace(/{s}/g,y+"").replace(/{SSS}/g,v(m,3)).replace(/{S}/g,m+"")}function w(t,e,n,i,o){var a=null;if(r.isString(n))a=n;else if(r.isFunction(n))a=n(t.value,e,{level:t.level});else{var s=r.extend({},f);if(t.level>0)for(var u=0;u<g.length;++u)s[g[u]]="{primary|"+s[g[u]]+"}";var l=n?!1===n.inherit?n:r.defaults(n,s):s,c=S(t.value,o);if(l[c])a=l[c];else if(l.inherit){for(var h=y.indexOf(c),u=h-1;u>=0;--u)if(l[c]){a=l[c];break}a=a||s.none}if(r.isArray(a)){var p=null==t.level?0:t.level>=0?t.level:a.length+t.level;p=Math.min(p,a.length-1),a=a[p]}}return b(new Date(t.value),a,o,i)}function S(t,e){var n=i._U(t),r=n[T(e)]()+1,o=n[k(e)](),a=n[C(e)](),s=n[I(e)](),u=n[D(e)](),l=0===n[O(e)](),c=l&&0===u,h=c&&0===s,f=h&&0===a,p=f&&1===o;if(p&&1===r)return"year";if(p)return"month";if(f)return"day";if(h)return"hour";if(c)return"minute";else if(l)return"second";else return"millisecond"}function M(t,e,n){var o=r.isNumber(t)?i._U(t):t;switch(e=e||S(t,n)){case"year":return o[A(n)]();case"half-year":return+(o[T(n)]()>=6);case"quarter":return Math.floor((o[T(n)]()+1)/4);case"month":return o[T(n)]();case"day":return o[k(n)]();case"half-day":return o[C(n)]()/24;case"hour":return o[C(n)]();case"minute":return o[I(n)]();case"second":return o[D(n)]();case"millisecond":return o[O(n)]()}}function A(t){return t?"getUTCFullYear":"getFullYear"}function T(t){return t?"getUTCMonth":"getMonth"}function k(t){return t?"getUTCDate":"getDate"}function C(t){return t?"getUTCHours":"getHours"}function I(t){return t?"getUTCMinutes":"getMinutes"}function D(t){return t?"getUTCSeconds":"getSeconds"}function O(t){return t?"getUTCMilliseconds":"getMilliseconds"}function P(t){return t?"setUTCFullYear":"setFullYear"}function L(t){return t?"setUTCMonth":"setMonth"}function R(t){return t?"setUTCDate":"setDate"}function E(t){return t?"setUTCHours":"setHours"}function N(t){return t?"setUTCMinutes":"setMinutes"}function B(t){return t?"setUTCSeconds":"setSeconds"}function F(t){return t?"setUTCMilliseconds":"setMilliseconds"}},9942:(t,e,n)=>{"use strict";n.d(e,{U:()=>i});var r=n(4123);function i(t){return new o(t)}var o=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n,i,o,a=this._upstream,s=t&&t.skip;if(this._dirty&&a){var u=this.context;u.data=u.outputData=a.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!s&&(i=this._plan(this.context));var l=((e=this._modBy)>=1||(e=1),e),c=this._modDataCount||0,h=((n=t&&t.modBy)>=1||(n=1),n),f=t&&t.modDataCount||0;(l!==h||c!==f)&&(i="reset"),(this._dirty||"reset"===i)&&(this._dirty=!1,o=this._doReset(s)),this._modBy=h,this._modDataCount=f;var p=t&&t.step;if(a?this._dueEnd=a._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,g=Math.min(null!=p?this._dueIndex+p:1/0,this._dueEnd);if(!s&&(o||d<g)){var y=this._progress;if((0,r.isArray)(y))for(var v=0;v<y.length;v++)this._doProgress(y[v],d,g,h,f);else this._doProgress(y,d,g,h,f)}this._dueIndex=g;var m=null!=this._settedOutputEnd?this._settedOutputEnd:g;this._outputDueEnd=m}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,r,i){a.reset(e,n,r,i),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:a.next},this.context)},t.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),(0,r.isArray)(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var e,n,i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),a=function(){var t,e,n,r,i,o={reset:function(u,l,c,h){e=u,t=l,n=c,i=Math.ceil((r=h)/n),o.next=n>1&&r>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%i*n+Math.ceil(e/i),a=e>=t?null:o<r?o:e;return e++,a}}()},9950:(t,e,n)=>{"use strict";n.d(e,{A:()=>s});var r=n(643),i=n(4271),o=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},a=function(t){function e(e){return t.call(this,e)||this}return(0,r.__extends)(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new o},e.prototype.buildPath=function(t,e){var n=e.cx,r=e.cy,i=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,u=Math.cos(o),l=Math.sin(o);t.moveTo(u*i+n,l*i+r),t.arc(n,r,i,o,a,!s)},e}(i.Ay);a.prototype.type="arc";let s=a}}]);