(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[167],{1496:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(2115);let i=r.forwardRef(function({title:e,titleId:t,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{fillRule:"evenodd",d:"M4.22 11.78a.75.75 0 0 1 0-1.06L9.44 5.5H5.75a.75.75 0 0 1 0-1.5h5.5a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-1.5 0V6.56l-5.22 5.22a.75.75 0 0 1-1.06 0Z",clipRule:"evenodd"}))})},3774:e=>{"use strict";let{entries:t,setPrototypeOf:n,isFrozen:r,getPrototypeOf:i,getOwnPropertyDescriptor:l}=Object,{freeze:s,seal:a,create:o}=Object,{apply:c,construct:u}="undefined"!=typeof Reflect&&Reflect;s||(s=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),u||(u=function(e,t){return new e(...t)});let p=_(Array.prototype.forEach),h=_(Array.prototype.lastIndexOf),d=_(Array.prototype.pop),f=_(Array.prototype.push),g=_(Array.prototype.splice),m=_(String.prototype.toLowerCase),k=_(String.prototype.toString),b=_(String.prototype.match),x=_(String.prototype.replace),w=_(String.prototype.indexOf),y=_(String.prototype.trim),T=_(Object.prototype.hasOwnProperty),S=_(RegExp.prototype.test),A=(Q=TypeError,function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return u(Q,t)});function _(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return c(e,t,r)}}function R(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:m;n&&n(e,null);let l=t.length;for(;l--;){let n=t[l];if("string"==typeof n){let e=i(n);e!==n&&(r(t)||(t[l]=e),n=e)}e[n]=!0}return e}function E(e){let n=o(null);for(let[r,i]of t(e))T(e,r)&&(Array.isArray(i)?n[r]=function(e){for(let t=0;t<e.length;t++)T(e,t)||(e[t]=null);return e}(i):i&&"object"==typeof i&&i.constructor===Object?n[r]=E(i):n[r]=i);return n}function v(e,t){for(;null!==e;){let n=l(e,t);if(n){if(n.get)return _(n.get);if("function"==typeof n.value)return _(n.value)}e=i(e)}return function(){return null}}let N=s(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),L=s(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),C=s(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),I=s(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),z=s(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),$=s(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),O=s(["#text"]),D=s(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),P=s(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),M=s(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),B=s(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),U=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),H=a(/<%[\w\W]*|[\w\W]*%>/gm),q=a(/\$\{[\w\W]*/gm),F=a(/^data-[\-\w.\u00B7-\uFFFF]+$/),G=a(/^aria-[\-\w]+$/),j=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Z=a(/^(?:\w+script|data):/i),W=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Y=a(/^html$/i);var Q,V=Object.freeze({__proto__:null,ARIA_ATTR:G,ATTR_WHITESPACE:W,CUSTOM_ELEMENT:a(/^[a-z][.\w]*(-[.\w]+)+$/i),DATA_ATTR:F,DOCTYPE_NAME:Y,ERB_EXPR:H,IS_ALLOWED_URI:j,IS_SCRIPT_OR_DATA:Z,MUSTACHE_EXPR:U,TMPLIT_EXPR:q});let X={element:1,text:3,progressingInstruction:7,comment:8,document:9},K=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null,r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));let i="dompurify"+(n?"#"+n:"");try{return e.createPolicy(i,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+i+" could not be created."),null}},J=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};e.exports=function e(){let n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window,i=t=>e(t);if(i.version="3.2.4",i.removed=[],!r||!r.document||r.document.nodeType!==X.document||!r.Element)return i.isSupported=!1,i;let{document:l}=r,a=l,c=a.currentScript,{DocumentFragment:u,HTMLTemplateElement:_,Node:U,Element:H,NodeFilter:q,NamedNodeMap:F=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Z,trustedTypes:W}=r,Q=H.prototype,ee=v(Q,"cloneNode"),et=v(Q,"remove"),en=v(Q,"nextSibling"),er=v(Q,"childNodes"),ei=v(Q,"parentNode");if("function"==typeof _){let e=l.createElement("template");e.content&&e.content.ownerDocument&&(l=e.content.ownerDocument)}let el="",{implementation:es,createNodeIterator:ea,createDocumentFragment:eo,getElementsByTagName:ec}=l,{importNode:eu}=a,ep=J();i.isSupported="function"==typeof t&&"function"==typeof ei&&es&&void 0!==es.createHTMLDocument;let{MUSTACHE_EXPR:eh,ERB_EXPR:ed,TMPLIT_EXPR:ef,DATA_ATTR:eg,ARIA_ATTR:em,IS_SCRIPT_OR_DATA:ek,ATTR_WHITESPACE:eb,CUSTOM_ELEMENT:ex}=V,{IS_ALLOWED_URI:ew}=V,ey=null,eT=R({},[...N,...L,...C,...z,...O]),eS=null,eA=R({},[...D,...P,...M,...B]),e_=Object.seal(o(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),eR=null,eE=null,ev=!0,eN=!0,eL=!1,eC=!0,eI=!1,ez=!0,e$=!1,eO=!1,eD=!1,eP=!1,eM=!1,eB=!1,eU=!0,eH=!1,eq=!0,eF=!1,eG={},ej=null,eZ=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),eW=null,eY=R({},["audio","video","img","source","image","track"]),eQ=null,eV=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),eX="http://www.w3.org/1998/Math/MathML",eK="http://www.w3.org/2000/svg",eJ="http://www.w3.org/1999/xhtml",e0=eJ,e1=!1,e3=null,e2=R({},[eX,eK,eJ],k),e5=R({},["mi","mo","mn","ms","mtext"]),e6=R({},["annotation-xml"]),e9=R({},["title","style","font","a","script"]),e4=null,e7=["application/xhtml+xml","text/html"],e8=null,te=null,tt=l.createElement("form"),tn=function(e){return e instanceof RegExp||e instanceof Function},tr=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!te||te!==e){if(e&&"object"==typeof e||(e={}),e=E(e),e8="application/xhtml+xml"===(e4=-1===e7.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE)?k:m,ey=T(e,"ALLOWED_TAGS")?R({},e.ALLOWED_TAGS,e8):eT,eS=T(e,"ALLOWED_ATTR")?R({},e.ALLOWED_ATTR,e8):eA,e3=T(e,"ALLOWED_NAMESPACES")?R({},e.ALLOWED_NAMESPACES,k):e2,eQ=T(e,"ADD_URI_SAFE_ATTR")?R(E(eV),e.ADD_URI_SAFE_ATTR,e8):eV,eW=T(e,"ADD_DATA_URI_TAGS")?R(E(eY),e.ADD_DATA_URI_TAGS,e8):eY,ej=T(e,"FORBID_CONTENTS")?R({},e.FORBID_CONTENTS,e8):eZ,eR=T(e,"FORBID_TAGS")?R({},e.FORBID_TAGS,e8):{},eE=T(e,"FORBID_ATTR")?R({},e.FORBID_ATTR,e8):{},eG=!!T(e,"USE_PROFILES")&&e.USE_PROFILES,ev=!1!==e.ALLOW_ARIA_ATTR,eN=!1!==e.ALLOW_DATA_ATTR,eL=e.ALLOW_UNKNOWN_PROTOCOLS||!1,eC=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,eI=e.SAFE_FOR_TEMPLATES||!1,ez=!1!==e.SAFE_FOR_XML,e$=e.WHOLE_DOCUMENT||!1,eP=e.RETURN_DOM||!1,eM=e.RETURN_DOM_FRAGMENT||!1,eB=e.RETURN_TRUSTED_TYPE||!1,eD=e.FORCE_BODY||!1,eU=!1!==e.SANITIZE_DOM,eH=e.SANITIZE_NAMED_PROPS||!1,eq=!1!==e.KEEP_CONTENT,eF=e.IN_PLACE||!1,ew=e.ALLOWED_URI_REGEXP||j,e0=e.NAMESPACE||eJ,e5=e.MATHML_TEXT_INTEGRATION_POINTS||e5,e6=e.HTML_INTEGRATION_POINTS||e6,e_=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&tn(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(e_.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&tn(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(e_.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(e_.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),eI&&(eN=!1),eM&&(eP=!0),eG&&(ey=R({},O),eS=[],!0===eG.html&&(R(ey,N),R(eS,D)),!0===eG.svg&&(R(ey,L),R(eS,P),R(eS,B)),!0===eG.svgFilters&&(R(ey,C),R(eS,P),R(eS,B)),!0===eG.mathMl&&(R(ey,z),R(eS,M),R(eS,B))),e.ADD_TAGS&&(ey===eT&&(ey=E(ey)),R(ey,e.ADD_TAGS,e8)),e.ADD_ATTR&&(eS===eA&&(eS=E(eS)),R(eS,e.ADD_ATTR,e8)),e.ADD_URI_SAFE_ATTR&&R(eQ,e.ADD_URI_SAFE_ATTR,e8),e.FORBID_CONTENTS&&(ej===eZ&&(ej=E(ej)),R(ej,e.FORBID_CONTENTS,e8)),eq&&(ey["#text"]=!0),e$&&R(ey,["html","head","body"]),ey.table&&(R(ey,["tbody"]),delete eR.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw A('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw A('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');el=(n=e.TRUSTED_TYPES_POLICY).createHTML("")}else void 0===n&&(n=K(W,c)),null!==n&&"string"==typeof el&&(el=n.createHTML(""));s&&s(e),te=e}},ti=R({},[...L,...C,...I]),tl=R({},[...z,...$]),ts=function(e){let t=ei(e);t&&t.tagName||(t={namespaceURI:e0,tagName:"template"});let n=m(e.tagName),r=m(t.tagName);return!!e3[e.namespaceURI]&&(e.namespaceURI===eK?t.namespaceURI===eJ?"svg"===n:t.namespaceURI===eX?"svg"===n&&("annotation-xml"===r||e5[r]):!!ti[n]:e.namespaceURI===eX?t.namespaceURI===eJ?"math"===n:t.namespaceURI===eK?"math"===n&&e6[r]:!!tl[n]:e.namespaceURI===eJ?(t.namespaceURI!==eK||!!e6[r])&&(t.namespaceURI!==eX||!!e5[r])&&!tl[n]&&(e9[n]||!ti[n]):"application/xhtml+xml"===e4&&!!e3[e.namespaceURI])},ta=function(e){f(i.removed,{element:e});try{ei(e).removeChild(e)}catch(t){et(e)}},to=function(e,t){try{f(i.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(i.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(eP||eM)try{ta(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},tc=function(e){let t=null,r=null;if(eD)e="<remove></remove>"+e;else{let t=b(e,/^[\r\n\t ]+/);r=t&&t[0]}"application/xhtml+xml"===e4&&e0===eJ&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");let i=n?n.createHTML(e):e;if(e0===eJ)try{t=new Z().parseFromString(i,e4)}catch(e){}if(!t||!t.documentElement){t=es.createDocument(e0,"template",null);try{t.documentElement.innerHTML=e1?el:i}catch(e){}}let s=t.body||t.documentElement;return(e&&r&&s.insertBefore(l.createTextNode(r),s.childNodes[0]||null),e0===eJ)?ec.call(t,e$?"html":"body")[0]:e$?t.documentElement:s},tu=function(e){return ea.call(e.ownerDocument||e,e,q.SHOW_ELEMENT|q.SHOW_COMMENT|q.SHOW_TEXT|q.SHOW_PROCESSING_INSTRUCTION|q.SHOW_CDATA_SECTION,null)},tp=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof F)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},th=function(e){return"function"==typeof U&&e instanceof U};function td(e,t,n){p(e,e=>{e.call(i,t,n,te)})}let tf=function(e){let t=null;if(td(ep.beforeSanitizeElements,e,null),tp(e))return ta(e),!0;let n=e8(e.nodeName);if(td(ep.uponSanitizeElement,e,{tagName:n,allowedTags:ey}),e.hasChildNodes()&&!th(e.firstElementChild)&&S(/<[/\w]/g,e.innerHTML)&&S(/<[/\w]/g,e.textContent)||e.nodeType===X.progressingInstruction||ez&&e.nodeType===X.comment&&S(/<[/\w]/g,e.data))return ta(e),!0;if(!ey[n]||eR[n]){if(!eR[n]&&tm(n)&&(e_.tagNameCheck instanceof RegExp&&S(e_.tagNameCheck,n)||e_.tagNameCheck instanceof Function&&e_.tagNameCheck(n)))return!1;if(eq&&!ej[n]){let t=ei(e)||e.parentNode,n=er(e)||e.childNodes;if(n&&t){let r=n.length;for(let i=r-1;i>=0;--i){let r=ee(n[i],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,en(e))}}}return ta(e),!0}return e instanceof H&&!ts(e)||("noscript"===n||"noembed"===n||"noframes"===n)&&S(/<\/no(script|embed|frames)/i,e.innerHTML)?(ta(e),!0):(eI&&e.nodeType===X.text&&(t=e.textContent,p([eh,ed,ef],e=>{t=x(t,e," ")}),e.textContent!==t&&(f(i.removed,{element:e.cloneNode()}),e.textContent=t)),td(ep.afterSanitizeElements,e,null),!1)},tg=function(e,t,n){if(eU&&("id"===t||"name"===t)&&(n in l||n in tt))return!1;if(eN&&!eE[t]&&S(eg,t));else if(ev&&S(em,t));else if(!eS[t]||eE[t]){if(!(tm(e)&&(e_.tagNameCheck instanceof RegExp&&S(e_.tagNameCheck,e)||e_.tagNameCheck instanceof Function&&e_.tagNameCheck(e))&&(e_.attributeNameCheck instanceof RegExp&&S(e_.attributeNameCheck,t)||e_.attributeNameCheck instanceof Function&&e_.attributeNameCheck(t))||"is"===t&&e_.allowCustomizedBuiltInElements&&(e_.tagNameCheck instanceof RegExp&&S(e_.tagNameCheck,n)||e_.tagNameCheck instanceof Function&&e_.tagNameCheck(n))))return!1}else if(eQ[t]);else if(S(ew,x(n,eb,"")));else if(("src"===t||"xlink:href"===t||"href"===t)&&"script"!==e&&0===w(n,"data:")&&eW[e]);else if(eL&&!S(ek,x(n,eb,"")));else if(n)return!1;return!0},tm=function(e){return"annotation-xml"!==e&&b(e,ex)},tk=function(e){td(ep.beforeSanitizeAttributes,e,null);let{attributes:t}=e;if(!t||tp(e))return;let r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:eS,forceKeepAttr:void 0},l=t.length;for(;l--;){let{name:s,namespaceURI:a,value:o}=t[l],c=e8(s),u="value"===s?o:y(o);if(r.attrName=c,r.attrValue=u,r.keepAttr=!0,r.forceKeepAttr=void 0,td(ep.uponSanitizeAttribute,e,r),u=r.attrValue,eH&&("id"===c||"name"===c)&&(to(s,e),u="user-content-"+u),ez&&S(/((--!?|])>)|<\/(style|title)/i,u)){to(s,e);continue}if(r.forceKeepAttr||(to(s,e),!r.keepAttr))continue;if(!eC&&S(/\/>/i,u)){to(s,e);continue}eI&&p([eh,ed,ef],e=>{u=x(u,e," ")});let h=e8(e.nodeName);if(tg(h,c,u)){if(n&&"object"==typeof W&&"function"==typeof W.getAttributeType)if(a);else switch(W.getAttributeType(h,c)){case"TrustedHTML":u=n.createHTML(u);break;case"TrustedScriptURL":u=n.createScriptURL(u)}try{a?e.setAttributeNS(a,s,u):e.setAttribute(s,u),tp(e)?ta(e):d(i.removed)}catch(e){}}}td(ep.afterSanitizeAttributes,e,null)},tb=function e(t){let n=null,r=tu(t);for(td(ep.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)td(ep.uponSanitizeShadowNode,n,null),tf(n),tk(n),n.content instanceof u&&e(n.content);td(ep.afterSanitizeShadowDOM,t,null)};return i.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,l=null,s=null,o=null;if((e1=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!th(e))if("function"==typeof e.toString){if("string"!=typeof(e=e.toString()))throw A("dirty is not a string, aborting")}else throw A("toString is not a function");if(!i.isSupported)return e;if(eO||tr(t),i.removed=[],"string"==typeof e&&(eF=!1),eF){if(e.nodeName){let t=e8(e.nodeName);if(!ey[t]||eR[t])throw A("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof U)(l=(r=tc("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType===X.element&&"BODY"===l.nodeName||"HTML"===l.nodeName?r=l:r.appendChild(l);else{if(!eP&&!eI&&!e$&&-1===e.indexOf("<"))return n&&eB?n.createHTML(e):e;if(!(r=tc(e)))return eP?null:eB?el:""}r&&eD&&ta(r.firstChild);let c=tu(eF?e:r);for(;s=c.nextNode();)tf(s),tk(s),s.content instanceof u&&tb(s.content);if(eF)return e;if(eP){if(eM)for(o=eo.call(r.ownerDocument);r.firstChild;)o.appendChild(r.firstChild);else o=r;return(eS.shadowroot||eS.shadowrootmode)&&(o=eu.call(a,o,!0)),o}let h=e$?r.outerHTML:r.innerHTML;return e$&&ey["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&S(Y,r.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+h),eI&&p([eh,ed,ef],e=>{h=x(h,e," ")}),n&&eB?n.createHTML(h):h},i.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};tr(e),eO=!0},i.clearConfig=function(){te=null,eO=!1},i.isValidAttribute=function(e,t,n){return te||tr({}),tg(e8(e),e8(t),n)},i.addHook=function(e,t){"function"==typeof t&&f(ep[e],t)},i.removeHook=function(e,t){if(void 0!==t){let n=h(ep[e],t);return -1===n?void 0:g(ep[e],n,1)[0]}return d(ep[e])},i.removeHooks=function(e){ep[e]=[]},i.removeAllHooks=function(){ep=J()},i}()},4558:(e,t,n)=>{"use strict";function r(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}n.d(t,{xI:()=>ef});let i=r(),l={exec:()=>null};function s(e,t=""){let n="string"==typeof e?e:e.source,r={replace:(e,t)=>{let i="string"==typeof t?t:t.source;return i=i.replace(a.caret,"$1"),n=n.replace(e,i),r},getRegex:()=>new RegExp(n,t)};return r}let a={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},o=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,c=/(?:[*+-]|\d{1,9}[.)])/,u=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,p=s(u).replace(/bull/g,c).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),h=s(u).replace(/bull/g,c).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),d=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,f=/(?!\s*\])(?:\\.|[^\[\]\\])+/,g=s(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",f).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),m=s(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,c).getRegex(),k="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",b=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,x=s("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",b).replace("tag",k).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),w=s(d).replace("hr",o).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex(),y={blockquote:s(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",w).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:g,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:o,html:x,lheading:p,list:m,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:w,table:l,text:/^[^\n]+/},T=s("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",o).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex(),S={...y,lheading:h,table:T,paragraph:s(d).replace("hr",o).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",T).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex()},A={...y,html:s("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",b).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:l,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:s(d).replace("hr",o).replace("heading"," *#{1,6} *[^\n]").replace("lheading",p).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},_=/^( {2,}|\\)\n(?!\s*$)/,R=/[\p{P}\p{S}]/u,E=/[\s\p{P}\p{S}]/u,v=/[^\s\p{P}\p{S}]/u,N=s(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,E).getRegex(),L=/(?!~)[\p{P}\p{S}]/u,C=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,I=s(C,"u").replace(/punct/g,R).getRegex(),z=s(C,"u").replace(/punct/g,L).getRegex(),$="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",O=s($,"gu").replace(/notPunctSpace/g,v).replace(/punctSpace/g,E).replace(/punct/g,R).getRegex(),D=s($,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,L).getRegex(),P=s("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,v).replace(/punctSpace/g,E).replace(/punct/g,R).getRegex(),M=s(/\\(punct)/,"gu").replace(/punct/g,R).getRegex(),B=s(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),U=s(b).replace("(?:--\x3e|$)","--\x3e").getRegex(),H=s("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",U).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),q=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,F=s(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",q).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),G=s(/^!?\[(label)\]\[(ref)\]/).replace("label",q).replace("ref",f).getRegex(),j=s(/^!?\[(ref)\](?:\[\])?/).replace("ref",f).getRegex(),Z=s("reflink|nolink(?!\\()","g").replace("reflink",G).replace("nolink",j).getRegex(),W={_backpedal:l,anyPunctuation:M,autolink:B,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:_,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:l,emStrongLDelim:I,emStrongRDelimAst:O,emStrongRDelimUnd:P,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:F,nolink:j,punctuation:N,reflink:G,reflinkSearch:Z,tag:H,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:l},Y={...W,link:s(/^!?\[(label)\]\((.*?)\)/).replace("label",q).getRegex(),reflink:s(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",q).getRegex()},Q={...W,emStrongRDelimAst:D,emStrongLDelim:z,url:s(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},V={...Q,br:s(_).replace("{2,}","*").getRegex(),text:s(Q.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},X={normal:y,gfm:S,pedantic:A},K={normal:W,gfm:Q,breaks:V,pedantic:Y},J={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ee=e=>J[e];function et(e,t){if(t){if(a.escapeTest.test(e))return e.replace(a.escapeReplace,ee)}else if(a.escapeTestNoEncode.test(e))return e.replace(a.escapeReplaceNoEncode,ee);return e}function en(e){try{e=encodeURI(e).replace(a.percentDecode,"%")}catch{return null}return e}function er(e,t){let n=e.replace(a.findPipe,(e,t,n)=>{let r=!1,i=t;for(;--i>=0&&"\\"===n[i];)r=!r;return r?"|":" |"}).split(a.splitPipe),r=0;if(n[0].trim()||n.shift(),n.length>0&&!n.at(-1)?.trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(a.slashPipe,"|");return n}function ei(e,t,n){let r=e.length;if(0===r)return"";let i=0;for(;i<r;)if(e.charAt(r-i-1)===t)i++;else break;return e.slice(0,r-i)}function el(e,t,n,r,i){let l=t.href,s=t.title||null,a=e[1].replace(i.other.outputLinkReplace,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;let e={type:"link",raw:n,href:l,title:s,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,e}return{type:"image",raw:n,href:l,title:s,text:a}}class es{options;rules;lexer;constructor(e){this.options=e||i}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:ei(e,"\n")}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let e=t[0],n=function(e,t,n){let r=e.match(n.other.indentCodeCompensation);if(null===r)return t;let i=r[1];return t.split("\n").map(e=>{let t=e.match(n.other.beginningSpace);if(null===t)return e;let[r]=t;return r.length>=i.length?e.slice(i.length):e}).join("\n")}(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){let t=ei(e,"#");this.options.pedantic?e=t.trim():(!t||this.rules.other.endingSpaceChar.test(t))&&(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:ei(t[0],"\n")}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let e=ei(t[0],"\n").split("\n"),n="",r="",i=[];for(;e.length>0;){let t,l=!1,s=[];for(t=0;t<e.length;t++)if(this.rules.other.blockquoteStart.test(e[t]))s.push(e[t]),l=!0;else if(l)break;else s.push(e[t]);e=e.slice(t);let a=s.join("\n"),o=a.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${a}`:a,r=r?`${r}
${o}`:o;let c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(o,i,!0),this.lexer.state.top=c,0===e.length)break;let u=i.at(-1);if(u?.type==="code")break;if(u?.type==="blockquote"){let t=u.raw+"\n"+e.join("\n"),l=this.blockquote(t);i[i.length-1]=l,n=n.substring(0,n.length-u.raw.length)+l.raw,r=r.substring(0,r.length-u.text.length)+l.text;break}if(u?.type==="list"){let t=u.raw+"\n"+e.join("\n"),l=this.list(t);i[i.length-1]=l,n=n.substring(0,n.length-u.raw.length)+l.raw,r=r.substring(0,r.length-u.raw.length)+l.raw,e=t.substring(i.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:n,tokens:i,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,i={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let l=this.rules.other.listItemRegex(n),s=!1;for(;e;){let n,r=!1,a="",o="";if(!(t=l.exec(e))||this.rules.block.hr.test(e))break;a=t[0],e=e.substring(a.length);let c=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,e=>" ".repeat(3*e.length)),u=e.split("\n",1)[0],p=!c.trim(),h=0;if(this.options.pedantic?(h=2,o=c.trimStart()):p?h=t[1].length+1:(h=(h=t[2].search(this.rules.other.nonSpaceChar))>4?1:h,o=c.slice(h),h+=t[1].length),p&&this.rules.other.blankLine.test(u)&&(a+=u+"\n",e=e.substring(u.length+1),r=!0),!r){let t=this.rules.other.nextBulletRegex(h),n=this.rules.other.hrRegex(h),r=this.rules.other.fencesBeginRegex(h),i=this.rules.other.headingBeginRegex(h),l=this.rules.other.htmlBeginRegex(h);for(;e;){let s,d=e.split("\n",1)[0];if(u=d,s=this.options.pedantic?u=u.replace(this.rules.other.listReplaceNesting,"  "):u.replace(this.rules.other.tabCharGlobal,"    "),r.test(u)||i.test(u)||l.test(u)||t.test(u)||n.test(u))break;if(s.search(this.rules.other.nonSpaceChar)>=h||!u.trim())o+="\n"+s.slice(h);else{if(p||c.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||r.test(c)||i.test(c)||n.test(c))break;o+="\n"+u}p||u.trim()||(p=!0),a+=d+"\n",e=e.substring(d.length+1),c=s.slice(h)}}!i.loose&&(s?i.loose=!0:this.rules.other.doubleBlankLine.test(a)&&(s=!0));let d=null;this.options.gfm&&(d=this.rules.other.listIsTask.exec(o))&&(n="[ ] "!==d[0],o=o.replace(this.rules.other.listReplaceTask,"")),i.items.push({type:"list_item",raw:a,task:!!d,checked:n,loose:!1,text:o,tokens:[]}),i.raw+=a}let a=i.items.at(-1);if(!a)return;a.raw=a.raw.trimEnd(),a.text=a.text.trimEnd(),i.raw=i.raw.trimEnd();for(let e=0;e<i.items.length;e++)if(this.lexer.state.top=!1,i.items[e].tokens=this.lexer.blockTokens(i.items[e].text,[]),!i.loose){let t=i.items[e].tokens.filter(e=>"space"===e.type);i.loose=t.length>0&&t.some(e=>this.rules.other.anyLine.test(e.raw))}if(i.loose)for(let e=0;e<i.items.length;e++)i.items[e].loose=!0;return i}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=er(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],l={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let e of r)this.rules.other.tableAlignRight.test(e)?l.align.push("right"):this.rules.other.tableAlignCenter.test(e)?l.align.push("center"):this.rules.other.tableAlignLeft.test(e)?l.align.push("left"):l.align.push(null);for(let e=0;e<n.length;e++)l.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:l.align[e]});for(let e of i)l.rows.push(er(e,l.header.length).map((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:l.align[t]})));return l}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e))return;let t=ei(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{let e=function(e,t){if(-1===e.indexOf(")"))return -1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if("("===e[r])n++;else if(e[r]===t[1]&&--n<0)return r;return -1}(t[2],"()");if(e>-1){let n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){let e=this.rules.other.pedanticHrefTitle.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(n=this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)?n.slice(1):n.slice(1,-1)),el(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=t[(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!e){let e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return el(n,e,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){let n=[...r[0]].length-1,i,l,s=n,a=0,o="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(o.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=o.exec(t));){if(!(i=r[1]||r[2]||r[3]||r[4]||r[5]||r[6]))continue;if(l=[...i].length,r[3]||r[4]){s+=l;continue}if((r[5]||r[6])&&n%3&&!((n+l)%3)){a+=l;continue}if((s-=l)>0)continue;l=Math.min(l,l+s+a);let t=[...r[0]][0].length,o=e.slice(0,n+r.index+t+l);if(Math.min(n,l)%2){let e=o.slice(1,-1);return{type:"em",raw:o,text:e,tokens:this.lexer.inlineTokens(e)}}let c=o.slice(2,-2);return{type:"strong",raw:o,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," "),n=this.rules.other.nonSpaceChar.test(e),r=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);return n&&r&&(e=e.substring(1,e.length-1)),{type:"codespan",raw:t[0],text:e}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let e,n;return n="@"===t[2]?"mailto:"+(e=t[1]):e=t[1],{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])n="mailto:"+(e=t[0]);else{let r;do r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(r!==t[0]);e=t[0],n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}}class ea{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||i,this.options.tokenizer=this.options.tokenizer||new es,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:a,block:X.normal,inline:K.normal};this.options.pedantic?(t.block=X.pedantic,t.inline=K.pedantic):this.options.gfm&&(t.block=X.gfm,this.options.breaks?t.inline=K.breaks:t.inline=K.gfm),this.tokenizer.rules=t}static get rules(){return{block:X,inline:K}}static lex(e,t){return new ea(t).lex(e)}static lexInline(e,t){return new ea(t).inlineTokens(e)}lex(e){e=e.replace(a.carriageReturn,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){let t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(a.tabCharGlobal,"    ").replace(a.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let n=t.at(-1);1===r.raw.length&&void 0!==n?n.raw+="\n":t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let n=t.at(-1);n?.type==="paragraph"||n?.type==="text"?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if((r=this.tokenizer.fences(e))||(r=this.tokenizer.heading(e))||(r=this.tokenizer.hr(e))||(r=this.tokenizer.blockquote(e))||(r=this.tokenizer.list(e))||(r=this.tokenizer.html(e))){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let n=t.at(-1);n?.type==="paragraph"||n?.type==="text"?(n.raw+="\n"+r.raw,n.text+="\n"+r.raw,this.inlineQueue.at(-1).src=n.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if((r=this.tokenizer.table(e))||(r=this.tokenizer.lheading(e))){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startBlock){let t,n=1/0,r=e.slice(1);this.options.extensions.startBlock.forEach(e=>{"number"==typeof(t=e.call({lexer:this},r))&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(i=e.substring(0,n+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){let l=t.at(-1);n&&l?.type==="paragraph"?(l.raw+="\n"+r.raw,l.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let n=t.at(-1);n?.type==="text"?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,r=null;if(this.tokens.links){let e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(n));)e.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(n));)n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(r=this.tokenizer.rules.inline.anyPunctuation.exec(n));)n=n.slice(0,r.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let i=!1,l="";for(;e;){let r;if(i||(l=""),i=!1,this.options.extensions?.inline?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if((r=this.tokenizer.escape(e))||(r=this.tokenizer.tag(e))||(r=this.tokenizer.link(e))){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(r.raw.length);let n=t.at(-1);"text"===r.type&&n?.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if((r=this.tokenizer.emStrong(e,n,l))||(r=this.tokenizer.codespan(e))||(r=this.tokenizer.br(e))||(r=this.tokenizer.del(e))||(r=this.tokenizer.autolink(e))||!this.state.inLink&&(r=this.tokenizer.url(e))){e=e.substring(r.raw.length),t.push(r);continue}let s=e;if(this.options.extensions?.startInline){let t,n=1/0,r=e.slice(1);this.options.extensions.startInline.forEach(e=>{"number"==typeof(t=e.call({lexer:this},r))&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(s=e.substring(0,n+1))}if(r=this.tokenizer.inlineText(s)){e=e.substring(r.raw.length),"_"!==r.raw.slice(-1)&&(l=r.raw.slice(-1)),i=!0;let n=t.at(-1);n?.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw Error(t)}}return t}}class eo{options;parser;constructor(e){this.options=e||i}space(e){return""}code({text:e,lang:t,escaped:n}){let r=(t||"").match(a.notSpaceStart)?.[0],i=e.replace(a.endingNewline,"")+"\n";return r?'<pre><code class="language-'+et(r)+'">'+(n?i:et(i,!0))+"</code></pre>\n":"<pre><code>"+(n?i:et(i,!0))+"</code></pre>\n"}blockquote({tokens:e}){let t=this.parser.parse(e);return`<blockquote>
${t}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return"<hr>\n"}list(e){let t=e.ordered,n=e.start,r="";for(let t=0;t<e.items.length;t++){let n=e.items[t];r+=this.listitem(n)}let i=t?"ol":"ul";return"<"+i+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+i+">\n"}listitem(e){let t="";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?e.tokens[0]?.type==="paragraph"?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+et(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",n="";for(let t=0;t<e.header.length;t++)n+=this.tablecell(e.header[t]);t+=this.tablerow({text:n});let r="";for(let t=0;t<e.rows.length;t++){let i=e.rows[t];n="";for(let e=0;e<i.length;e++)n+=this.tablecell(i[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${et(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){let r=this.parser.parseInline(n),i=en(e);if(null===i)return r;let l='<a href="'+(e=i)+'"';return t&&(l+=' title="'+et(t)+'"'),l+=">"+r+"</a>"}image({href:e,title:t,text:n}){let r=en(e);if(null===r)return et(n);e=r;let i=`<img src="${e}" alt="${n}"`;return t&&(i+=` title="${et(t)}"`),i+=">"}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:et(e.text)}}class ec{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class eu{options;renderer;textRenderer;constructor(e){this.options=e||i,this.options.renderer=this.options.renderer||new eo,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new ec}static parse(e,t){return new eu(t).parse(e)}static parseInline(e,t){return new eu(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let e=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==e||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=e||"";continue}}switch(i.type){case"space":n+=this.renderer.space(i);continue;case"hr":n+=this.renderer.hr(i);continue;case"heading":n+=this.renderer.heading(i);continue;case"code":n+=this.renderer.code(i);continue;case"table":n+=this.renderer.table(i);continue;case"blockquote":n+=this.renderer.blockquote(i);continue;case"list":n+=this.renderer.list(i);continue;case"html":n+=this.renderer.html(i);continue;case"paragraph":n+=this.renderer.paragraph(i);continue;case"text":{let l=i,s=this.renderer.text(l);for(;r+1<e.length&&"text"===e[r+1].type;)l=e[++r],s+="\n"+this.renderer.text(l);t?n+=this.renderer.paragraph({type:"paragraph",raw:s,text:s,tokens:[{type:"text",raw:s,text:s,escaped:!0}]}):n+=s;continue}default:{let e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw Error(e)}}}return n}parseInline(e,t=this.renderer){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let e=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=e||"";continue}}switch(i.type){case"escape":case"text":n+=t.text(i);break;case"html":n+=t.html(i);break;case"link":n+=t.link(i);break;case"image":n+=t.image(i);break;case"strong":n+=t.strong(i);break;case"em":n+=t.em(i);break;case"codespan":n+=t.codespan(i);break;case"br":n+=t.br(i);break;case"del":n+=t.del(i);break;default:{let e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw Error(e)}}}return n}}class ep{options;block;constructor(e){this.options=e||i}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?ea.lex:ea.lexInline}provideParser(){return this.block?eu.parse:eu.parseInline}}class eh{defaults=r();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=eu;Renderer=eo;TextRenderer=ec;Lexer=ea;Tokenizer=es;Hooks=ep;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":for(let e of r.header)n=n.concat(this.walkTokens(e.tokens,t));for(let e of r.rows)for(let r of e)n=n.concat(this.walkTokens(r.tokens,t));break;case"list":n=n.concat(this.walkTokens(r.items,t));break;default:{let e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach(r=>{let i=e[r].flat(1/0);n=n.concat(this.walkTokens(i,t))}):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(e=>{let n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach(e=>{if(!e.name)throw Error("extension name required");if("renderer"in e){let n=t.renderers[e.name];n?t.renderers[e.name]=function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:t.renderers[e.name]=e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw Error("extension level must be 'block' or 'inline'");let n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)}),n.extensions=t),e.renderer){let t=this.defaults.renderer||new eo(this.defaults);for(let n in e.renderer){if(!(n in t))throw Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;let r=e.renderer[n],i=t[n];t[n]=(...e)=>{let n=r.apply(t,e);return!1===n&&(n=i.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){let t=this.defaults.tokenizer||new es(this.defaults);for(let n in e.tokenizer){if(!(n in t))throw Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;let r=e.tokenizer[n],i=t[n];t[n]=(...e)=>{let n=r.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){let t=this.defaults.hooks||new ep;for(let n in e.hooks){if(!(n in t))throw Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;let r=e.hooks[n],i=t[n];ep.passThroughHooks.has(n)?t[n]=e=>{if(this.defaults.async)return Promise.resolve(r.call(t,e)).then(e=>i.call(t,e));let n=r.call(t,e);return i.call(t,n)}:t[n]=(...e)=>{let n=r.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){let t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return ea.lex(e,t??this.defaults)}parser(e,t){return eu.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{let r={...n},i={...this.defaults,...r},l=this.onError(!!i.silent,!!i.async);if(!0===this.defaults.async&&!1===r.async)return l(Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return l(Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return l(Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));i.hooks&&(i.hooks.options=i,i.hooks.block=e);let s=i.hooks?i.hooks.provideLexer():e?ea.lex:ea.lexInline,a=i.hooks?i.hooks.provideParser():e?eu.parse:eu.parseInline;if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(e=>s(e,i)).then(e=>i.hooks?i.hooks.processAllTokens(e):e).then(e=>i.walkTokens?Promise.all(this.walkTokens(e,i.walkTokens)).then(()=>e):e).then(e=>a(e,i)).then(e=>i.hooks?i.hooks.postprocess(e):e).catch(l);try{i.hooks&&(t=i.hooks.preprocess(t));let e=s(t,i);i.hooks&&(e=i.hooks.processAllTokens(e)),i.walkTokens&&this.walkTokens(e,i.walkTokens);let n=a(e,i);return i.hooks&&(n=i.hooks.postprocess(n)),n}catch(e){return l(e)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){let e="<p>An error occurred:</p><pre>"+et(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}}let ed=new eh;function ef(e,t){return ed.parse(e,t)}ef.options=ef.setOptions=function(e){return ed.setOptions(e),ef.defaults=ed.defaults,i=ef.defaults,ef},ef.getDefaults=r,ef.defaults=i,ef.use=function(...e){return ed.use(...e),ef.defaults=ed.defaults,i=ef.defaults,ef},ef.walkTokens=function(e,t){return ed.walkTokens(e,t)},ef.parseInline=ed.parseInline,ef.Parser=eu,ef.parser=eu.parse,ef.Renderer=eo,ef.TextRenderer=ec,ef.Lexer=ea,ef.lexer=ea.lex,ef.Tokenizer=es,ef.Hooks=ep,ef.parse=ef,ef.options,ef.setOptions,ef.use,ef.walkTokens,ef.parseInline,eu.parse,ea.lex},4652:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return o},getImageProps:function(){return a}});let r=n(8140),i=n(5040),l=n(1356),s=r._(n(1124));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let o=l.Image},4848:(e,t,n)=>{"use strict";n.d(t,{ViewTransitions:()=>u,o:()=>d});var r=n(5155);n(2619);var i=n(63),l=n(2115);function s(){return window.location.hash}function a(){return""}function o(e){return window.addEventListener("hashchange",e),()=>window.removeEventListener("hashchange",e)}let c=(0,l.createContext)(()=>()=>{});function u(e){let{children:t}=e,[n,u]=(0,l.useState)(null);return(0,l.useEffect)(()=>{n&&(n(),u(null))},[n]),!function(){let e=(0,i.usePathname)(),t=(0,l.useRef)(e),[n,r]=(0,l.useState)(null);(0,l.useEffect)(()=>{if(!("startViewTransition"in document))return()=>{};let e=()=>{let e,t=new Promise(t=>{e=t});r([new Promise(e=>{document.startViewTransition(()=>(e(),t))}),e])};return window.addEventListener("popstate",e),()=>{window.removeEventListener("popstate",e)}},[]),n&&t.current!==e&&(0,l.use)(n[0]);let c=(0,l.useRef)(n);(0,l.useEffect)(()=>{c.current=n},[n]);let u=(0,l.useSyncExternalStore)(o,s,a);(0,l.useEffect)(()=>{t.current=e,c.current&&(c.current[1](),c.current=null)},[u,e])}(),(0,r.jsx)(c.Provider,{value:u,children:t})}function p(){return(p=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function h(e,t){if(null==e)return{};var n,r,i={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}function d(){let e=(0,i.useRouter)(),t=(0,l.use)(c),n=(0,l.useCallback)(function(e){let{onTransitionReady:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!("startViewTransition"in document))return e();{let r=document.startViewTransition(()=>new Promise(n=>{(0,l.startTransition)(()=>{e(),t(()=>n)})}));n&&r.ready.then(n)}},[]),r=(0,l.useCallback)(function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var{onTransitionReady:i}=r,l=h(r,["onTransitionReady"]);n(()=>e.push(t,l),{onTransitionReady:i})},[n,e]),s=(0,l.useCallback)(function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var{onTransitionReady:i}=r,l=h(r,["onTransitionReady"]);n(()=>e.replace(t,l),{onTransitionReady:i})},[n,e]);return(0,l.useMemo)(()=>p({},e,{push:r,replace:s}),[r,s,e])}},5239:(e,t,n)=>{"use strict";n.d(t,{default:()=>i.a});var r=n(4652),i=n.n(r)},6980:(e,t,n)=>{e.exports=window.DOMPurify||(window.DOMPurify=n(3774).default||n(3774))},8655:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(2115);let i=r.forwardRef(function({title:e,titleId:t,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{fillRule:"evenodd",d:"M2 8a.75.75 0 0 1 .75-.75h8.69L8.22 4.03a.75.75 0 0 1 1.06-1.06l4.5 4.5a.75.75 0 0 1 0 1.06l-4.5 4.5a.75.75 0 0 1-1.06-1.06l3.22-3.22H2.75A.75.75 0 0 1 2 8Z",clipRule:"evenodd"}))})}}]);