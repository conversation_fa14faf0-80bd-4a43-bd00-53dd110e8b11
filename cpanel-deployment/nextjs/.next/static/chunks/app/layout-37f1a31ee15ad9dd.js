(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{90:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(2115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},1150:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=n(5155),a=n(2115),l=n(4437);function i(e){return{default:e&&"default"in e?e.default:e}}n(6552);let s={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},o=function(e){let t={...s,...e},n=(0,a.lazy)(()=>t.loader().then(i)),o=t.loading;function c(e){let i=o?(0,r.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,c=s?a.Suspense:a.Fragment,d=t.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(n,{...e})]}):(0,r.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(c,{...s?{fallback:i}:{},children:d})}return c.displayName="LoadableComponent",c}},1290:()=>{},2751:(e,t,n)=>{"use strict";n.d(t,{default:()=>c});var r=n(5155),a=n(6980),l=n.n(a),i=n(4558),s=n(2115);let o=s.forwardRef(function(e,t){let{title:n,titleId:r,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),n?s.createElement("title",{id:r},n):null,s.createElement("path",{fillRule:"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"}))});function c(e){let{data:t}=e,[n,a]=(0,s.useState)(null);if((0,s.useEffect)(()=>{localStorage.getItem("announcementDismissed")||a(!0)},[]),!t)return(0,r.jsx)("div",{className:"bg-neutral-950 relative z-[10000]",children:(0,r.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,r.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "Announcement" component.'})})});if(!n)return null;let{content:c}=t;return c?(0,r.jsx)("aside",{className:"bg-neutral-950 relative z-[10000]",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3 mx-auto max-w-screen-xl text-white pl-[56px] pr-4 py-2",children:[(0,r.jsx)("div",{className:"text-center prose prose-sm leading-snug prose-invert prose-modifier !max-w-none",dangerouslySetInnerHTML:{__html:l().sanitize(i.xI.parse(c))}}),(0,r.jsx)("button",{"aria-label":"Dismiss announcement",className:" p-1 rounded-full bg-white/20 transition hover:bg-white/25 active:bg-white/30 ",onClick:()=>{a(!1),localStorage.setItem("announcementDismissed","true")},children:(0,r.jsx)(o,{className:"size-5"})})]})}):null}},3522:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(5155),a=n(8655),l=n(1496),i=n(2619),s=n.n(i),o=n(4848),c=n(9498);function d(e){let{label:t="Default label",url:n="#",target:i,rel:d,className:u="",showIcon:m=!1,iconType:h="arrowRight",...f}=e,p=(0,o.o)();return(0,r.jsxs)(s(),{target:i,rel:d,href:n,onClick:e=>{e.preventDefault(),p.push(n,{onTransitionReady:c.W})},className:"\n        group\n        inline-flex\n        justify-center\n        items-center\n        transition\n        px-4\n        h-11\n        font-medium\n        leading-none\n        rounded-full\n        text-primary-700\n        border border-white\n        hover:border-neutral-100\n        active:border-neutral-200\n        bg-white\n        hover:bg-neutral-100\n        active:bg-neutral-200\n        ".concat(u,"\n      "),...f,children:[t,m?"arrowUpRight"===h?(0,r.jsx)(l.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):(0,r.jsx)(a.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):null]})}},4054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return l},createSnapshot:function(){return s}});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function l(){return a?new a:new r}function i(e){return a?a.bind(e):r.bind(e)}function s(){return a?a.snapshot():function(e,...t){return e(...t)}}},4437:(e,t,n)=>{"use strict";function r(e){let{reason:t,children:n}=e;return n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),n(4553)},4748:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});let r=(0,n(7909).default)(()=>n.e(461).then(n.bind(n,7461)),{loadableGenerated:{webpack:()=>[7461]},ssr:!1})},5537:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,1290,23)),Promise.resolve().then(n.bind(n,2751)),Promise.resolve().then(n.bind(n,3522)),Promise.resolve().then(n.bind(n,7116)),Promise.resolve().then(n.bind(n,7928)),Promise.resolve().then(n.bind(n,8487)),Promise.resolve().then(n.bind(n,6668)),Promise.resolve().then(n.bind(n,4748)),Promise.resolve().then(n.bind(n,4848)),Promise.resolve().then(n.t.bind(n,2619,23)),Promise.resolve().then(n.t.bind(n,1356,23))},6278:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=n(8140)._(n(1150));function a(e,t){var n;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,r.default)({...l,modules:null==(n=l.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6552:(e,t,n)=>{"use strict";function r(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return r}}),n(5155),n(7650),n(8567),n(7278)},6668:(e,t,n)=>{"use strict";n.d(t,{default:()=>m});var r=n(5155),a=n(5239),l=n(8662),i=n(2115);let s=i.forwardRef(function({title:e,titleId:t,...n},r){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?i.createElement("title",{id:t},e):null,i.createElement("path",{fillRule:"evenodd",d:"M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z",clipRule:"evenodd"}))});var o=n(2619),c=n.n(o),d=n(4848),u=n(9498);function m(e){var t,n,o,m,h,f,p,x;let{data:b,siteRepresentation:v}=e,[g,y]=(0,i.useState)(!1),j=(0,d.o)(),w=(0,i.useCallback)(()=>{y(!g)},[g]);if(!b||!v)return(0,r.jsx)("div",{className:"backdrop-blur-xl sticky top-0 z-[1000] border-b border-black/15",children:(0,r.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,r.jsx)("div",{className:"text-red-600 text-center",children:'Error: We encountered an issue while loading the "Header" component.'})})});let{additionalNavigationItems:k,cta:N}=b,{logo:_,logomark:P}=v,R=new URL(_.url,"https://api.yourdomain.com").href,E=new URL(P.url,"https://api.yourdomain.com").href;return(0,r.jsx)("header",{className:"backdrop-blur-xl sticky top-0 z-[1000] border-b border-black/15",children:(0,r.jsxs)("nav",{className:"flex flex-wrap gap-4 md:gap-6 items-center justify-between p-4",children:[(0,r.jsxs)(c(),{href:"/",className:"block text-primary-700",onClick:e=>{e.preventDefault(),j.push("/",{onTransitionReady:u.W})},children:[(0,r.jsx)("span",{className:"sr-only",children:"Home"}),(0,r.jsx)(a.default,{draggable:"false",priority:!0,src:R,alt:null!=(t=_.alternativeText)?t:"",className:"hidden md:block",width:Math.round((null!=(n=null==_?void 0:_.width)?n:36)/2),height:Math.round((null!=(o=null==_?void 0:_.height)?o:36)/2),sizes:"".concat(Math.round((null!=(m=null==_?void 0:_.width)?m:36)/2),"px")}),(0,r.jsx)(a.default,{draggable:"false",priority:!0,src:E,alt:null!=(h=P.alternativeText)?h:"",className:"md:hidden",width:Math.round((null!=(f=null==P?void 0:P.width)?f:36)/2),height:Math.round((null!=(p=null==P?void 0:P.height)?p:36)/2),sizes:"".concat(Math.round((null!=(x=null==P?void 0:P.width)?x:36)/2),"px")})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 md:order-2",children:[(0,r.jsx)(l.default,{className:"!h-9",target:N.openLinkInNewTab?"_blank":void 0,rel:N.sameHostLink?void 0:"noopener noreferrer",label:N.label,url:N.url,showIcon:N.showIcon,iconType:N.iconType}),(0,r.jsxs)("button",{className:" block justify-items-center w-9 h-9 rounded-full transition border border-primary-100 text-primary-700 bg-primary-50 hover:bg-primary-100 active:bg-primary-200 md:hidden ","aria-label":"Toggle navigation","aria-expanded":g,"aria-controls":"header-navigation",onClick:w,children:[(0,r.jsx)("span",{className:"sr-only",children:"Toggle menu"}),(0,r.jsx)(s,{className:"size-5"})]})]}),(0,r.jsxs)("ul",{id:"header-navigation",className:"header-navigation flex flex-col basis-full grow text-base md:flex-row md:basis-auto ".concat(g?"show":""),children:[(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/projects/",className:"block py-[10px] leading-none md:px-2 text-gray-900 transition hover:text-gray-900/75",onClick:e=>{e.preventDefault(),j.push("/projects/",{onTransitionReady:u.W})},children:"Projects"})}),(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/blog/",className:"block py-[10px] leading-none md:px-2 text-gray-900 transition hover:text-gray-900/75",onClick:e=>{e.preventDefault(),j.push("/blog/",{onTransitionReady:u.W})},children:"Blog"})}),k.length>0&&k.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(c(),{target:e.openLinkInNewTab?"_blank":void 0,rel:e.sameHostLink?void 0:"noopener noreferrer",href:e.url,className:"block py-[10px] leading-none md:px-2 text-gray-900 transition hover:text-gray-900/75",children:e.label})},e.id))]})]})})}},7116:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(5155),a=n(90),l=n(9852);let i=e=>{let t,n,{type:i,className:s,showIcon:o=!0,...c}=e,d=atob("your_base64_encoded_email").trim(),u=atob("your_base64_encoded_phone").trim(),m=u?u.replace(/[^\d+]/g,""):null;if("email"===i)t=d?"mailto:".concat(d):"#",n=d||"Email not available";else{if("telephone"!==i)return console.error('Invalid type "'.concat(i,'" passed to ContactLink.')),null;t=m?"tel:".concat(m):"#",n=u||"Phone not available"}let h=o&&"email"===i?a.A:o&&"telephone"===i?l.A:null;return(0,r.jsxs)("a",{className:s,href:t,...c,children:[h&&(0,r.jsx)(h,{className:"h-[1.2em] w-[1.2em] shrink-0"})," ",(0,r.jsx)("span",{children:n})]})}},7828:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,n(4054).createAsyncLocalStorage)()},7909:(e,t,n)=>{"use strict";n.d(t,{default:()=>a.a});var r=n(6278),a=n.n(r)},7928:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(5155),a=n(2619),l=n.n(a),i=n(4848),s=n(9498);function o(e){let{copyright:t}=e,n=(0,i.o)();return(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between",children:[(0,r.jsx)(l(),{className:"text-white/75 text-base transition hover:underline md:order-2 text-center mb-4 md:mb-0",href:"/privacy-policy/",onClick:e=>{e.preventDefault(),n.push("/privacy-policy/",{onTransitionReady:s.W})},children:"Privacy policy"}),(0,r.jsx)("p",{className:"text-white/75 text-base md:order-1 text-center",children:t})]})}},8487:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(5155),a=n(2619),l=n.n(a),i=n(4848),s=n(9498);function o(){let e=(0,i.o)();return(0,r.jsxs)("div",{className:"mt-[6px] md:mt-0 col-span-1",children:[(0,r.jsx)("h3",{className:"text-white font-medium text-xl tracking-tight text-center md:text-start",children:"Navigation"}),(0,r.jsxs)("ul",{className:"mt-4 space-y-4",children:[(0,r.jsx)("li",{className:"text-center md:text-start",children:(0,r.jsx)(l(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/",onClick:t=>{t.preventDefault(),e.push("/",{onTransitionReady:s.W})},children:"Home"})}),(0,r.jsx)("li",{className:"text-center md:text-start",children:(0,r.jsx)(l(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/projects/",onClick:t=>{t.preventDefault(),e.push("/projects/",{onTransitionReady:s.W})},children:"Projects"})}),(0,r.jsx)("li",{className:"text-center md:text-start",children:(0,r.jsx)(l(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/blog/",onClick:t=>{t.preventDefault(),e.push("/blog/",{onTransitionReady:s.W})},children:"Blog"})}),(0,r.jsx)("li",{className:"text-center md:text-start",children:(0,r.jsx)(l(),{className:"block md:inline text-base text-white/75 hover:underline",href:"/contact/",onClick:t=>{t.preventDefault(),e.push("/contact/",{onTransitionReady:s.W})},children:"Contact"})})]})]})}},8567:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=n(7828)},8662:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(5155),a=n(8655),l=n(1496),i=n(2619),s=n.n(i),o=n(4848),c=n(9498);function d(e){let{label:t="Default label",url:n="#",target:i,rel:d,className:u="",showIcon:m=!1,iconType:h="arrowRight",...f}=e,p=(0,o.o)();return(0,r.jsxs)(s(),{target:i,rel:d,href:n,onClick:e=>{e.preventDefault(),p.push(n,{onTransitionReady:c.W})},className:"\n        group\n        inline-flex\n        justify-center\n        items-center\n        transition\n        px-4\n        h-11\n        font-medium\n        leading-none\n        rounded-full\n        text-white\n        border border-primary-700\n        hover:border-primary-600\n        active:border-primary-500\n        bg-primary-700\n        hover:bg-primary-600\n        active:bg-primary-500\n        ".concat(u,"\n      "),...f,children:[t,m?"arrowUpRight"===h?(0,r.jsx)(l.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):(0,r.jsx)(a.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):null]})}},9498:(e,t,n)=>{"use strict";n.d(t,{W:()=>a,Y:()=>r});let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.DateTimeFormat(t,{dateStyle:"short"}).format(new Date(e))},a=()=>{document.documentElement.animate([{opacity:1,scale:1,transform:"translateY(0)"},{opacity:.5,scale:.9,transform:"translateY(-100px)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-old(root)"}),document.documentElement.animate([{transform:"translateY(100%)"},{transform:"translateY(0)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-new(root)"})}},9852:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(2115);let a=r.forwardRef(function(e,t){let{title:n,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),n?r.createElement("title",{id:a},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})}},e=>{e.O(0,[741,619,305,167,441,255,358],()=>e(e.s=5537)),_N_E=e.O()}]);