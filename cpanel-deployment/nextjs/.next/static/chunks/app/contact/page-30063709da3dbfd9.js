(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{1866:(e,a,t)=>{Promise.resolve().then(t.bind(t,7116)),Promise.resolve().then(t.bind(t,7548)),Promise.resolve().then(t.bind(t,4748)),Promise.resolve().then(t.t.bind(t,2619,23))},4748:(e,a,t)=>{"use strict";t.d(a,{default:()=>n});let n=(0,t(7909).default)(()=>t.e(461).then(t.bind(t,7461)),{loadableGenerated:{webpack:()=>[7461]},ssr:!1})},7116:(e,a,t)=>{"use strict";t.d(a,{default:()=>l});var n=t(5155),r=t(90),s=t(9852);let l=e=>{let a,t,{type:l,className:i,showIcon:o=!0,...d}=e,c=atob("your_base64_encoded_email").trim(),m=atob("your_base64_encoded_phone").trim(),u=m?m.replace(/[^\d+]/g,""):null;if("email"===l)a=c?"mailto:".concat(c):"#",t=c||"Email not available";else{if("telephone"!==l)return console.error('Invalid type "'.concat(l,'" passed to ContactLink.')),null;a=u?"tel:".concat(u):"#",t=m||"Phone not available"}let p=o&&"email"===l?r.A:o&&"telephone"===l?s.A:null;return(0,n.jsxs)("a",{className:i,href:a,...d,children:[p&&(0,n.jsx)(p,{className:"h-[1.2em] w-[1.2em] shrink-0"})," ",(0,n.jsx)("span",{children:t})]})}},7548:(e,a,t)=>{"use strict";t.d(a,{default:()=>v});var n=t(5155),r=t(2115),s=t(2544),l=t(6942),i=t(8908),o=t(656),d=t(2619),c=t.n(d),m=t(4879);let u=m.Ik({alternativeText:m.Yj().nullable(),width:m.ai().nullable(),height:m.ai().nullable(),url:m.Yj()}),p=m.Ik({headline:m.Yj(),supportiveText:m.Yj()}),b=m.Ik({title:m.Yj().nullable(),description:m.Yj().nullable(),image:u.nullable()}),j=m.Ik({id:m.ai(),channel:m.Yj().refine(e=>"GitHub"===e||"LinkedIn"==e||"X"===e,{message:"Value must be 'GitHub', 'LinkedIn' or 'X'"}),url:m.Yj(),label:m.Yj()}),h=m.Ik({id:m.ai(),label:m.Yj(),url:m.Yj(),openLinkInNewTab:m.zM(),sameHostLink:m.zM(),showIcon:m.zM(),iconType:m.k5(["arrowRight","arrowUpRight"])}),x=m.Ik({headline:m.Yj(),supportiveText:m.Yj()}),g=m.Ik({id:m.ai(),authorName:m.Yj(),isOrganization:m.zM(),url:m.Yj()}).nullable(),Y=m.Ik({id:m.ai(),title:m.Yj(),slug:m.Yj(),excerpt:m.Yj(),content:m.Yj(),createdAt:m.Yj().datetime(),updatedAt:m.Yj().datetime(),featuredImage:u,author:g}),k=m.Ik({id:m.ai(),title:m.Yj(),slug:m.Yj(),excerpt:m.Yj(),demoUrl:m.Yj().nullable(),repoUrl:m.Yj().nullable(),content:m.Yj(),duration:m.Yj(),featuredImage:u,scopes:m.YO(m.Ik({id:m.ai(),title:m.Yj()})),tools:m.YO(m.Ik({id:m.ai(),title:m.Yj()})),designFile:m.Ik({url:m.Yj()}).nullable(),author:g});m.Ik({data:m.YO(Y)}),m.Ik({data:m.YO(k)}),m.Ik({data:m.Ik({announcement:m.Ik({content:m.Yj().nullable()}),header:m.Ik({additionalNavigationItems:m.YO(h),cta:h}),cta:x.extend({button:h}),footer:m.Ik({statement:m.Yj(),copyright:m.Yj()}),siteRepresentation:m.Ik({isOrganization:m.zM(),siteName:m.Yj(),siteDescription:m.Yj(),siteImage:u,jobTitle:m.Yj().nullable(),schedulingLink:m.Yj().nullable(),logo:u,logomark:u,socialChannels:m.YO(j),addressLocality:m.Yj(),areaServed:m.Yj().nullable(),businessHours:m.Yj().nullable(),knowsAbout:m.YO(m.Ik({name:m.Yj(),children:m.YO(m.Ik({name:m.Yj(),value:m.ai()}))}))}),miscellaneous:m.Ik({localeString:m.Yj(),htmlLanguageTag:m.Yj(),themeColor:m.Yj()}),icons:m.Ik({iconICO:u,iconSVG:u,iconPNG:u})})}),m.Ik({data:m.Ik({metadata:b,hero:x.extend({greeting:m.Yj().nullable(),primaryButton:h.nullable(),secondaryButton:h.nullable()}),about:x.extend({content:m.Yj(),image:u}),featuredProjects:x,skills:x,testimonials:x.extend({testimonialList:m.YO(m.Ik({id:m.ai(),statement:m.Yj(),author:m.Yj(),role:m.Yj(),company:m.Yj(),companyWebsite:m.Yj()})).nonempty()}),faq:x.extend({faqList:m.YO(m.Ik({id:m.ai(),question:m.Yj(),answer:m.Yj()})).nonempty()}),latestPosts:x,useCaseSpecificContent:m.YO(m.gM("__component",[x.extend({__component:m.eu("sections.services"),serviceList:m.YO(m.Ik({id:m.ai(),description:m.Yj(),title:m.Yj()})).nonempty()}),x.extend({__component:m.eu("sections.experience"),experienceList:m.YO(m.Ik({id:m.ai(),role:m.Yj(),company:m.Yj(),companyUrl:m.Yj().nullable(),duration:m.Yj(),location:m.Yj(),description:m.Yj(),content:m.Yj(),companyLogo:u})).nonempty()})]))})}),m.Ik({data:m.Ik({metadata:b,banner:p})}),m.Ik({data:m.Ik({metadata:b,banner:p})}),m.Ik({data:m.Ik({metadata:b,banner:p,contactFormHeading:m.Yj(),otherContactOptionsHeading:m.Yj()})}),m.Ik({data:m.Ik({metadata:b,banner:p,content:m.Yj()})}),m.Ik({data:m.Ik({metadata:b,banner:p})});let y=m.Ik({name:m.Yj().trim(),email:m.Yj().trim().nonempty({message:"Email is required."}).email({message:"Invalid email address."}),message:m.Yj().trim().nonempty({message:"Message is required."}).min(10,{message:"Message must be at least 10 characters long."}),consent:m.zM().refine(e=>!0===e,{message:"Consent is required."})});m.Ik({data:m.YO(m.Ik({slug:m.Yj()}))}),m.Ik({data:m.YO(m.Ik({title:m.Yj(),excerpt:m.Yj(),featuredImage:u}))});var f=t(926);let I=(0,f.createServerReference)("40e911967c2b3f3c9f0af05e8a82cc52e54a76f8be",f.callServer,void 0,f.findSourceMapURL,"onSubmitAction");function v(){var e,a,t;let[d,m]=(0,r.useState)(!1),[u,p]=(0,r.useState)(!1),[b,j]=(0,r.useState)(!1),{register:h,handleSubmit:x,reset:g,setFocus:Y,formState:{errors:k}}=(0,s.mN)({resolver:(0,l.u)(y),defaultValues:{name:"",email:"",message:"",consent:!1}}),f=async e=>{if(m(!0),j(!1),e.name){g(),m(!1);return}try{let a={name:e.name.trim(),email:e.email.trim(),message:e.message.trim(),consent:e.consent};await I(a),g(),p(!0),setTimeout(()=>p(!1),1e4)}catch(e){console.error("Error submitting form: ",e),j(!0),k.email?Y("email"):k.message?Y("message"):k.consent&&Y("consent")}finally{m(!1)}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("form",{onSubmit:x(f),noValidate:!0,className:"flex flex-col gap-6 sm:gap-6",children:[(0,n.jsx)("div",{style:{display:"none"},children:(0,n.jsxs)("label",{className:"relative block border border-neutral-300 bg-transparent rounded-lg",children:[(0,n.jsx)("input",{...h("name"),type:"hidden",placeholder:"Your name",className:"rounded-lg outline-none peer w-full border-none bg-transparent px-4 py-2 text-gray-700 placeholder-transparent sm:text-xl",tabIndex:-1,autoComplete:"off"}),(0,n.jsx)("span",{className:"bg-neutral-50 px-1 absolute left-[12px] top-0 -translate-y-1/2 text- transition-all peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-focus:top-0 peer-focus:text-base peer-focus:text-primary-700",children:"Your name"})]})}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"relative block border border-neutral-300 bg-transparent rounded-lg",children:[(0,n.jsx)("input",{"aria-describedby":"email-error","aria-invalid":k.email?"true":"false",...h("email"),type:"email",placeholder:"Your email",className:"block rounded-lg outline-none peer w-full border-none bg-transparent px-4 py-2 text-gray-700 placeholder-transparent sm:text-xl"}),(0,n.jsx)("span",{className:"bg-neutral-50 px-1 absolute left-[12px] top-0 -translate-y-1/2 text- transition-all peer-placeholder-shown:top-1/2 peer-placeholder-shown:text-base peer-focus:top-0 peer-focus:text-base peer-focus:text-primary-700",children:"Your email"})]}),(0,n.jsx)("div",{id:"email-error",className:"text-red-500 text-sm mt-1 ".concat(k.email?"":"sr-only"),children:null==(e=k.email)?void 0:e.message})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"relative block border border-neutral-300 bg-transparent rounded-lg",children:[(0,n.jsx)("textarea",{"aria-describedby":"message-error","aria-invalid":k.message?"true":"false",...h("message"),rows:"5",placeholder:"Your message",className:"block rounded-lg peer w-full border-none bg-transparent px-4 py-2 text-gray-700 placeholder-transparent focus:border-transparent focus:outline-none text-xl"}),(0,n.jsx)("span",{className:"bg-neutral-50 px-1 absolute left-[12px] top-0 -translate-y-1/2 text-base transition-all peer-placeholder-shown:translate-y-1/2 peer-placeholder-shown:text-base peer-focus:-translate-y-1/2 peer-focus:text-base peer-focus:text-primary-700",children:"Your message"})]}),(0,n.jsx)("div",{id:"message-error",className:"text-red-500 text-sm mt-1 ".concat(k.message?"":"sr-only"),children:null==(a=k.message)?void 0:a.message})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"flex cursor-pointer items-start gap-3 transition",children:[(0,n.jsxs)("div",{className:"relative flex items-center mt-[1px]",children:[(0,n.jsx)("input",{name:"consent","aria-describedby":"consent-error","aria-invalid":k.consent?"true":"false",...h("consent"),type:"checkbox",className:"peer size-5 rounded border border-neutral-400 appearance-none checked:bg-primary-700 checked:border-0"}),(0,n.jsx)(o.A,{className:"absolute hidden fill-white peer-checked:block"})]}),(0,n.jsxs)("div",{className:"text-pretty font-light text-gray-700",children:["I have read the ",(0,n.jsx)(c(),{href:"/privacy-policy",target:"_blank",className:"font-medium border-b border-primary-700 hover:border-b-2",children:"privacy policy"})," and consent to having my submitted information collected and processed to respond to my inquiry."]})]}),(0,n.jsx)("div",{id:"consent-error",className:"text-red-500 text-sm mt-1 ".concat(k.consent?"":"sr-only"),children:null==(t=k.consent)?void 0:t.message})]}),(0,n.jsx)("button",{disabled:d,type:"submit",className:"\n            group\n            inline-flex\n            justify-center\n            items-center\n            transition\n            px-4\n            h-11\n            font-medium\n            leading-none\n            rounded-lg\n            text-white\n            border border-primary-700\n            hover:border-primary-600\n            active:border-primary-500\n            bg-primary-700\n            hover:bg-primary-600\n            active:bg-primary-500\n            ".concat(d?"cursor-not-allowed opacity-50":"","\n          "),"aria-label":"Submit your message",children:d?"Submitting...":(0,n.jsxs)(n.Fragment,{children:["Submit message",(0,n.jsx)(i.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"})]})})]}),b&&(0,n.jsx)("div",{className:"text-red-500 text-sm mt-1",children:"An error occurred while submitting the form. Please try again later."}),u&&(0,n.jsx)("div",{className:"text-green-500 text-sm mt-1",children:"Your message has been submitted successfully!"})]})}}},e=>{e.O(0,[619,405,441,255,358],()=>e(e.s=1866)),_N_E=e.O()}]);