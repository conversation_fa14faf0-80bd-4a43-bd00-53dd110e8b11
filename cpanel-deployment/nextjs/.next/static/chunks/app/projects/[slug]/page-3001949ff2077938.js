(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[419],{1496:(e,n,r)=>{"use strict";r.d(n,{A:()=>a});var t=r(2115);let a=t.forwardRef(function({title:e,titleId:n,...r},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":n},r),e?t.createElement("title",{id:n},e):null,t.createElement("path",{fillRule:"evenodd",d:"M4.22 11.78a.75.75 0 0 1 0-1.06L9.44 5.5H5.75a.75.75 0 0 1 0-1.5h5.5a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-1.5 0V6.56l-5.22 5.22a.75.75 0 0 1-1.06 0Z",clipRule:"evenodd"}))})},4848:(e,n,r)=>{"use strict";r.d(n,{ViewTransitions:()=>u,o:()=>h});var t=r(5155);r(2619);var a=r(63),i=r(2115);function l(){return window.location.hash}function s(){return""}function o(e){return window.addEventListener("hashchange",e),()=>window.removeEventListener("hashchange",e)}let c=(0,i.createContext)(()=>()=>{});function u(e){let{children:n}=e,[r,u]=(0,i.useState)(null);return(0,i.useEffect)(()=>{r&&(r(),u(null))},[r]),!function(){let e=(0,a.usePathname)(),n=(0,i.useRef)(e),[r,t]=(0,i.useState)(null);(0,i.useEffect)(()=>{if(!("startViewTransition"in document))return()=>{};let e=()=>{let e,n=new Promise(n=>{e=n});t([new Promise(e=>{document.startViewTransition(()=>(e(),n))}),e])};return window.addEventListener("popstate",e),()=>{window.removeEventListener("popstate",e)}},[]),r&&n.current!==e&&(0,i.use)(r[0]);let c=(0,i.useRef)(r);(0,i.useEffect)(()=>{c.current=r},[r]);let u=(0,i.useSyncExternalStore)(o,l,s);(0,i.useEffect)(()=>{n.current=e,c.current&&(c.current[1](),c.current=null)},[u,e])}(),(0,t.jsx)(c.Provider,{value:u,children:n})}function d(){return(d=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e}).apply(this,arguments)}function m(e,n){if(null==e)return{};var r,t,a={},i=Object.keys(e);for(t=0;t<i.length;t++)r=i[t],n.indexOf(r)>=0||(a[r]=e[r]);return a}function h(){let e=(0,a.useRouter)(),n=(0,i.use)(c),r=(0,i.useCallback)(function(e){let{onTransitionReady:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!("startViewTransition"in document))return e();{let t=document.startViewTransition(()=>new Promise(r=>{(0,i.startTransition)(()=>{e(),n(()=>r)})}));r&&t.ready.then(r)}},[]),t=(0,i.useCallback)(function(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var{onTransitionReady:a}=t,i=m(t,["onTransitionReady"]);r(()=>e.push(n,i),{onTransitionReady:a})},[r,e]),l=(0,i.useCallback)(function(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var{onTransitionReady:a}=t,i=m(t,["onTransitionReady"]);r(()=>e.replace(n,i),{onTransitionReady:a})},[r,e]);return(0,i.useMemo)(()=>d({},e,{push:t,replace:l}),[t,l,e])}},4981:(e,n,r)=>{"use strict";r.r(n),r.d(n,{default:()=>u});var t=r(5155),a=r(8655),i=r(1496),l=r(2619),s=r.n(l),o=r(4848),c=r(9498);function u(e){let{label:n="Default label",url:r="#",target:l,rel:u,className:d="",showIcon:m=!1,iconType:h="arrowRight",...f}=e,p=(0,o.o)();return(0,t.jsxs)(s(),{target:l,rel:u,href:r,onClick:e=>{e.preventDefault(),p.push(r,{onTransitionReady:c.W})},className:"\n        group\n        inline-flex\n        justify-center\n        items-center\n        transition\n        px-4\n        h-11\n        font-medium\n        leading-none\n        rounded-full\n        border border-primary-100\n        text-primary-700\n        bg-primary-50\n        hover:bg-primary-100\n        active:bg-primary-200\n        ".concat(d,"\n      "),...f,children:[n,m?"arrowUpRight"===h?(0,t.jsx)(i.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):(0,t.jsx)(a.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):null]})}},8464:(e,n,r)=>{Promise.resolve().then(r.bind(r,8662)),Promise.resolve().then(r.bind(r,4981)),Promise.resolve().then(r.bind(r,8578)),Promise.resolve().then(r.t.bind(r,2619,23)),Promise.resolve().then(r.t.bind(r,1356,23)),Promise.resolve().then(r.t.bind(r,9895,23))},8578:(e,n,r)=>{"use strict";r.d(n,{default:()=>s});var t=r(5155),a=r(63),i=r(335);let l={Envelope:(0,t.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,t.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM218 271.7L64.2 172.4C66 156.4 79.5 144 96 144l256 0c16.5 0 30 12.4 31.8 28.4L230 271.7c-1.8 1.2-3.9 1.8-6 1.8s-4.2-.6-6-1.8zm29.4 26.9L384 210.4 384 336c0 17.7-14.3 32-32 32L96 368c-17.7 0-32-14.3-32-32l0-125.6 136.6 88.2c7 4.5 15.1 6.9 23.4 6.9s16.4-2.4 23.4-6.9z"})}),Facebook:(0,t.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,t.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64h98.2V334.2H109.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H255V480H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z"})}),LinkedIn:(0,t.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,t.jsx)("path",{d:"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"})}),X:(0,t.jsx)("svg",{className:"size-8 fill-primary-700 hover:fill-primary-600 active:fill-primary-500 transition",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,t.jsx)("path",{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm297.1 84L257.3 234.6 379.4 396H283.8L209 298.1 123.3 396H75.8l111-126.9L69.7 116h98l67.7 89.5L313.6 116h47.5zM323.3 367.6L153.4 142.9H125.1L296.9 367.6h26.3z"})})};function s(){let e=new URL((0,a.usePathname)(),"https://yourdomain.com").href;return(0,t.jsxs)("dl",{className:"flex flex-col gap-2 not-prose",children:[(0,t.jsx)("dt",{className:"text-gray-900 font-medium",children:"Share this page"}),(0,t.jsxs)("dd",{className:"flex flex-wrap gap-3",children:[(0,t.jsx)(i.r6,{url:e,"aria-label":"Share on X",children:l.X||(0,t.jsx)("span",{className:"text-red-500",children:"Icon not found"})}),(0,t.jsx)(i.wk,{url:e,"aria-label":"Share on LinkedIn",children:l.LinkedIn||(0,t.jsx)("span",{className:"text-red-500",children:"Icon not found"})}),(0,t.jsx)(i.uI,{url:e,"aria-label":"Share on Facebook",children:l.Facebook||(0,t.jsx)("span",{className:"text-red-500",children:"Icon not found"})}),(0,t.jsx)(i.Ot,{url:e,"aria-label":"Share via Email",children:l.Envelope||(0,t.jsx)("span",{className:"text-red-500",children:"Icon not found"})})]})]})}},8655:(e,n,r)=>{"use strict";r.d(n,{A:()=>a});var t=r(2115);let a=t.forwardRef(function({title:e,titleId:n,...r},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":n},r),e?t.createElement("title",{id:n},e):null,t.createElement("path",{fillRule:"evenodd",d:"M2 8a.75.75 0 0 1 .75-.75h8.69L8.22 4.03a.75.75 0 0 1 1.06-1.06l4.5 4.5a.75.75 0 0 1 0 1.06l-4.5 4.5a.75.75 0 0 1-1.06-1.06l3.22-3.22H2.75A.75.75 0 0 1 2 8Z",clipRule:"evenodd"}))})},8662:(e,n,r)=>{"use strict";r.d(n,{default:()=>u});var t=r(5155),a=r(8655),i=r(1496),l=r(2619),s=r.n(l),o=r(4848),c=r(9498);function u(e){let{label:n="Default label",url:r="#",target:l,rel:u,className:d="",showIcon:m=!1,iconType:h="arrowRight",...f}=e,p=(0,o.o)();return(0,t.jsxs)(s(),{target:l,rel:u,href:r,onClick:e=>{e.preventDefault(),p.push(r,{onTransitionReady:c.W})},className:"\n        group\n        inline-flex\n        justify-center\n        items-center\n        transition\n        px-4\n        h-11\n        font-medium\n        leading-none\n        rounded-full\n        text-white\n        border border-primary-700\n        hover:border-primary-600\n        active:border-primary-500\n        bg-primary-700\n        hover:bg-primary-600\n        active:bg-primary-500\n        ".concat(d,"\n      "),...f,children:[n,m?"arrowUpRight"===h?(0,t.jsx)(i.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):(0,t.jsx)(a.A,{className:"h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"}):null]})}},9498:(e,n,r)=>{"use strict";r.d(n,{W:()=>a,Y:()=>t});let t=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.DateTimeFormat(n,{dateStyle:"short"}).format(new Date(e))},a=()=>{document.documentElement.animate([{opacity:1,scale:1,transform:"translateY(0)"},{opacity:.5,scale:.9,transform:"translateY(-100px)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-old(root)"}),document.documentElement.animate([{transform:"translateY(100%)"},{transform:"translateY(0)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-new(root)"})}}},e=>{e.O(0,[448,619,305,881,441,255,358],()=>e(e.s=8464)),_N_E=e.O()}]);