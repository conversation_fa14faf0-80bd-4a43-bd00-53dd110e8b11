(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[831],{63:(e,t,n)=>{"use strict";var r=n(7260);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},732:(e,t,n)=>{Promise.resolve().then(n.bind(n,5930))},4848:(e,t,n)=>{"use strict";n.d(t,{ViewTransitions:()=>c,o:()=>f});var r=n(5155);n(2619);var a=n(63),s=n(2115);function i(){return window.location.hash}function l(){return""}function o(e){return window.addEventListener("hashchange",e),()=>window.removeEventListener("hashchange",e)}let u=(0,s.createContext)(()=>()=>{});function c(e){let{children:t}=e,[n,c]=(0,s.useState)(null);return(0,s.useEffect)(()=>{n&&(n(),c(null))},[n]),!function(){let e=(0,a.usePathname)(),t=(0,s.useRef)(e),[n,r]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!("startViewTransition"in document))return()=>{};let e=()=>{let e,t=new Promise(t=>{e=t});r([new Promise(e=>{document.startViewTransition(()=>(e(),t))}),e])};return window.addEventListener("popstate",e),()=>{window.removeEventListener("popstate",e)}},[]),n&&t.current!==e&&(0,s.use)(n[0]);let u=(0,s.useRef)(n);(0,s.useEffect)(()=>{u.current=n},[n]);let c=(0,s.useSyncExternalStore)(o,i,l);(0,s.useEffect)(()=>{t.current=e,u.current&&(u.current[1](),u.current=null)},[c,e])}(),(0,r.jsx)(u.Provider,{value:c,children:t})}function d(){return(d=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function m(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}function f(){let e=(0,a.useRouter)(),t=(0,s.use)(u),n=(0,s.useCallback)(function(e){let{onTransitionReady:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!("startViewTransition"in document))return e();{let r=document.startViewTransition(()=>new Promise(n=>{(0,s.startTransition)(()=>{e(),t(()=>n)})}));n&&r.ready.then(n)}},[]),r=(0,s.useCallback)(function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var{onTransitionReady:a}=r,s=m(r,["onTransitionReady"]);n(()=>e.push(t,s),{onTransitionReady:a})},[n,e]),i=(0,s.useCallback)(function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var{onTransitionReady:a}=r,s=m(r,["onTransitionReady"]);n(()=>e.replace(t,s),{onTransitionReady:a})},[n,e]);return(0,s.useMemo)(()=>d({},e,{push:r,replace:i}),[r,i,e])}},5930:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(5155),a=n(8655),s=n(2619),i=n.n(s),l=n(9498),o=n(4848);function u(e){let{title:t,excerpt:n,slug:s,createdAt:u,localeString:c}=e,d=(0,l.Y)(u,c),m=(0,o.o)();return(0,r.jsxs)("article",{className:"relative p-4 py-6 bg-white hover:bg-neutral-100 transition border border-neutral-200 rounded-2xl",children:[(0,r.jsxs)("dl",{className:"text-xs leading-6 flex gap-1 mb-1",children:[(0,r.jsx)("dt",{className:"sr-only",children:"Posted on:"}),(0,r.jsx)("dd",{children:(0,r.jsx)("time",{dateTime:u,children:d})})]}),(0,r.jsx)("h3",{className:"text-gray-900 font-normal text-xl sm:text-2xl tracking-tight relative",children:t}),(0,r.jsx)("p",{className:"text-gray-700 mt-2 mb-4 relative",children:n}),(0,r.jsxs)(i(),{href:"/blog/".concat(s,"/"),onClick:e=>{e.preventDefault(),m.push("/blog/".concat(s,"/"),{onTransitionReady:l.W})},className:" group flex font-medium leading-none text-primary-700 ",children:[(0,r.jsx)("span",{className:"absolute top-0 bottom-0 left-0 right-0 rounded-2xl block"}),(0,r.jsxs)("span",{className:"relative",children:["Read more",(0,r.jsxs)("span",{className:"sr-only",children:[", about ",t]})]}),(0,r.jsx)(a.A,{className:"relative h-[1em] w-[1em] ms-1 group-hover:translate-x-0.5 transition"})]})]})}},8655:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(2115);let a=r.forwardRef(function({title:e,titleId:t,...n},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{fillRule:"evenodd",d:"M2 8a.75.75 0 0 1 .75-.75h8.69L8.22 4.03a.75.75 0 0 1 1.06-1.06l4.5 4.5a.75.75 0 0 1 0 1.06l-4.5 4.5a.75.75 0 0 1-1.06-1.06l3.22-3.22H2.75A.75.75 0 0 1 2 8Z",clipRule:"evenodd"}))})},9498:(e,t,n)=>{"use strict";n.d(t,{W:()=>a,Y:()=>r});let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.DateTimeFormat(t,{dateStyle:"short"}).format(new Date(e))},a=()=>{document.documentElement.animate([{opacity:1,scale:1,transform:"translateY(0)"},{opacity:.5,scale:.9,transform:"translateY(-100px)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-old(root)"}),document.documentElement.animate([{transform:"translateY(100%)"},{transform:"translateY(0)"}],{duration:1e3,easing:"cubic-bezier(0.76, 0, 0.24, 1)",fill:"forwards",pseudoElement:"::view-transition-new(root)"})}}},e=>{e.O(0,[619,441,255,358],()=>e(e.s=732)),_N_E=e.O()}]);