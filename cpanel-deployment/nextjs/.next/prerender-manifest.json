{"version": 4, "routes": {"/_not-found": {"initialStatus": 404, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/_not-found", "dataRoute": "/_not-found.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/blog": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/blog", "dataRoute": "/blog.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/sitemap.xml": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "application/xml", "x-next-cache-tags": "_N_T_/layout,_N_T_/sitemap.xml/layout,_N_T_/sitemap.xml/route,_N_T_/sitemap.xml/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/sitemap.xml", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/contact": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/contact", "dataRoute": "/contact.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/projects": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/projects", "dataRoute": "/projects.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/privacy-policy": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/privacy-policy", "dataRoute": "/privacy-policy.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {"/blog/[slug]": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/blog/([^/]+?)(?:/)?$", "dataRoute": "/blog/[slug].rsc", "fallback": null, "dataRouteRegex": "^/blog/([^/]+?)\\.rsc$", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/projects/[slug]": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/projects/([^/]+?)(?:/)?$", "dataRoute": "/projects/[slug].rsc", "fallback": null, "dataRouteRegex": "^/projects/([^/]+?)\\.rsc$", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "notFoundRoutes": [], "preview": {"previewModeId": "64889f1be761b84c99d55831c30ad1e8", "previewModeSigningKey": "74a4e07d5e79386003fd9724256c931839dbb2b8f83a2364b732549d5805e1ff", "previewModeEncryptionKey": "4ae8e2b666601e66375ce31a357a5e44bc58c5a95e183e78fa41525e12df4bbe"}}