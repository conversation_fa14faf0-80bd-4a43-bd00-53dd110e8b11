# Deployment Guide for nurpratapkarki.com.np

## 🎯 **Custom Configuration for Your Domain**

Your portfolio is now configured to work with:
- **Domain**: `https://nurpratapkarki.com.np`
- **Strapi Admin**: `https://nurpratapkarki.com.np/admin`
- **Frontend**: `https://nurpratapkarki.com.np`

## ✅ **What's Already Configured**

### 1. **Environment Variables Updated**
- ✅ Next.js configured to connect to `https://nurpratapkarki.com.np`
- ✅ CORS configured for your domain
- ✅ Production environment templates updated

### 2. **CORS Configuration**
Your Strapi is now configured to accept requests from:
- `http://localhost:3000` (for local development)
- `https://nurpratapkarki.com.np` (your production domain)
- `https://www.nurpratapkarki.com.np` (with www subdomain)

## 🚀 **Deployment Steps for Your Domain**

### **Option 1: Single Domain Setup (Recommended)**

Since you're using `https://nurpratapkarki.com.np/admin`, you'll deploy both Strapi and Next.js on the same domain with different paths.

#### **Step 1: Deploy Strapi**

1. **Upload Strapi files** from `cpanel-deployment/strapi/` to your domain root
2. **Configure environment** (`.env` file):
```env
HOST="0.0.0.0"
PORT=1337
ADMIN_PANEL_PATH="/admin"

# Generate secure keys for production
APP_KEYS="your_secure_key1,your_secure_key2,your_secure_key3,your_secure_key4"
API_TOKEN_SALT="your_secure_api_token_salt"
ADMIN_JWT_SECRET="your_secure_admin_jwt_secret"
TRANSFER_TOKEN_SALT="your_secure_transfer_token_salt"
JWT_SECRET="your_secure_jwt_secret"
```

3. **Install dependencies**:
```bash
npm install --production
```

4. **Start Strapi** - it will be accessible at `https://nurpratapkarki.com.np/admin`

#### **Step 2: Configure Next.js**

1. **Upload Next.js files** from `cpanel-deployment/nextjs/`
2. **Update environment** (`.env.local`):
```env
NEXT_PUBLIC_STRAPI="https://nurpratapkarki.com.np"
NEXT_PUBLIC_WEBSITE="https://nurpratapkarki.com.np"

# Your contact information (Base64 encoded)
NEXT_PUBLIC_EMAIL_ENCODED="your_base64_encoded_email"
NEXT_PUBLIC_TELEPHONE_ENCODED="your_base64_encoded_phone"

# API tokens (generate these in Strapi admin)
STRAPI_READ_ONLY_TOKEN="your_production_read_only_token"
STRAPI_FORM_TOKEN="your_production_form_token"
```

3. **Install dependencies and build**:
```bash
npm install --production
npm run build
```

### **Step 3: Generate API Tokens**

1. **Access Strapi admin**: `https://nurpratapkarki.com.np/admin`
2. **Create admin account** if first time
3. **Generate API tokens** (Settings → API Tokens):
   - **READ-ONLY-TOKEN**: Type: Read-only, All content types
   - **FORM-TOKEN**: Type: Custom, Lead → Create only
4. **Update Next.js environment** with the generated tokens

### **Step 4: Test Your Setup**

1. **✅ Test Strapi**: Visit `https://nurpratapkarki.com.np/admin`
2. **✅ Test Frontend**: Visit `https://nurpratapkarki.com.np`
3. **✅ Test API**: Check if projects and blog posts load
4. **✅ Test Contact Form**: Submit a test message

## 🔧 **Server Configuration**

### **Nginx/Apache Configuration**

You may need to configure your web server to handle both Strapi and Next.js:

#### **Nginx Example**:
```nginx
# Strapi API and Admin
location /admin {
    proxy_pass http://localhost:1337;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /api {
    proxy_pass http://localhost:1337;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /uploads {
    proxy_pass http://localhost:1337;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Next.js Frontend
location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🎯 **Quick Start Commands**

### **Local Testing with Your Domain Configuration**

```bash
# Start Strapi (from /strapi directory)
npm run develop

# Start Next.js (from /next directory) 
npm run dev
```

### **Production Deployment**

```bash
# Build both applications
npm run build:all

# Or prepare deployment files
npm run deploy:cpanel
```

## 📋 **Checklist**

- [ ] Strapi deployed and accessible at `/admin`
- [ ] Environment variables configured
- [ ] CORS configured for your domain
- [ ] API tokens generated
- [ ] Next.js configured with correct API endpoints
- [ ] Frontend loads with content from Strapi
- [ ] Contact form works
- [ ] SSL certificate configured
- [ ] All images and assets load correctly

## 🆘 **Troubleshooting**

### **Common Issues:**

1. **CORS Errors**: Verify your domain is in the CORS configuration
2. **API Connection Failed**: Check if Strapi is running and accessible
3. **404 on /admin**: Ensure Strapi is properly configured and running
4. **Images not loading**: Check if `/uploads` path is properly proxied

Your portfolio is now ready for deployment on `https://nurpratapkarki.com.np`! 🎉
