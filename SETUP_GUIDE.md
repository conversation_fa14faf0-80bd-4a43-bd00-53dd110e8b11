# Updated Next.js + Strapi Portfolio Setup Guide

This guide covers setting up the portfolio locally and deploying to cPanel with Node.js support.

## What's Updated

### Latest Versions (as of September 2024)
- **Next.js**: 15.5.3 (latest stable)
- **React**: 19.1.1 (latest stable)
- **Strapi**: 5.23.4 (latest stable)
- **Node.js**: 18.x - 24.x supported (extended compatibility)
- **ESLint**: 9.x (latest)
- **Tailwind CSS**: 3.4.17

### Key Changes Made
- ✅ Removed `--legacy-peer-deps` flag (no longer needed)
- ✅ Updated all dependencies to latest stable versions
- ✅ Extended Node.js support to version 24.x (tested with Node.js 24.5.0)
- ✅ Fixed `better-sqlite3` compatibility with Node.js 24.x
- ✅ Added update script for easy dependency management
- ✅ Created cPanel deployment scripts and configurations
- ✅ Tested local setup with demo content import

## Local Development Setup

### Prerequisites
- Node.js 18.x, 20.x, or 22.x
- npm 6.0.0 or higher

### Step 1: Install Dependencies

```bash
# Install all dependencies for both apps
npm run setup
```

### Step 2: Configure Strapi

1. Navigate to `/strapi/` directory
2. Copy `.env.example` to `.env`:
```bash
cd strapi
cp .env.example .env
```

3. The default values in `.env` are fine for development:
```env
HOST="localhost"
PORT=1337
ADMIN_PANEL_PATH="/admin"
APP_KEYS="toBeModified1,toBeModified2,toBeModified3,toBeModified4"
API_TOKEN_SALT="toBeModified"
ADMIN_JWT_SECRET="toBeModified"
TRANSFER_TOKEN_SALT="toBeModified"
JWT_SECRET="toBeModified"
```

### Step 3: Restore Strapi Configuration

```bash
# From /strapi/ directory
npm run strapi config:restore -- --file backup/config.json
```

### Step 4: Import Demo Content (Optional)

```bash
# From /strapi/ directory
npm run strapi import -- --file backup/data.tar.gz
```

### Step 5: Start Strapi

```bash
# From /strapi/ directory
npm run develop
```

### Step 6: Create API Tokens

1. Access admin at `http://localhost:1337/admin`
2. Create your first admin user
3. Generate API tokens (*Settings → API Tokens*):

| Token Name | Type | Permissions |
|------------|------|-------------|
| `READ-ONLY-TOKEN` | Read-only | All content types |
| `FORM-TOKEN` | Custom | Lead → Create only |

### Step 7: Configure Next.js

1. Navigate to `/next/` directory
2. Copy `.env.example` to `.env`:
```bash
cd next
cp .env.example .env
```

3. Update the `.env` file with your API tokens:
```env
NEXT_PUBLIC_STRAPI="http://localhost:1337"
NEXT_PUBLIC_WEBSITE="http://localhost:3000"
NEXT_PUBLIC_EMAIL_ENCODED="************************"
NEXT_PUBLIC_TELEPHONE_ENCODED="KzQ5IDE3MCAxMjM0NTY3"
STRAPI_READ_ONLY_TOKEN="your_generated_read_only_token"
STRAPI_FORM_TOKEN="your_generated_form_token"
```

### Step 8: Start Next.js

```bash
# From /next/ directory
npm run dev
```

### Quick Start (Both Apps)

```bash
# From root directory - starts both apps simultaneously
npm run dev
```

## cPanel Deployment Guide

### Prerequisites
- cPanel hosting with Node.js support
- SSH access (recommended)
- Domain/subdomain configured

### Deployment Architecture
For cPanel deployment, we'll use:
- **Strapi**: Deployed as a Node.js app on a subdomain (e.g., `api.yourdomain.com`)
- **Next.js**: Deployed as a static export or Node.js app on main domain

### Step 1: Prepare Strapi for Production

1. **Update Strapi environment variables** for production:
```env
HOST="0.0.0.0"
PORT=3001
ADMIN_PANEL_PATH="/admin"
# Generate secure keys for production
APP_KEYS="secure_key1,secure_key2,secure_key3,secure_key4"
API_TOKEN_SALT="secure_salt"
ADMIN_JWT_SECRET="secure_jwt_secret"
TRANSFER_TOKEN_SALT="secure_transfer_salt"
JWT_SECRET="secure_jwt"
```

2. **Create production build**:
```bash
cd strapi
npm run build
```

### Step 2: Deploy Strapi to cPanel

1. **Create Node.js App in cPanel**:
   - Go to "Node.js Apps" in cPanel
   - Create new app with Node.js 18.x or higher
   - Set startup file to `server.js`
   - Set application root to `/strapi`

2. **Upload Strapi files**:
   - Upload entire `/strapi` directory to your cPanel file manager
   - Ensure `node_modules` is excluded (will be installed on server)

3. **Install dependencies**:
```bash
# Via SSH or cPanel terminal
cd /path/to/your/strapi
npm install --production
```

4. **Create startup file** (`server.js` in strapi root):
```javascript
const strapi = require('@strapi/strapi');
const app = strapi({ distDir: './dist' });
app.start();
```

### Step 3: Prepare Next.js for Production

1. **Update Next.js environment variables**:
```env
NEXT_PUBLIC_STRAPI="https://api.yourdomain.com"
NEXT_PUBLIC_WEBSITE="https://yourdomain.com"
NEXT_PUBLIC_EMAIL_ENCODED="your_base64_email"
NEXT_PUBLIC_TELEPHONE_ENCODED="your_base64_phone"
STRAPI_READ_ONLY_TOKEN="production_read_only_token"
STRAPI_FORM_TOKEN="production_form_token"
```

2. **Choose deployment method**:

#### Option A: Static Export (Recommended for cPanel)
```bash
# Add to next.config.mjs
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
  // ... rest of config
};
```

#### Option B: Node.js App
Keep existing configuration for server-side rendering.

### Step 4: Deploy Next.js to cPanel

#### For Static Export:
1. Build and export:
```bash
cd next
npm run build
```

2. Upload `out` folder contents to your domain's public_html

#### For Node.js App:
1. Create another Node.js app in cPanel
2. Upload Next.js files
3. Install dependencies and start

### Step 5: Configure Domain/Subdomain

1. **Set up subdomain** for Strapi API (e.g., `api.yourdomain.com`)
2. **Point main domain** to Next.js application
3. **Update DNS** if necessary

## Maintenance & Updates

### Updating Dependencies

```bash
# Update all dependencies to latest versions
npm run update

# Then reinstall
npm run setup
```

### Backup & Restore

```bash
# Export Strapi data
cd strapi
npm run export

# Dump configuration
npm run config:dump
```

## Troubleshooting

### Common Issues

1. **Node.js version mismatch**: Ensure cPanel uses Node.js 18.x or higher
2. **Environment variables**: Double-check all environment variables are set correctly
3. **CORS issues**: Configure Strapi CORS settings for your domain
4. **Build failures**: Check Node.js memory limits in cPanel

### Performance Optimization

1. **Enable caching** in cPanel
2. **Use CDN** for static assets
3. **Optimize images** before upload
4. **Enable gzip compression**

## Security Considerations

1. **Change default secrets** in production
2. **Use HTTPS** for all communications
3. **Restrict API access** with proper tokens
4. **Regular updates** of dependencies
5. **Backup regularly**

This setup provides a modern, scalable portfolio solution with the latest stable versions of all dependencies.
