{"name": "next-dev-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.1.5", "@hookform/resolvers": "^4.1.0", "@tailwindcss/typography": "^0.5.15", "clsx": "^2.1.1", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "embla-carousel-react": "^8.3.1", "isomorphic-dompurify": "^2.16.0", "marked": "^15.0.0", "marked-highlight": "^2.2.1", "mini-svg-data-uri": "^1.4.4", "next": "15.2.2", "next-view-transitions": "^0.3.4", "prismjs": "^1.29.0", "qs": "^6.14.0", "react": "19.0.0", "react-collapse": "^5.1.1", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-share": "^5.2.2", "sharp": "^0.33.5", "tailwind-merge": "^2.5.4", "zod": "^3.24.1"}, "devDependencies": {"@types/prismjs": "^1.26.5", "eslint": "^8", "eslint-config-next": "15.2.2", "postcss": "^8", "tailwindcss": "^3.4.1"}}