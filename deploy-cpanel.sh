#!/bin/bash

# cPanel Deployment Script for Next.js + Strapi Portfolio
# This script helps prepare the applications for cPanel deployment

echo "🚀 cPanel Deployment Preparation Script"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18.x or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Create deployment directory
DEPLOY_DIR="cpanel-deployment"
if [ -d "$DEPLOY_DIR" ]; then
    print_warning "Deployment directory exists. Removing..."
    rm -rf "$DEPLOY_DIR"
fi

mkdir -p "$DEPLOY_DIR"
print_status "Created deployment directory: $DEPLOY_DIR"

echo ""
echo "📦 Preparing Strapi for cPanel..."
echo "================================"

# Prepare Strapi
mkdir -p "$DEPLOY_DIR/strapi"

# Copy Strapi files (excluding node_modules and .env)
rsync -av --exclude='node_modules' --exclude='.env' --exclude='.tmp' strapi/ "$DEPLOY_DIR/strapi/"

# Copy production environment template
cp strapi/.env.production "$DEPLOY_DIR/strapi/.env.example"

print_status "Strapi files prepared"

# Build Strapi
cd strapi
if [ ! -d "node_modules" ]; then
    print_info "Installing Strapi dependencies..."
    npm install
fi

print_info "Building Strapi..."
npm run build

# Copy built files
cp -r dist "../$DEPLOY_DIR/strapi/"
cd ..

print_status "Strapi build completed"

echo ""
echo "🌐 Preparing Next.js for cPanel..."
echo "=================================="

# Prepare Next.js
mkdir -p "$DEPLOY_DIR/nextjs"

# Copy Next.js files (excluding node_modules and .env)
rsync -av --exclude='node_modules' --exclude='.env*' --exclude='.next' next/ "$DEPLOY_DIR/nextjs/"

# Copy production environment template
cp next/.env.production "$DEPLOY_DIR/nextjs/.env.example"

print_status "Next.js files prepared"

# Ask user for deployment type
echo ""
echo "Choose Next.js deployment type:"
echo "1) Static Export (recommended for cPanel)"
echo "2) Node.js App (requires Node.js support)"
read -p "Enter your choice (1 or 2): " DEPLOY_TYPE

if [ "$DEPLOY_TYPE" = "1" ]; then
    print_info "Preparing static export..."
    
    # Update next.config.mjs for static export
    cat > "$DEPLOY_DIR/nextjs/next.config.mjs" << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  reactStrictMode: true,
  trailingSlash: true,
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
};

export default nextConfig;
EOF

    # Build Next.js for static export
    cd next
    if [ ! -d "node_modules" ]; then
        print_info "Installing Next.js dependencies..."
        npm install
    fi
    
    print_info "Building Next.js for static export..."
    npm run build
    
    # Copy static files
    if [ -d "out" ]; then
        cp -r out "../$DEPLOY_DIR/nextjs-static"
        print_status "Static export completed. Files are in $DEPLOY_DIR/nextjs-static"
    else
        print_error "Static export failed. Please check your configuration."
    fi
    cd ..
    
elif [ "$DEPLOY_TYPE" = "2" ]; then
    print_info "Preparing Node.js app..."
    
    # Build Next.js
    cd next
    if [ ! -d "node_modules" ]; then
        print_info "Installing Next.js dependencies..."
        npm install
    fi
    
    print_info "Building Next.js..."
    npm run build
    
    # Copy built files
    cp -r .next "../$DEPLOY_DIR/nextjs/"
    cd ..
    
    print_status "Next.js Node.js app prepared"
else
    print_error "Invalid choice. Exiting."
    exit 1
fi

# Create deployment instructions
cat > "$DEPLOY_DIR/DEPLOYMENT_INSTRUCTIONS.md" << 'EOF'
# cPanel Deployment Instructions

## Strapi Deployment

1. **Create Node.js App in cPanel:**
   - Go to "Node.js Apps" in cPanel
   - Create new app with Node.js 18.x or higher
   - Set startup file to `server.js`
   - Set application root to `/strapi`

2. **Upload Files:**
   - Upload the entire `strapi` folder to your cPanel file manager
   - Place it in your desired location (e.g., `/home/<USER>/strapi`)

3. **Configure Environment:**
   - Rename `.env.example` to `.env`
   - Update all the REPLACE_WITH_SECURE_* values with actual secure keys
   - Update database configuration if needed

4. **Install Dependencies:**
   ```bash
   cd /path/to/your/strapi
   npm install --production
   ```

5. **Start the App:**
   - Use cPanel Node.js Apps interface to start the application

## Next.js Deployment

### For Static Export:
1. Upload contents of `nextjs-static` folder to your domain's `public_html`
2. Configure your domain to point to the uploaded files

### For Node.js App:
1. Create another Node.js app in cPanel
2. Upload `nextjs` folder contents
3. Set startup file to `server.js` (you may need to create this)
4. Install dependencies and start

## Post-Deployment

1. **Configure Domains:**
   - Set up subdomain for Strapi (e.g., api.yourdomain.com)
   - Point main domain to Next.js

2. **Update Environment Variables:**
   - Update Next.js `.env` with actual Strapi URL
   - Generate and configure API tokens in Strapi admin

3. **Test the Setup:**
   - Access Strapi admin panel
   - Verify Next.js site loads correctly
   - Test contact form functionality

## Security Checklist

- [ ] Changed all default secrets in Strapi
- [ ] Configured HTTPS for both applications
- [ ] Set up proper CORS in Strapi
- [ ] Generated secure API tokens
- [ ] Configured firewall rules if needed

EOF

print_status "Deployment instructions created"

echo ""
echo "🎉 Deployment preparation completed!"
echo "===================================="
print_info "Files are ready in the '$DEPLOY_DIR' directory"
print_info "Please read DEPLOYMENT_INSTRUCTIONS.md for next steps"
print_warning "Remember to update environment variables with your actual values"
print_warning "Generate secure keys for production use"

echo ""
echo "📋 Next Steps:"
echo "1. Review and update environment variables"
echo "2. Upload files to your cPanel hosting"
echo "3. Configure Node.js apps in cPanel"
echo "4. Set up domains and SSL certificates"
echo "5. Test the deployment"

echo ""
print_status "Happy deploying! 🚀"
