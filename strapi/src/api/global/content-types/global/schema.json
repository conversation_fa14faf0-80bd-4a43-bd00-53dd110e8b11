{"kind": "singleType", "collectionName": "globals", "info": {"singularName": "global", "pluralName": "globals", "displayName": "Global Settings", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"header": {"type": "component", "repeatable": false, "component": "sections.header", "required": true}, "announcement": {"type": "component", "repeatable": false, "component": "blocks.announcement", "required": true}, "footer": {"type": "component", "repeatable": false, "component": "sections.footer", "required": true}, "cta": {"type": "component", "repeatable": false, "component": "sections.call-to-action", "required": true}, "miscellaneous": {"type": "component", "repeatable": false, "component": "blocks.miscellaneous", "required": true}, "siteRepresentation": {"type": "component", "repeatable": false, "component": "blocks.site-representation", "required": true}, "icons": {"type": "component", "repeatable": false, "component": "blocks.favicon-and-app-icons", "required": true}}}