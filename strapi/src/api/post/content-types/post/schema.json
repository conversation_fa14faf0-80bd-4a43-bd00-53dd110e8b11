{"kind": "collectionType", "collectionName": "posts", "info": {"singularName": "post", "pluralName": "posts", "displayName": "Post", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "unique": true, "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "excerpt": {"type": "text", "required": true}, "featuredImage": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "content": {"type": "richtext", "required": true}, "author": {"type": "relation", "relation": "oneToOne", "target": "api::author.author", "required": true}}}