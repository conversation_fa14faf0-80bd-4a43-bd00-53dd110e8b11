{"name": "strapi-dev-portfolio", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "deploy": "strapi deploy", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "export": "npm run strapi export -- --no-encrypt --file backup/data", "config:dump": "npm run strapi configuration:dump -- --file backup/config.json"}, "dependencies": {"@strapi/plugin-cloud": "5.12.7", "@strapi/plugin-users-permissions": "5.12.7", "@strapi/strapi": "5.12.7", "better-sqlite3": "^11.8.1", "koa2-ratelimit": "^1.1.3", "pg": "^8.13.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}