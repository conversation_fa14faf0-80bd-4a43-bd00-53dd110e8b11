# Production Environment Variables for Strapi
# Copy this file to .env and update with your production values

# Server Configuration
HOST="0.0.0.0"
PORT=1337

# Admin panel's path
ADMIN_PANEL_PATH="/admin"

# Database Configuration (update for your production database)
DATABASE_CLIENT="better-sqlite3"
DATABASE_FILENAME=".tmp/data.db"

# For PostgreSQL (uncomment and configure if using PostgreSQL)
# DATABASE_CLIENT="postgres"
# DATABASE_HOST="localhost"
# DATABASE_PORT=5432
# DATABASE_NAME="strapi_production"
# DATABASE_USERNAME="your_db_user"
# DATABASE_PASSWORD="your_db_password"
# DATABASE_SSL=false

# Security Keys (IMPORTANT: Generate secure values for production)
# You can generate secure keys using: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
APP_KEYS="REPLACE_WITH_SECURE_KEY1,REPLACE_WITH_SECURE_KEY2,REPLACE_WITH_SECURE_KEY3,REPLACE_WITH_SECURE_KEY4"
API_TOKEN_SALT="REPLACE_WITH_SECURE_API_TOKEN_SALT"
ADMIN_JWT_SECRET="REPLACE_WITH_SECURE_ADMIN_JWT_SECRET"
TRANSFER_TOKEN_SALT="REPLACE_WITH_SECURE_TRANSFER_TOKEN_SALT"
JWT_SECRET="REPLACE_WITH_SECURE_JWT_SECRET"

# Optional: Email Configuration (for password reset, etc.)
# EMAIL_PROVIDER="sendmail"
# EMAIL_PROVIDER_OPTIONS_HOST="smtp.gmail.com"
# EMAIL_PROVIDER_OPTIONS_PORT=587
# EMAIL_PROVIDER_OPTIONS_USERNAME="<EMAIL>"
# EMAIL_PROVIDER_OPTIONS_PASSWORD="your-app-password"

# Optional: Upload Provider (for file uploads)
# UPLOAD_PROVIDER="local"

# Optional: Cloudinary (if using Cloudinary for media)
# CLOUDINARY_NAME="your-cloudinary-name"
# CLOUDINARY_KEY="your-cloudinary-key"
# CLOUDINARY_SECRET="your-cloudinary-secret"
