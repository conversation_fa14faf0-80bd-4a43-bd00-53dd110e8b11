{"scripts": {"clear": "cd next && rm -rf .next && rm -rf cache", "setup:next": "cd next && npm install", "setup:strapi": "cd strapi && npm install", "setup": "npm install && npm run setup:strapi && npm run setup:next", "dev": "npm run clear && concurrently \"cd strapi && npm run develop\" \"cd next && npm run dev\"", "update": "npm-check-updates -u && cd next && npm-check-updates -u && cd ../strapi && npm-check-updates -u", "deploy:cpanel": "./deploy-cpanel.sh", "build:all": "cd strapi && npm run build && cd ../next && npm run build"}, "dependencies": {"concurrently": "^9.1.0", "npm-check-updates": "^17.1.11"}}